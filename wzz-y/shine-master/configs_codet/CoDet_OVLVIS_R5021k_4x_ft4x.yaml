_BASE_: "Base_OVLVIS_R5021k_4x.yaml"
MODEL:
  WITH_CAPTION: True
  SYNC_CAPTION_BATCH: True
  ROI_BOX_HEAD:
    IMAGE_LOSS_WEIGHT: 0.2
    ADD_IMAGE_BOX: True
    USE_ZEROSHOT_CLS: True
    ZEROSHOT_WEIGHT_PATH: 'datasets/metadata/cc3m_clip_a+cname.npy'
    DETECTION_WEIGHT_PATH: 'datasets/metadata/lvis_v1_clip_a+cname.npy'
    IMAGE_LABEL_LOSS: 'concept_grouping'
    ADD_FEATURE_TO_PROP: True
    USE_FED_LOSS: True
    CAT_FREQ_PATH : 'datasets/lvis/lvis_v1_train_norare_cat_info.json'
  WEIGHTS: "models/BoxSup-C2_Lbase_CLIP_R5021k_640b64_4x.pth"
SOLVER:
  MAX_ITER: 90000
  IMS_PER_BATCH: 64
  BASE_LR: 0.0002
  WARMUP_ITERS: 1000
  WARMUP_FACTOR: 0.001
DATASETS:
  TRAIN: ("lvis_v1_train_norare",)
DATALOADER:
  SAMPLER_TRAIN: "MultiDatasetConceptSampler"
  DATASET_RATIO: [1, 4]
  USE_DIFF_BS_SIZE: True
  DATASET_BS: [8, 32]
  DATASET_INPUT_SIZE: [800, 400]
  USE_RFS: [True, False]
  DATASET_INPUT_SCALE: [[0.1, 2.0], [0.5, 1.5]]
  FILTER_EMPTY_ANNOTATIONS: False
  MULTI_DATASET_GROUPING: True
  DATASET_ANN: ['box', 'captiontag']
  NUM_WORKERS: 8
  CONCEPT_GROUP_SIZE: 8
WITH_IMAGE_LABELS: True