MODEL:
  META_ARCHITECTURE: "CustomRCNN"
  MASK_ON: True
  PROPOSAL_GENERATOR:
    NAME: "CenterNet"
  WEIGHTS: "models/resnet50_miil_21k.pkl"
  BACKBONE:
    NAME: build_p67_timm_fpn_backbone
  TIMM:
    BASE_NAME: resnet50_in21k
  FPN:
    IN_FEATURES: ["layer3", "layer4", "layer5"]
  PIXEL_MEAN: [123.675, 116.280, 103.530]
  PIXEL_STD: [58.395, 57.12, 57.375]
  ROI_HEADS:
    NAME: DeticCascadeROIHeads
    IN_FEATURES: ["p3", "p4", "p5"]
    IOU_THRESHOLDS: [0.6]
    NUM_CLASSES: 1203
    SCORE_THRESH_TEST: 0.02
    NMS_THRESH_TEST: 0.5
  ROI_BOX_CASCADE_HEAD:
    IOUS: [0.6, 0.7, 0.8]
  ROI_BOX_HEAD:
    NAME: "FastRCNNConvFCHead"
    NUM_FC: 2
    POOLER_RESOLUTION: 7
    CLS_AGNOSTIC_BBOX_REG: True
    MULT_PROPOSAL_SCORE: True

    USE_SIGMOID_CE: True
    USE_FED_LOSS: True
  ROI_MASK_HEAD:
    NAME: "MaskRCNNConvUpsampleHead"
    NUM_CONV: 4
    POOLER_RESOLUTION: 14
    CLS_AGNOSTIC_MASK: True
  CENTERNET:
    NUM_CLASSES: 1203
    REG_WEIGHT: 1.
    NOT_NORM_REG: True
    ONLY_PROPOSAL: True
    WITH_AGN_HM: True
    INFERENCE_TH: 0.0001
    PRE_NMS_TOPK_TRAIN: 4000
    POST_NMS_TOPK_TRAIN: 2000
    PRE_NMS_TOPK_TEST: 1000
    POST_NMS_TOPK_TEST: 256
    NMS_TH_TRAIN: 0.9
    NMS_TH_TEST: 0.9
    POS_WEIGHT: 0.5
    NEG_WEIGHT: 0.5
    IGNORE_HIGH_FP: 0.85
DATASETS:
  TRAIN: ("lvis_v1_train",)
  TEST: ("lvis_v1_val",)
DATALOADER:
  SAMPLER_TRAIN: "RepeatFactorTrainingSampler"
  REPEAT_THRESHOLD: 0.001
  NUM_WORKERS: 8
TEST:
  DETECTIONS_PER_IMAGE: 300
SOLVER:
  LR_SCHEDULER_NAME: "WarmupCosineLR"
  CHECKPOINT_PERIOD: 1000000000
  WARMUP_ITERS: 10000
  WARMUP_FACTOR: 0.0001
  USE_CUSTOM_SOLVER: True
  OPTIMIZER: "ADAMW"
  MAX_ITER: 90000
  IMS_PER_BATCH: 64
  BASE_LR: 0.0002
  CLIP_GRADIENTS:
    ENABLED: True
INPUT:
  FORMAT: RGB
  CUSTOM_AUG: EfficientDetResizeCrop
  TRAIN_SIZE: 640
OUTPUT_DIR: "/mnt/afs/intern/huangtao3/wzz/shine/output/Detic/auto"
EVAL_PROPOSAL_AR: False
VERSION: 2
FP16: True