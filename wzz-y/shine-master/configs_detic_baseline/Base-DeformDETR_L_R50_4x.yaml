MODEL:
  META_ARCHITECTURE: "DeformableDetr"
  WEIGHTS: "detectron2://ImageNetPretrained/torchvision/R-50.pkl"
  PIXEL_MEAN: [123.675, 116.280, 103.530]
  PIXEL_STD: [58.395, 57.120, 57.375]
  MASK_ON: False
  RESNETS:
    DEPTH: 50
    STRIDE_IN_1X1: False
    OUT_FEATURES: ["res3", "res4", "res5"]
  DETR:
    CLS_WEIGHT: 2.0
    GIOU_WEIGHT: 2.0
    L1_WEIGHT: 5.0
    NUM_OBJECT_QUERIES: 300
    DIM_FEEDFORWARD: 1024
    WITH_BOX_REFINE: True
    TWO_STAGE: True
    NUM_CLASSES: 1203
    USE_FED_LOSS: True
DATASETS:
  TRAIN: ("lvis_v1_train",)
  TEST: ("lvis_v1_val",)
SOLVER:
  CHECKPOINT_PERIOD: 10000000
  USE_CUSTOM_SOLVER: True
  IMS_PER_BATCH: 32
  BASE_LR: 0.0002
  STEPS: (150000,)
  MAX_ITER: 180000
  WARMUP_FACTOR: 1.0
  WARMUP_ITERS: 10
  WEIGHT_DECAY: 0.0001
  OPTIMIZER: "ADAMW"
  BACKBONE_MULTIPLIER: 0.1
  CLIP_GRADIENTS:
    ENABLED: True
    CLIP_TYPE: "full_model"
    CLIP_VALUE: 0.01
    NORM_TYPE: 2.0
  CUSTOM_MULTIPLIER: 0.1
  CUSTOM_MULTIPLIER_NAME: ['reference_points', 'sampling_offsets']
INPUT:
  FORMAT: "RGB"
  MIN_SIZE_TRAIN: (480, 512, 544, 576, 608, 640, 672, 704, 736, 768, 800)
  CROP:
    ENABLED: True
    TYPE: "absolute_range"
    SIZE: (384, 600)
  CUSTOM_AUG: "DETR"
TEST:
  DETECTIONS_PER_IMAGE: 300
DATALOADER:
  FILTER_EMPTY_ANNOTATIONS: False
  NUM_WORKERS: 4
  SAMPLER_TRAIN: "RepeatFactorTrainingSampler"
  REPEAT_THRESHOLD: 0.001
OUTPUT_DIR: "output/Detic/auto"
VERSION: 2