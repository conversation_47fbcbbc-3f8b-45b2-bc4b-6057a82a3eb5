MODEL:
  META_ARCHITECTURE: "CustomRCNN"
  RPN:
    PRE_NMS_TOPK_TEST: 6000
    POST_NMS_TOPK_TEST: 1000
  ROI_HEADS:
    NAME: "CustomRes5ROIHeads"
  WEIGHTS: "detectron2://ImageNetPretrained/MSRA/R-50.pkl"
  RESNETS:
    DEPTH: 50
  ROI_BOX_HEAD:
    CLS_AGNOSTIC_BBOX_REG: True
    USE_SIGMOID_CE: True
    USE_ZEROSHOT_CLS: True
    ZEROSHOT_WEIGHT_PATH: '/mnt/afs/intern/huangtao3/wzz/shine-master/datasets/metadata/coco_clip_a+cname.npy'
    IGNORE_ZERO_CATS: True
    CAT_FREQ_PATH: '/mnt/afs/intern/huangtao3/wzz/shine-master/datasets/coco/zero-shot/instances_train2017_seen_2_oriorder_cat_info.json'
DATASETS:
  TRAIN: ("coco_zeroshot_train_oriorder",)
  TEST: ("coco_generalized_zeroshot_val",)
SOLVER:
  IMS_PER_BATCH: 16
  BASE_LR: 0.02
  STEPS: (60000, 80000)
  MAX_ITER: 90000
  CHECKPOINT_PERIOD: 1000000000
INPUT:
  MIN_SIZE_TRAIN: (800,)
VERSION: 2
OUTPUT_DIR: output/Detic-COCO/auto
FP16: True