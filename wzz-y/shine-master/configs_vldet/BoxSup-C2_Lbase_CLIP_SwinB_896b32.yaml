_BASE_: "Base-C2_L_R5021k_640b64_4x.yaml"
MODEL:
  ROI_BOX_HEAD:
    USE_ZEROSHOT_CLS: True
    ZEROSHOT_WEIGHT_PATH: 'datasets/cc3m/VLDet/googlecc_nouns_6250_emb.pth' 
    DETECTION_WEIGHT_PATH: 'datasets/cc3m/VLDet/lvis_1203_cls_emb.pth'
    ZEROSHOT_WEIGHT_DIM: 1024
  SHARE_PROJ_V_DIM: 1024
  SHARE_PROJ_L_DIM: 1024
  WEIGHTS: "models/swin_base_patch4_window7_224_22k.pkl"
  BACKBONE:
    NAME: build_swintransformer_fpn_backbone
  SWIN:
    SIZE: B-22k
  FPN:
    IN_FEATURES: ["swin1", "swin2", "swin3"]
SOLVER:
  MAX_ITER: 180000
  IMS_PER_BATCH: 32
  BASE_LR: 0.0001
  CHECKPOINT_PERIOD: 30000
INPUT:
  TRAIN_SIZE: 896
DATASETS:
  TRAIN: ("lvis_v1_train_norare",)

