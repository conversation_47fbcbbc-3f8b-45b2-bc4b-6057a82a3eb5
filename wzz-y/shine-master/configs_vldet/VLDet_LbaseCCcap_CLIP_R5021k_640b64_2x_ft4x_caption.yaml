_BASE_: "Base-C2_L_R5021k_640b64_4x.yaml"
MODEL:
  WITH_CAPTION: True
  SYNC_CAPTION_BATCH: False
  ROI_BOX_HEAD:
    ADD_IMAGE_BOX: True # caption loss is added to the image-box
    USE_ZEROSHOT_CLS: True
    WS_NUM_PROPS: 32
    USE_OT: 'contrastive'
    OT_LOSS_WEIGHT: 0.01
    USE_CAPTION: True
    CAPTION_WEIGHT: 1.0
    ZEROSHOT_WEIGHT_PATH: 'datasets/cc3m/VLDet/googlecc_nouns_6250_emb.pth' 
    DETECTION_WEIGHT_PATH: 'datasets/cc3m/VLDet/lvis_1203_cls_emb.pth'
    ZEROSHOT_WEIGHT_DIM: 1024
  SHARE_PROJ_V_DIM: 1024
  SHARE_PROJ_L_DIM: 1024
  WEIGHTS: "models/lvis_base.pth"
SOLVER:
  MAX_ITER: 90000
  CHECKPOINT_PERIOD: 10000
  IMS_PER_BATCH: 64
  BASE_LR: 0.0001
  WARMUP_ITERS: 1000
  WARMUP_FACTOR: 0.001
DATASETS:
  TRAIN: ("lvis_v1_train_norare",)
DATALOADER:
  SAMPLER_TRAIN: "MultiDatasetSampler"
  DATASET_RATIO: [1, 4]
  USE_DIFF_BS_SIZE: True
  DATASET_BS: [8, 16]
  DATASET_INPUT_SIZE: [640, 640]
  USE_RFS: [True, False]
  DATASET_INPUT_SCALE: [[0.1, 2.0], [0.5, 1.5]]
  FILTER_EMPTY_ANNOTATIONS: False
  MULTI_DATASET_GROUPING: True
  DATASET_ANN: ['box', 'caption']
  NUM_WORKERS: 8
WITH_IMAGE_LABELS: True
FP16: True
OUTPUT_DIR: output/test
TEST:
  EVAL_PERIOD: 10000

