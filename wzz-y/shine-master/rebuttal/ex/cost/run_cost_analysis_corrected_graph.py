#!/usr/bin/env python3
"""
修正版本的成本分析脚本
正确实现GraSecon论文中的图结构：层次边、兄弟边、跨层级边
"""

import os
import json
import numpy as np
import time
from collections import defaultdict
import argparse

def is_direct_parent_child(node_id_1, node_id_2, parent_child_map):
    """检查两个节点是否为直接父子关系"""
    if node_id_1 not in parent_child_map or node_id_2 not in parent_child_map:
        return False
    
    # 检查直接父子关系
    if parent_child_map[node_id_2].get('parent') == node_id_1:
        return True
    if parent_child_map[node_id_1].get('parent') == node_id_2:
        return True
    if node_id_2 in parent_child_map[node_id_1].get('children', []):
        return True
    if node_id_1 in parent_child_map[node_id_2].get('children', []):
        return True
    
    return False

def build_parent_child_map(gpt_results_root, dataset_name, level_names):
    """构建父子关系映射，只处理实际的类别节点"""
    parent_child_map = {}
    node_to_level = {}
    
    # 第一步：收集所有实际的类别节点
    actual_nodes = set()
    for level_name in level_names:
        json_file = os.path.join(gpt_results_root, f"cleaned_{dataset_name}_gpt_detail_hrchy_{level_name}.json")
        
        if not os.path.exists(json_file):
            print(f"警告: 文件不存在 {json_file}")
            continue
            
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        for node_id, node_data in data.items():
            node_name = node_data.get('node_name', node_id)
            actual_nodes.add(node_name)
            node_to_level[node_name] = level_name
    
    print(f"📋 收集到 {len(actual_nodes)} 个实际类别节点")
    
    # 第二步：只为实际节点建立父子关系
    for node_name in actual_nodes:
        parent_child_map[node_name] = {'parent': None, 'children': []}
    
    # 第三步：建立父子关系（只在实际节点之间）
    for level_name in level_names:
        json_file = os.path.join(gpt_results_root, f"cleaned_{dataset_name}_gpt_detail_hrchy_{level_name}.json")
        
        if not os.path.exists(json_file):
            continue
            
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        for node_id, node_data in data.items():
            node_name = node_data.get('node_name', node_id)
            
            # 处理父节点（只考虑实际存在的节点）
            if 'parent_names' in node_data and node_data['parent_names']:
                for parent_name in node_data['parent_names']:
                    if parent_name in actual_nodes:  # 只处理实际存在的父节点
                        parent_child_map[node_name]['parent'] = parent_name
                        if node_name not in parent_child_map[parent_name]['children']:
                            parent_child_map[parent_name]['children'].append(node_name)
                        break  # 只取第一个有效的父节点
            
            # 处理子节点（只考虑实际存在的节点）
            if 'child_names' in node_data and node_data['child_names']:
                for child_name in node_data['child_names']:
                    if child_name in actual_nodes:  # 只处理实际存在的子节点
                        if child_name not in parent_child_map[node_name]['children']:
                            parent_child_map[node_name]['children'].append(child_name)
                        parent_child_map[child_name]['parent'] = node_name
    
    return parent_child_map, node_to_level

def build_corrected_adjacency_matrix(all_nodes, parent_child_map, node_to_level, dataset_name):
    """构建修正的邻接矩阵，包含三种边类型"""
    num_nodes = len(all_nodes)
    node_to_index = {node_id: i for i, node_id in enumerate(all_nodes)}
    adjacency = np.zeros((num_nodes, num_nodes))
    
    print(f"🏗️  构建修正的邻接矩阵 ({num_nodes} 个节点)")
    
    # 1. 层次边
    hierarchical_edges = 0
    for node_id in parent_child_map:
        if node_id not in node_to_index:
            continue
            
        node_idx = node_to_index[node_id]
        
        # 父节点关系
        parent_id = parent_child_map[node_id].get('parent')
        if parent_id is not None and parent_id in node_to_index:
            parent_idx = node_to_index[parent_id]
            adjacency[node_idx, parent_idx] = 1
            adjacency[parent_idx, node_idx] = 1
            hierarchical_edges += 1
        
        # 子节点关系
        for child_id in parent_child_map[node_id].get('children', []):
            if child_id in node_to_index:
                child_idx = node_to_index[child_id]
                adjacency[node_idx, child_idx] = 1
                adjacency[child_idx, node_idx] = 1
                hierarchical_edges += 1
    
    # 2. 兄弟边
    sibling_edges_added = 0
    for node_id in all_nodes:
        if node_id not in parent_child_map or node_id not in node_to_index:
            continue
            
        node_idx = node_to_index[node_id]
        node_level = node_to_level.get(node_id)
        parent_id = parent_child_map[node_id].get('parent')
        
        if parent_id is None:
            continue
            
        for other_node_id in all_nodes:
            if (other_node_id == node_id or 
                other_node_id not in parent_child_map or 
                other_node_id not in node_to_index):
                continue
                
            other_node_idx = node_to_index[other_node_id]
            other_level = node_to_level.get(other_node_id)
            
            if (other_level == node_level and 
                parent_child_map[other_node_id].get('parent') == parent_id):
                
                if adjacency[node_idx, other_node_idx] == 0:
                    adjacency[node_idx, other_node_idx] = 1
                    adjacency[other_node_idx, node_idx] = 1
                    sibling_edges_added += 1
    
    # 3. 跨层级边
    cross_level_edges_added = 0
    
    # 根据数据集确定跳数限制
    if dataset_name == 'inat':
        max_level_gap = 3
    elif dataset_name == 'fsod':
        max_level_gap = 2
    else:
        max_level_gap = 1
    
    # 构建层级映射
    level_to_num = {}
    unique_levels = list(set(node_to_level.values()))
    for i, level in enumerate(sorted(unique_levels)):
        level_to_num[level] = i + 1
    
    for i, node_id_i in enumerate(all_nodes):
        if node_id_i not in node_to_level:
            continue
            
        level_i = node_to_level[node_id_i]
        level_num_i = level_to_num.get(level_i, 0)
        
        for j, node_id_j in enumerate(all_nodes):
            if (i >= j or node_id_j not in node_to_level or 
                adjacency[i, j] == 1):
                continue
                
            level_j = node_to_level[node_id_j]
            level_num_j = level_to_num.get(level_j, 0)
            level_gap = abs(level_num_i - level_num_j)
            
            if (level_i != level_j and 
                1 < level_gap <= max_level_gap and
                not is_direct_parent_child(node_id_i, node_id_j, parent_child_map)):
                
                adjacency[i, j] = 1
                adjacency[j, i] = 1
                cross_level_edges_added += 1
    
    # 添加自连接
    np.fill_diagonal(adjacency, 1)
    
    # 计算统计信息
    total_possible_edges = num_nodes * (num_nodes - 1) // 2
    actual_edges = np.sum(adjacency) // 2 - num_nodes
    sparsity = 1.0 - (actual_edges / total_possible_edges)
    avg_degree = np.sum(adjacency) / num_nodes - 1
    
    print(f"📊 图统计: {actual_edges} 边 (层次:{hierarchical_edges}, 兄弟:{sibling_edges_added}, 跨层级:{cross_level_edges_added})")
    print(f"   稀疏度: {sparsity:.4f} ({sparsity*100:.2f}%), 平均度数: {avg_degree:.2f}")
    
    return adjacency, {
        'num_nodes': num_nodes,
        'hierarchical_edges': hierarchical_edges,
        'sibling_edges': sibling_edges_added,
        'cross_level_edges': cross_level_edges_added,
        'total_edges': actual_edges,
        'sparsity': sparsity,
        'avg_degree': avg_degree
    }

def simulate_slp_propagation(adjacency, num_steps=5, alpha=0.7):
    """模拟SLP传播"""
    num_nodes = adjacency.shape[0]
    
    # 计算归一化拉普拉斯矩阵
    degree_matrix = np.diag(np.sum(adjacency, axis=1))
    degree_sqrt_inv = np.diag(1.0 / np.sqrt(np.diag(degree_matrix) + 1e-10))
    normalized_adjacency = degree_sqrt_inv @ adjacency @ degree_sqrt_inv
    
    # 初始化特征
    feature_dim = 512
    features = np.random.randn(num_nodes, feature_dim)
    
    # 记录传播时间
    start_time = time.time()
    for step in range(num_steps):
        features = (1 - alpha) * features + alpha * normalized_adjacency @ features
    propagation_time = time.time() - start_time
    
    return propagation_time

def build_single_level_semantic_adjacency_matrix(num_nodes, dataset_name):
    """
    为单层级数据集构建基于语义相似度的邻接矩阵
    
    对于单层级数据集（COCO、LVIS、ImageNet），虽然没有层次结构，
    但可以基于语义相似度建立类别间的连接，实现有意义的图传播。
    
    Args:
        num_nodes: 节点数量
        dataset_name: 数据集名称
    
    Returns:
        adjacency: 邻接矩阵
        graph_stats: 图统计信息
    """
    print(f"🏗️  构建单层级语义邻接矩阵 ({num_nodes} 个节点)")
    
    # 创建邻接矩阵
    adjacency = np.eye(num_nodes)  # 初始化为单位矩阵（自连接）
    
    # 为单层级数据集模拟语义连接
    # 基于经验：每个节点平均连接到k个最相似的节点
    if dataset_name == 'coco':
        # COCO: 80个类别，相对密集的连接
        k_neighbors = min(8, num_nodes - 1)  # 每个节点连接8个最相似节点
    elif dataset_name == 'lvis':
        # LVIS: 1203个类别，稀疏连接
        k_neighbors = min(12, num_nodes - 1)  # 每个节点连接12个最相似节点
    elif 'imagenet' in dataset_name:
        # ImageNet: 大量类别，中等稀疏连接
        k_neighbors = min(15, num_nodes - 1)  # 每个节点连接15个最相似节点
    else:
        k_neighbors = min(10, num_nodes - 1)  # 默认连接10个节点
    
    # 模拟语义相似度连接
    # 为每个节点随机选择k个邻居（模拟基于语义相似度的连接）
    np.random.seed(42)  # 固定随机种子确保结果可重现
    
    semantic_edges = 0
    for i in range(num_nodes):
        # 为节点i选择k个最相似的邻居
        possible_neighbors = list(range(num_nodes))
        possible_neighbors.remove(i)  # 排除自己
        
        # 随机选择k个邻居（在实际应用中，这会基于CLIP特征相似度）
        selected_neighbors = np.random.choice(possible_neighbors, 
                                            size=min(k_neighbors, len(possible_neighbors)), 
                                            replace=False)
        
        for j in selected_neighbors:
            if adjacency[i, j] == 0:  # 避免重复连接
                adjacency[i, j] = 1
                adjacency[j, i] = 1  # 无向图
                semantic_edges += 1
    
    # 计算统计信息
    total_possible_edges = num_nodes * (num_nodes - 1) // 2
    actual_edges = np.sum(adjacency) // 2 - num_nodes  # 排除自连接
    sparsity = 1.0 - (actual_edges / total_possible_edges)
    avg_degree = np.sum(adjacency) / num_nodes - 1  # 排除自连接
    
    print(f"📊 图统计: {actual_edges} 边 (语义连接:{semantic_edges})")
    print(f"   稀疏度: {sparsity:.4f} ({sparsity*100:.2f}%), 平均度数: {avg_degree:.2f}")
    print(f"   注意: 单层级数据集基于语义相似度建立连接，支持有效的图传播")
    
    return adjacency, {
        'num_nodes': num_nodes,
        'hierarchical_edges': 0,
        'sibling_edges': 0,
        'cross_level_edges': 0,
        'semantic_edges': semantic_edges,
        'total_edges': actual_edges,
        'sparsity': sparsity,
        'avg_degree': avg_degree
    }

def analyze_per_category_graph_cost(dataset_name, data_root):
    """
    分析每个类别独立构建图的成本（正确的理解）
    
    对于每个类别，我们构建一个包含该类别及其父子节点的小图，
    然后计算图传播的成本。总成本 = 单个类别成本 × 类别数量
    """
    print(f"\n{'='*50}")
    print(f"分析数据集: {dataset_name.upper()} (每类别独立图)")
    print(f"{'='*50}")
    
    if dataset_name == 'inat':
        level_names = ['l1', 'l2', 'l3', 'l4', 'l5', 'l6']
        gpt_results_root = os.path.join(data_root, 'shine/inat_llm_answers')
        
        # 构建层次结构
        parent_child_map, node_to_level = build_parent_child_map(gpt_results_root, dataset_name, level_names)
        all_nodes = list(parent_child_map.keys())
        
        # 分析每个类别的图结构
        category_graph_stats = analyze_category_graphs(all_nodes, parent_child_map, node_to_level)
        
        # 分析层次完整性（回答审稿人的核心问题）
        hierarchy_completeness = analyze_hierarchy_completeness(all_nodes, parent_child_map, node_to_level, dataset_name)
        category_graph_stats['hierarchy_completeness'] = hierarchy_completeness
        
    elif dataset_name == 'fsod':
        level_names = ['l1', 'l2', 'l3']
        gpt_results_root = os.path.join(data_root, 'shine/fsod_llm_detail_answers')
        
        # 构建层次结构
        parent_child_map, node_to_level = build_parent_child_map(gpt_results_root, dataset_name, level_names)
        all_nodes = list(parent_child_map.keys())
        
        # 分析每个类别的图结构
        category_graph_stats = analyze_category_graphs(all_nodes, parent_child_map, node_to_level)
        
        # 分析层次完整性
        hierarchy_completeness = analyze_hierarchy_completeness(all_nodes, parent_child_map, node_to_level, dataset_name)
        category_graph_stats['hierarchy_completeness'] = hierarchy_completeness
        
    elif dataset_name in ['coco', 'lvis', 'imagenet1k', 'imagenet21k']:
        # 单层级数据集：每个类别只有自己，无父子关系
        if dataset_name == 'coco':
            num_categories = 80
        elif dataset_name == 'lvis':
            num_categories = 1203
        elif dataset_name == 'imagenet1k':
            num_categories = 1000
        elif dataset_name == 'imagenet21k':
            num_categories = 21841
            
        category_graph_stats = analyze_single_level_category_graphs(num_categories, dataset_name)
        
    else:
        raise ValueError(f"不支持的数据集: {dataset_name}")
    
    return category_graph_stats

def analyze_category_graphs(all_nodes, parent_child_map, node_to_level):
    """
    分析每个类别的图结构统计
    """
    if len(all_nodes) == 0:
        print("⚠️  没有找到任何节点，返回空统计")
        return {
            'num_categories': 0,
            'avg_graph_size': 0,
            'max_graph_size': 0,
            'min_graph_size': 0,
            'complete_paths': 0,
            'incomplete_paths': 0,
            'isolated_nodes': 0,
            'completeness_rate': 0.0,
            'sparsity_rate': 0.0,
            'single_category_time': 0.0,
            'total_propagation_time': 0.0
        }
    
    print(f"🔍 分析 {len(all_nodes)} 个类别的独立图结构...")
    
    graph_sizes = []
    complete_paths = 0
    incomplete_paths = 0
    isolated_nodes = 0
    
    for node_id in all_nodes:
        # 为每个类别构建其局部图（包含父子节点）
        local_graph_nodes = set([node_id])
        
        # 添加父节点
        parent_id = parent_child_map[node_id].get('parent')
        if parent_id:
            local_graph_nodes.add(parent_id)
        
        # 添加子节点
        for child_id in parent_child_map[node_id].get('children', []):
            local_graph_nodes.add(child_id)
        
        graph_size = len(local_graph_nodes)
        graph_sizes.append(graph_size)
        
        # 分析路径完整性
        if graph_size == 1:
            isolated_nodes += 1
        elif graph_size >= 3:  # 至少有父或子节点
            complete_paths += 1
        else:
            incomplete_paths += 1
    
    # 计算统计信息
    avg_graph_size = float(np.mean(graph_sizes))
    max_graph_size = int(np.max(graph_sizes))
    min_graph_size = int(np.min(graph_sizes))
    
    # 模拟单个类别图的传播时间
    single_category_time = simulate_single_category_propagation(avg_graph_size)
    total_propagation_time = single_category_time * len(all_nodes)
    
    print(f"📊 每类别图统计:")
    print(f"   平均图大小: {avg_graph_size:.2f} 节点")
    print(f"   图大小范围: {min_graph_size} - {max_graph_size} 节点")
    print(f"   完整路径类别: {complete_paths} ({complete_paths/len(all_nodes)*100:.1f}%)")
    print(f"   不完整路径类别: {incomplete_paths} ({incomplete_paths/len(all_nodes)*100:.1f}%)")
    print(f"   孤立节点类别: {isolated_nodes} ({isolated_nodes/len(all_nodes)*100:.1f}%)")
    print(f"   单类别传播时间: {single_category_time:.6f} 秒")
    print(f"   总传播时间: {total_propagation_time:.6f} 秒")
    
    return {
        'num_categories': len(all_nodes),
        'avg_graph_size': avg_graph_size,
        'max_graph_size': max_graph_size,
        'min_graph_size': min_graph_size,
        'complete_paths': complete_paths,
        'incomplete_paths': incomplete_paths,
        'isolated_nodes': isolated_nodes,
        'completeness_rate': complete_paths / len(all_nodes),
        'sparsity_rate': isolated_nodes / len(all_nodes),
        'single_category_time': single_category_time,
        'total_propagation_time': total_propagation_time
    }

def analyze_single_level_category_graphs(num_categories, dataset_name):
    """
    分析单层级数据集的每类别图结构
    """
    print(f"🔍 分析 {num_categories} 个单层级类别...")
    
    # 单层级数据集：每个类别只有自己，图大小为1
    avg_graph_size = 1.0
    max_graph_size = 1
    min_graph_size = 1
    
    # 所有类别都是孤立的（无层次结构）
    complete_paths = 0
    incomplete_paths = 0
    isolated_nodes = num_categories
    
    # 单个类别的传播时间（只有自己，无传播）
    single_category_time = 1e-6  # 微秒级别
    total_propagation_time = single_category_time * num_categories
    
    print(f"📊 每类别图统计:")
    print(f"   平均图大小: {avg_graph_size:.2f} 节点")
    print(f"   图大小范围: {min_graph_size} - {max_graph_size} 节点")
    print(f"   完整路径类别: {complete_paths} ({0:.1f}%)")
    print(f"   不完整路径类别: {incomplete_paths} ({0:.1f}%)")
    print(f"   孤立节点类别: {isolated_nodes} ({100:.1f}%)")
    print(f"   单类别传播时间: {single_category_time:.6f} 秒")
    print(f"   总传播时间: {total_propagation_time:.6f} 秒")
    print(f"   注意: 单层级数据集无层次结构，每个类别独立")
    
    return {
        'num_categories': num_categories,
        'avg_graph_size': avg_graph_size,
        'max_graph_size': max_graph_size,
        'min_graph_size': min_graph_size,
        'complete_paths': complete_paths,
        'incomplete_paths': incomplete_paths,
        'isolated_nodes': isolated_nodes,
        'completeness_rate': 0.0,
        'sparsity_rate': 1.0,  # 100% 稀疏（所有类别都孤立）
        'single_category_time': single_category_time,
        'total_propagation_time': total_propagation_time
    }

def simulate_single_category_propagation(graph_size):
    """
    模拟单个类别图的传播时间
    """
    if graph_size <= 1:
        return 1e-6  # 无传播需要
    
    # 确保graph_size是整数
    graph_size = int(graph_size)
    
    # 创建小图的邻接矩阵
    adjacency = np.eye(graph_size)
    
    # 添加连接（父子关系）
    if graph_size > 1:
        # 简单的链式连接（父->当前->子）
        for i in range(graph_size - 1):
            adjacency[i, i + 1] = 1
            adjacency[i + 1, i] = 1
    
    # 模拟传播
    start_time = time.time()
    
    # 简化的传播计算
    feature_dim = 512
    features = np.random.randn(graph_size, feature_dim)
    
    # 归一化拉普拉斯
    degree_matrix = np.diag(np.sum(adjacency, axis=1))
    degree_sqrt_inv = np.diag(1.0 / np.sqrt(np.diag(degree_matrix) + 1e-10))
    normalized_adjacency = degree_sqrt_inv @ adjacency @ degree_sqrt_inv
    
    # 传播步骤
    for _ in range(3):  # 3步传播
        features = 0.3 * features + 0.7 * normalized_adjacency @ features
    
    propagation_time = time.time() - start_time
    return propagation_time

def analyze_hierarchy_completeness(all_nodes, parent_child_map, node_to_level, dataset_name):
    """
    分析每个类别是否有完整的层次路径
    
    对于iNat: 检查每个L6类别是否有L6→L5→L4→L3→L2→L1的完整路径
    对于FSOD: 检查每个L3类别是否有L3→L2→L1的完整路径
    """
    print(f"\n🔍 分析{dataset_name.upper()}数据集的层次完整性...")
    
    if dataset_name == 'inat':
        target_level = 'l6'  # 分析L6类别（物种）
        expected_path_length = 6  # L6→L5→L4→L3→L2→L1
        level_order = ['l6', 'l5', 'l4', 'l3', 'l2', 'l1']
    elif dataset_name == 'fsod':
        target_level = 'l3'  # 分析L3类别
        expected_path_length = 3  # L3→L2→L1
        level_order = ['l3', 'l2', 'l1']
    else:
        print(f"⚠️  {dataset_name}是单层级数据集，无层次结构")
        return {
            'total_target_categories': 0,
            'complete_path_categories': 0,
            'incomplete_path_categories': 0,
            'completeness_rate': 0.0,
            'missing_levels_stats': {}
        }
    
    # 找到目标层级的所有类别
    target_categories = [node_id for node_id in all_nodes if node_to_level.get(node_id) == target_level]
    
    complete_paths = 0
    incomplete_paths = 0
    missing_levels_stats = {}
    
    print(f"📊 分析{len(target_categories)}个{target_level.upper()}类别的层次完整性...")
    
    for node_id in target_categories:
        # 追踪从当前节点到根节点的路径
        path = [node_id]
        current_node = node_id
        
        # 向上追踪父节点
        while current_node and len(path) < expected_path_length:
            parent_id = parent_child_map[current_node].get('parent')
            if parent_id and parent_id in all_nodes:
                path.append(parent_id)
                current_node = parent_id
            else:
                break
        
        # 检查路径完整性
        if len(path) == expected_path_length:
            complete_paths += 1
        else:
            incomplete_paths += 1
            missing_count = expected_path_length - len(path)
            if missing_count not in missing_levels_stats:
                missing_levels_stats[missing_count] = 0
            missing_levels_stats[missing_count] += 1
    
    completeness_rate = complete_paths / len(target_categories) if target_categories else 0
    
    print(f"📈 层次完整性统计:")
    print(f"   目标类别总数: {len(target_categories)}")
    print(f"   完整路径类别: {complete_paths} ({completeness_rate*100:.1f}%)")
    print(f"   不完整路径类别: {incomplete_paths} ({(1-completeness_rate)*100:.1f}%)")
    
    if missing_levels_stats:
        print(f"   缺失层级分布:")
        for missing_count, count in sorted(missing_levels_stats.items()):
            print(f"     缺失{missing_count}级: {count}个类别")
    
    return {
        'total_target_categories': len(target_categories),
        'complete_path_categories': complete_paths,
        'incomplete_path_categories': incomplete_paths,
        'completeness_rate': completeness_rate,
        'missing_levels_stats': missing_levels_stats,
        'expected_path_length': expected_path_length,
        'target_level': target_level
    }

def main():
    parser = argparse.ArgumentParser(description='修正版本的GraSecon成本分析')
    parser.add_argument('--data_root', type=str, default='/mnt/afs/intern/fangwenhan/wzz-y/shine-master')
    parser.add_argument('--datasets', nargs='+', default=['inat', 'fsod'])
    parser.add_argument('--output', type=str, default='rebuttal/ex/cost/corrected_cost_analysis.json')
    
    args = parser.parse_args()
    
    print("🚀 开始修正版本的GraSecon成本分析")
    
    all_reports = {}
    for dataset in args.datasets:
        try:
            report = analyze_per_category_graph_cost(dataset, args.data_root)
            all_reports[dataset] = report
        except Exception as e:
            print(f"❌ 分析 {dataset} 数据集时出错: {e}")
            continue
    
    # 保存报告
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    with open(args.output, 'w', encoding='utf-8') as f:
        json.dump(all_reports, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ 分析完成！报告已保存到: {args.output}")

if __name__ == "__main__":
    main() 