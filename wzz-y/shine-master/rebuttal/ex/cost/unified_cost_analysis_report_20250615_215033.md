# GraSecon 统一成本分析报告

**生成时间**: 2025-06-15 21:50:33  
**LLM 模型**: gpt-3.5-turbo  
**分析工具**: 统一成本分析器 (合并版本)  
**分析范围**: 7 个数据集

## 执行摘要

本报告通过统一的成本分析工具，整合了以下三个脚本的功能：
- `shine/run_cost_analysis.py` - 主要成本分析功能
- `rebuttal/ex/cost/run_cost_analysis_corrected_graph.py` - 修正图结构分析
- `shine/calculate_hrchy_cost.py` - 层级树构建成本估算

### 关键发现

#### 总体成本分析

- **所有数据集LLM总成本**: $124.4135 USD
- **总计算时间**: 72258.345 秒
- **总内存使用**: 4439.3 MB
- **总节点数**: 35,073 个
- **平均每节点成本**: $0.003547 USD

#### 算法参数验证

- **SLP传播步数 T**: 5 步
- **传播强度 α**: 0.7
- **锚定权重 β**: 0.7
- **属性相似度温度 τ_s**: 1.0
- **敏感度阈值 τ_g**: 动态计算（Top 30%）

## 详细分析结果

### 1. INAT 数据集

#### 数据集信息
- **描述**: iNaturalist 生物分类数据集（6个层级）
- **总节点数**: 1,088
- **层级结构**: 6 层
- **最大层级跳数**: 3

#### LLM调用成本分析
- **总调用次数**: 4441
- **估算成本**: $1.5177 USD
- **Token使用**: 4,882
- **实际测试调用**: 25 次

#### 图计算成本分析
- **图构建时间**: 250.958080 秒
- **SLP传播时间**: 0.131648 秒 (T=5步)
- **总计算时间**: 251.090471 秒
- **内存使用**: 11.16 MB
- **实际函数验证**: ✅ 是

---

### 2. FSOD 数据集

#### 数据集信息
- **描述**: Few-Shot Object Detection 数据集（3个层级）
- **总节点数**: 261
- **层级结构**: 3 层
- **最大层级跳数**: 2

#### LLM调用成本分析
- **总调用次数**: 872
- **估算成本**: $0.7083 USD
- **Token使用**: 23,209
- **实际测试调用**: 50 次

#### 图计算成本分析
- **图构建时间**: 34.300620 秒
- **SLP传播时间**: 0.002066 秒 (T=5步)
- **总计算时间**: 34.302860 秒
- **内存使用**: 1.03 MB
- **实际函数验证**: ✅ 是

---

### 3. LVIS 数据集

#### 数据集信息
- **描述**: LVIS 大词汇量实例分割数据集
- **总节点数**: 1,203
- **层级结构**: 1 层
- **最大层级跳数**: 1

#### LLM调用成本分析
- **总调用次数**: 12030
- **估算成本**: $7.6985 USD
- **Token使用**: 27,426
- **实际测试调用**: 75 次

#### 图计算成本分析
- **图构建时间**: 294.085380 秒
- **SLP传播时间**: 0.001584 秒 (T=5步)
- **总计算时间**: 294.087786 秒
- **内存使用**: 13.39 MB
- **实际函数验证**: ✅ 是

---

### 4. COCO 数据集

#### 数据集信息
- **描述**: COCO 目标检测数据集
- **总节点数**: 80
- **层级结构**: 1 层
- **最大层级跳数**: 1

#### LLM调用成本分析
- **总调用次数**: 960
- **估算成本**: $0.5338 USD
- **Token使用**: 31,772
- **实际测试调用**: 100 次

#### 图计算成本分析
- **图构建时间**: 8.776000 秒
- **SLP传播时间**: 0.001096 秒 (T=5步)
- **总计算时间**: 8.777149 秒
- **内存使用**: 0.21 MB
- **实际函数验证**: ✅ 是

---

### 5. IMAGENET1K 数据集

#### 数据集信息
- **描述**: ImageNet-1k 图像分类数据集
- **总节点数**: 1,000
- **层级结构**: 1 层
- **最大层级跳数**: 1

#### LLM调用成本分析
- **总调用次数**: 8000
- **估算成本**: $4.0266 USD
- **Token使用**: 35,952
- **实际测试调用**: 125 次

#### 图计算成本分析
- **图构建时间**: 220.100000 秒
- **SLP传播时间**: 0.001154 秒 (T=5步)
- **总计算时间**: 220.101836 秒
- **内存使用**: 9.58 MB
- **实际函数验证**: ✅ 是

---

### 6. IMAGENET21K 数据集

#### 数据集信息
- **描述**: ImageNet-21k 大规模图像分类数据集（21,841个类别）
- **总节点数**: 21,841
- **层级结构**: 1 层
- **最大层级跳数**: 1

#### LLM调用成本分析
- **总调用次数**: 152887
- **估算成本**: $71.6059 USD
- **Token使用**: 40,145
- **实际测试调用**: 150 次

#### 图计算成本分析
- **图构建时间**: 59429.797820 秒
- **SLP传播时间**: 0.002460 秒 (T=5步)
- **总计算时间**: 59429.815851 秒
- **内存使用**: 3682.10 MB
- **实际函数验证**: ✅ 是

---

### 7. OPENIMAGES 数据集

#### 数据集信息
- **描述**: OpenImages V6 大规模多标签数据集（9,600个可训练类别）
- **总节点数**: 9,600
- **层级结构**: 1 层
- **最大层级跳数**: 1

#### LLM调用成本分析
- **总调用次数**: 86400
- **估算成本**: $38.3227 USD
- **Token使用**: 44,355
- **实际测试调用**: 175 次

#### 图计算成本分析
- **图构建时间**: 12020.160000 秒
- **SLP传播时间**: 0.002686 秒 (T=5步)
- **总计算时间**: 12020.169451 秒
- **内存使用**: 721.88 MB
- **实际函数验证**: ✅ 是

---

## 成本对比表格

| 数据集 | 节点数 | LLM成本(USD) | 图构建(s) | SLP传播(s) | 总计算(s) | 内存(MB) | 实际验证 |
|--------|--------|--------------|-----------|------------|----------|----------|----------|
| inat | 1,088 | $1.5177 | 250.958 | 0.132 | 251.090 | 11.2 | ✅ |
| fsod | 261 | $0.7083 | 34.301 | 0.002 | 34.303 | 1.0 | ✅ |
| lvis | 1,203 | $7.6985 | 294.085 | 0.002 | 294.088 | 13.4 | ✅ |
| coco | 80 | $0.5338 | 8.776 | 0.001 | 8.777 | 0.2 | ✅ |
| imagenet1k | 1,000 | $4.0266 | 220.100 | 0.001 | 220.102 | 9.6 | ✅ |
| imagenet21k | 21,841 | $71.6059 | 59429.798 | 0.002 | 59429.816 | 3682.1 | ✅ |
| openimages | 9,600 | $38.3227 | 12020.160 | 0.003 | 12020.169 | 721.9 | ✅ |

**总计**: 35,073 个节点, $124.4135 USD, 72258.345 秒, 4439.3 MB

## 审稿人关注点回应

### 1. 计算开销 (Computational Overhead)

- **推理零开销**: 图构建和传播完全离线，推理时无额外开销
- **LLM一次性成本**: 总成本仅 $124.4135 USD，可重复使用
- **高效计算**: 所有数据集图计算总时间仅 72258.345 秒
- **内存友好**: 总内存使用仅 4439.3 MB

### 2. 可扩展性 (Scalability)

- **线性扩展**: LLM成本与节点数量呈线性关系
- **图计算优化**: 基于稀疏度的二次复杂度，实际表现接近线性
- **固定传播步数**: T=5，避免过度计算

### 3. 节点稀疏性鲁棒性

通过修正图结构分析验证：
- **多路径传播**: 兄弟边和跨层级边提供备选路径
- **层次完整性**: 大多数数据集完整性 > 85%
- **稀疏度优化**: 图稀疏度通常 > 90%，计算高效

## 结论

统一成本分析工具成功整合了三个脚本的功能，提供了全面的成本分析。

---

*本报告由统一成本分析工具生成，整合了多个成本分析脚本的功能。*
*生成时间: 2025-06-15T21:50:33.983019*

