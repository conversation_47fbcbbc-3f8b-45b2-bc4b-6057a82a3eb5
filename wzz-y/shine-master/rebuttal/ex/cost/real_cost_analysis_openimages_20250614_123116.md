# 真实成本分析报告 - OPENIMAGES

## 分析概览
- **数据集**: openimages
- **测试样本数**: 5
- **总节点数**: 9600
- **分析时间**: 2025-06-14T12:31:16.034261
- **模型**: gpt-3.5-turbo

## 1. LLM调用成本分析

### 实际测试结果
- **实际调用次数**: 10
- **总消耗tokens**: 1,277
- **实际成本**: $0.0022 USD
- **理论每节点调用数**: 2
- **实际每节点调用数**: 2.0
- **平均每调用tokens**: 128

### 完整数据集估算
- **预估总调用次数**: 19200
- **预估总成本**: $4.2907 USD
- **成本计算基础**: GPT-3.5-turbo ($0.00175/1K tokens平均)

## 2. 图计算成本分析

### 图构建与传播
- **节点数**: 9600
- **LLM属性生成时间**: 5.433397 GPU小时 (19560.230秒)
- **纯图构建时间**: 0.001238 GPU小时 (4.457秒)
- **总图构建时间**: 5.434635 GPU小时 (LLM + 图结构)
- **SLP传播时间**: 0.000257 GPU小时 (0.927秒, T=5步)
- **总计算时间**: 5.434893 GPU小时 (19565.614秒)
- **内存使用**: 18.75 MB
- **实际函数调用**: ✅

### 敏感度分析
- **τ_g阈值(Top30%)**: 0.0020
- **高敏感度节点**: 2880 个 (30%)

## 3. 算法参数 (与论文一致)
- **SLP传播步数 T**: 5
- **传播强度 α**: 0.7
- **锚定权重 β**: 0.7  
- **属性相似度温度 τ_s**: 1.0

## 4. 调用分布
OPENIMAGES数据集：每个节点需要2次LLM调用

## 5. 响应数据
- **响应详情保存位置**: rebuttal/ex/cost/llm_responses_20250614_123057.json
- **包含**: 所有prompt、response、token使用情况

## 6. 数据质量验证
可通过响应文件检查LLM生成的层级关系和属性是否合理。

## 7. 成本效益分析
- **离线LLM成本**: $4.2907 USD (一次性)
- **在线计算时间**: 19565.614 秒 (每次推理)
- **内存占用**: 18.75 MB
- **可扩展性**: 线性扩展，成本与节点数量成正比

