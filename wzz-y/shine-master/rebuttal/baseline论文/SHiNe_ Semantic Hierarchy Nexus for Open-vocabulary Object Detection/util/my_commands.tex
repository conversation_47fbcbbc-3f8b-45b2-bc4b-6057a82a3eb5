%%%%%%%%% COLOR SCHEMES
\definecolor{me_node}{RGB}{16,119,51}
\definecolor{sup_node}{RGB}{0, 90, 181}
\definecolor{sub_node}{RGB}{212, 17, 89}

\definecolor{higher}{RGB}{16,119,51}
\definecolor{lower}{RGB}{220,50,32}

\definecolor{ours_meth}{RGB}{16,119,51}
\definecolor{bl_meth}{RGB}{115,144,167}

\definecolor{firstBest}{rgb}{0.9, 1, 0.9}
\definecolor{secondBest}{rgb}{1, 0.95, 0.95}

\newcommand{\tablestyle}[2]{\setlength{\tabcolsep}{#1}\renewcommand{\arraystretch}{#2}\centering\footnotesize}


%%%%%%%%% SETTING
\newcommand{\hierarchylong}{hierarchy\xspace}
\newcommand{\hierarchy}{H\xspace}


\newcommand{\ovodtitle}{Open-vocabulary Object Detection\xspace}
\newcommand{\ovodlong}{open-vocabulary object detection\xspace}
\newcommand{\ovod}{OvOD\xspace}
\newcommand{\shine}{SHiNe\xspace}
\newcommand{\zsc}{ZSC\xspace}

\newcommand{\nlptitle}{Natural Language Processing\xspace}
\newcommand{\nlplong}{natural language processing\xspace}
\newcommand{\nlp}{NLP\xspace}

\newcommand{\predbasedWSOD}{prediction-based WSOD\xspace}
\newcommand{\zeroshot}{open-vocabulary\xspace}
\newcommand{\Zeroshot}{Open-vocabulary\xspace}
\newcommand{\od}{object detection\xspace}
\newcommand{\Od}{Object detection\xspace}
\newcommand{\OD}{Object Detection\xspace}
\newcommand{\hovod}{hierarchical open-vocabulary object detection\xspace}
\newcommand{\Hovod}{Hierarchical open-vocabulary object detection\xspace}
\newcommand{\HOvOD}{Hierarchical Open-vocabulary Object Detection\xspace}
\newcommand{\ov}{open-vocabulary\xspace}
\newcommand{\Ov}{Open-vocabulary\xspace}


\newcommand{\lca}{lowest common ancestor\xspace}
\newcommand{\LCA}{Lowest Common Ancestor\xspace}

%%%%%%%%% DATASET
\newcommand{\imnet}{IN-21K\xspace}
\newcommand{\imnetFull}{ImageNet-21K\xspace}
\newcommand{\lvis}{LVIS\xspace}
\newcommand{\imnetLvis}{IN-L\xspace}
\newcommand{\lvisall}{LVIS-all\xspace}
\newcommand{\lvisbase}{LVIS-base\xspace}
\newcommand{\lvisnovel}{LVIS-novel\xspace}
\newcommand{\lvisrare}{LVIS-rare\xspace}
\newcommand{\coco}{COCO\xspace}
\newcommand{\cocobase}{COCO-base\xspace}
\newcommand{\coconovel}{COCO-novel\xspace}
\newcommand{\cocoZs}{COCO-ZS\xspace}
\newcommand{\ccFull}{Conceptual Captions\xspace}
\newcommand{\cc}{CC\xspace}

\newcommand{\captionSpace}{-3mm}

\newcommand{\oid}{OpenImages\xspace}
\newcommand{\oidFull}{Open Images Dataset\xspace}

\newcommand{\inattitle}{iNaturalist Localization 500\xspace}
\newcommand{\inatlong}{iNaturalist Localization 500\xspace}
\newcommand{\inat}{iNatLoc\xspace}

\newcommand{\fsodtitle}{Few-shot Object Detection dataset\xspace}
\newcommand{\fsodlong}{few-short object detection dataset\xspace}
\newcommand{\fsod}{FSOD\xspace}

\newcommand{\vastdet}{V3Det\xspace}
\newcommand{\vastdetFull}{Vast Vocabulary Visual Detection Dataset\xspace}
\newcommand{\objectsset}{Objects365\xspace}

%%%%%%%%% MODELS
\newcommand{\vlmtitle}{Vision-Language Model\xspace}
\newcommand{\vlmlong}{vision-language model\xspace}
\newcommand{\vlm}{VLM\xspace}

\newcommand{\llmtitle}{Large Language Model\xspace}
\newcommand{\llmlong}{large language model\xspace}
\newcommand{\llm}{LLM\xspace}

\newcommand{\chils}{CHiLS\xspace}
\newcommand{\hclip}{H-CLIP\xspace}

\newcommand{\swin}{Swin\xspace}
\newcommand{\swinB}{Swin-B\xspace}
\newcommand{\swinL}{Swin-L\xspace}
\newcommand{\vit}{ViT\xspace}
\newcommand{\vitB}{ViT-B\xspace}

%%%%%%%%% NOTATION
\newcommand{\bNexus}{\mathbf{N}}
\newcommand{\bnexus}{\mathbf{n}}

\newcommand{\hier}{\mathcal{H}}
\newcommand{\sent}{e}
\newcommand{\sentset}{\mathbf{e}}

% \newcommand{\encimg}{\mathcal{E}_{\text{img}}^{\text{vlm}}}
% \newcommand{\enctxt}{\mathcal{E}_{\text{txt}}^{\text{vlm}}}
\newcommand{\encimg}{\mathcal{E}_{\text{img}}}
\newcommand{\enctxt}{\mathcal{E}_{\text{txt}}}

\newcommand{\bI}{\mathbf{I}}
\newcommand{\bb}{\mathbf{b}}
\newcommand{\bB}{\mathbf{B}}
\newcommand{\bF}{\mathbf{F}}
\newcommand{\bof}{\mathbf{f}}
\newcommand{\bz}{\mathbf{z}}
\newcommand{\bs}{\mathbf{s}}
\newcommand{\bw}{\mathbf{w}}
\newcommand{\bW}{\mathbf{W}}
\newcommand{\bG}{\mathbf{G}}
\newcommand{\bS}{\mathbf{S}}
\newcommand{\calT}{\mathcal{T}}
\newcommand{\calG}{\mathcal{G}}
\newcommand{\class}{\mathcal{C}}
\newcommand{\classSup}{\mathcal{C}^{\mathrm{det}}}
\newcommand{\dataSup}{\mathcal{D}^{\mathrm{det}}}
\newcommand{\classWeak}{\mathcal{C}^{\mathrm{weak}}}
\newcommand{\dataWeak}{\mathcal{D}^{\mathrm{weak}}}
\newcommand{\classTest}{\mathcal{C}^{\mathrm{test}}}
\newcommand{\mAPr}{mAP$_{\text{r}}$}
\newcommand{\mAPnoval}{mAP$_{\text{novel}}$}
\newcommand{\mAPall}{mAP$_{\text{all}}$}
\newcommand{\mask}{^{\text{mask}}}
\newcommand{\bbox}{^{\text{box}}}
% \newcommand{\etal}{\emph{et al.}}
% \newcommand{\vs}{\emph{vs}\xspace}
\newcommand{\baselineDet}{Box-Supervised\xspace}

%%%%%%%%% METHODS
\newcommand{\vild}{ViLD\xspace}
\newcommand{\deticFull}{Detector with Image Classes\xspace}
\newcommand{\detic}{Detic\xspace}
\newcommand{\vldet}{VLD\xspace}


% \newcommand{\data}{\sD}
\newcommand{\data}{\mathcal{D}}
\newcommand{\feature}{\mathcal{Z}}
\newcommand{\batch}{\mathcal{B}}

\newcommand{\classes}{\mathcal{C}}

\newcommand{\numdata}{\vert\data\vert}
\newcommand{\numdatast}{\vert\data\tst\vert}

% \newcommand{\classes}{\sY}
\newcommand{\numclasses}{\vert\classes\vert}
\newcommand{\numclassesst}{\classes\tst}

\newcommand{\lab}{\mathtt{L}}
\newcommand{\unlab}{\mathtt{U}}
\newcommand{\all}{\mathtt{A}}

\newcommand{\tlab}{\ensuremath{^{[\lab]}}}
\newcommand{\tunlab}{\ensuremath{^{[\unlab]}}}


\newcommand{\tall}{\ensuremath{^{[\all]}}}
\newcommand{\told}{\ensuremath{^{[\old]}}}
\newcommand{\tnew}{\ensuremath{^{[\new]}}}

\newcommand{\task}{\mathcal{T}}
\newcommand{\taskb}{\bm{\mathcal{T}}}

\newcommand{\X}{\mathcal{X}}
\newcommand{\Y}{\mathcal{Y}}

\newcommand{\Mu}{\bm{M}}
\newcommand{\Mub}{\bm{M}}




% \newcommand{\ie}{\textit{i}.\textit{e}.}
% \newcommand{\eg}{\textit{e}.\textit{g}.}
% \newcommand{\etal}{\textit{et}.\textit{al}.}
\newcommand{\normal}{\mathcal{N}}
\newcommand{\ths}{\textsuperscript{th}\;}
% \newcommand{\E}{\mathbb{E}}

% ======================================================================================================
% My macros for vectors and matrices
% ======================================================================================================
\newcommand{\mbf}[1]{\mathbf{#1}}
% \newcommand{\va}{\mbf{a}}
% \newcommand{\vb}{\mbf{b}}
% \newcommand{\vc}{\mbf{c}}
% \newcommand{\vd}{\mbf{d}}
% \newcommand{\ve}{\mbf{e}}
% \newcommand{\vf}{\mbf{f}}
% \newcommand{\vg}{\mbf{g}}
% \newcommand{\vh}{\mbf{h}}
% \newcommand{\vi}{\mbf{i}}
% \newcommand{\vj}{\mbf{j}}
% \newcommand{\vk}{\mbf{k}}
% \newcommand{\vl}{\mbf{l}}
% \newcommand{\vm}{\mbf{m}}
% \newcommand{\vn}{\mbf{n}}
% \newcommand{\vo}{\mbf{o}}
% \newcommand{\vp}{\mbf{p}}
% \newcommand{\vq}{\mbf{q}}
% \newcommand{\vr}{\mbf{r}}
% \newcommand{\vu}{\mbf{u}}
% \newcommand{\vv}{\mbf{v}}
% \newcommand{\vw}{\mbf{w}}
% \newcommand{\vx}{\mbf{x}}
% \newcommand{\vy}{\mbf{y}}
% \newcommand{\vz}{\mbf{z}}
\newcommand{\MA}{\mbf{A}}
\newcommand{\MB}{\mbf{B}}
\newcommand{\MC}{\mbf{C}}
\newcommand{\MD}{\mbf{D}}
\newcommand{\MF}{\mbf{F}}
\newcommand{\MG}{\mbf{G}}
\newcommand{\MH}{\mbf{H}}
\newcommand{\MI}{\mbf{I}}
\newcommand{\MJ}{\mbf{J}}
\newcommand{\MK}{\mbf{K}}
\newcommand{\ML}{\mbf{L}}
\newcommand{\MM}{\mbf{M}}
\newcommand{\MP}{\mbf{P}}
\newcommand{\MQ}{\mbf{Q}}
\newcommand{\MR}{\mbf{R}}
\newcommand{\MS}{\mbf{S}}
\newcommand{\MT}{\mbf{T}}
\newcommand{\MU}{\mbf{U}}
\newcommand{\MV}{\mbf{V}}
\newcommand{\MW}{\mbf{W}}
\newcommand{\MX}{\mbf{X}}
\newcommand{\MY}{\mbf{Y}}
\newcommand{\MZ}{\mbf{Z}}
\newcommand{\ME}{\mbf{E}}

% \DeclareMathOperator*{\argmax}{arg\,max}
% \DeclareMathOperator*{\argmin}{arg\,min}