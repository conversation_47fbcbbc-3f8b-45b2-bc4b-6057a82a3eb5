\section{Method}
\lblsec{mthd_method}

\begin{figure*}[!t]
    \centering
    \includegraphics[width=\linewidth]{fig/approach.pdf}
    \vspace{-7.0mm}
    \caption{
    Overview of our method. \textbf{(Top)} \shine constructs the semantic hierarchy \textit{nexus} classifier in three steps \textit{offline}: (1) For each target class (\textit{e.g.}, \mytexttt{"{\color{me_node}Bat}"} in {\color{me_node}green}) in the given vocabulary, we query the associated super-(in \text{{\color{sup_node}blue}})/sub-(in \text{{\color{sub_node}pink}}) categories from a semantic hierarchy. (2) These retrieved categories along with their interrelationships are integrated into a set of hierarchy-aware sentences using our proposed \textbf{Is-A} connector. (3) These sentences are then encoded by a frozen VLM text encoder (\textit{e.g.}, CLIP~\cite{radford2021learning}) and subsequently fused using an aggregator (\textit{e.g.}, mean-aggregator) to form a \textit{nexus} classifier vector for the target class. \textbf{(Bottom)}: The constructed classifier is directly applied to an \textit{off-the-shelf} \ovod detector for inference, enhancing its robustness across various levels of vocabulary granularity.
    }
    \lblfig{approach}
    \lesspace
\end{figure*}

Our objective is to improve the robustness of \textit{off-the-shelf} open-vocabulary object detectors to diverse user-defined Classes of Interest (CoIs) with varying levels of semantic granularity. We first provide an introduction of open-vocabulary object detection (\ovod). \refsec{mthd_shine} introduces our method of developing the \textbf{S}emantic \textbf{Hi}erarchy \textit{\textbf{Ne}xus} (\shine) based classifier for \ovod detectors to improve their vocabulary granularity robustness. Once established, the \shine classifier can be directly integrated with existing trained \ovod detectors and transferred to novel datasets in a zero-shot manner as discussed in \refsec{mthd_zeroshot}.

\lblsec{mthd_preliminary}
\myparagraph{Problem formulation.}
The objective of \ovodlong is to localize and classify novel object classes freely specified by the user within an image, without 
% necessitating 
any
retraining, in a zero-shot manner. Given an input image $\bI \in \mathbb{R}^{3\times h \times w}$, \ovod localizes all foreground objects and classifies them by estimating a set of bounding box coordinates and class label pairs $\{\bb_m, c_m\}_{m=1}^{M}$, 
with $\bb_m \in \mathbb{R}^4$ and $c_m \in \classTest$, where $\classTest$ is the vocabulary set defined by the user at test time.
To attain open-vocabulary capabilities, \ovod~\cite{zhu2023survey, zhou2022detecting,lin2023match} uses a box-labeled dataset $\dataSup$ with a limited vocabulary $\classSup$ and an auxiliary dataset $\dataWeak$ as weak supervisory signals. $\dataWeak$ features fewer detailed image-class or image-caption annotation pairs, yet it encompasses a broad vocabulary $\classWeak$ (\textit{e.g.}, ImageNet-21k~\cite{deng2009imagenet}), significantly expanding the detection lexicon.


\myparagraph{Open-vocabulary detector.}
Predominant \ovod detectors, such as Detic~\cite{zhou2022detecting} and VLDet~\cite{lin2023match}, follow a two-stage 
pipeline.
First,
given an image, a learned region proposal network (RPN) yields a bag of $M$ region proposals by $\{\bz_m\}_{m=1}^{M} = \Phi_\textsc{RPN}(\bI)$, where $\bz_m \in \mathbb{R}^D$ is a $D$-dimensional region-of-interest (RoI) feature embedding. Then,
for each proposed region, a learned bounding box regressor predicts the location coordinates by $\hat{\bb}_m = \Phi_{\textsc{REG}}(\bz_m)$.
An open-vocabulary classifier estimates a set of classification scores $s_{m}(c,\bz_m) = \langle \bw_{c}, \bz_m \rangle$ for each class, where $\bw_{c}$ is a vector in the classifier $\bW \in \mathbb{R}^{|\classTest|}$ and $\langle \cdot, \cdot \rangle$ is the cosine similarity function. $\bW$ is the frozen text classifier, created by using a \vlm text encoder (\textit{e.g.}, CLIP~\cite{radford2021learning}) to encode the names of CoIs in $\classTest$ specified by the user. The CoI that yields the highest score is assigned as the classification result. During training, \ovod detectors learn all model parameters except for the frozen text classifier. This allows them to achieve region-class alignment by leveraging the vision-language semantic space pre-aligned by \vlm{s} for the open-vocabulary capability. 
Our work aims to improve existing
pre-trained
\ovod detectors, so we omit further details, {and refer the reader to dedicated surveys}~\cite{zhu2023survey,WuPAMI23TowardsOpenVocabularyLearning}.







\subsection{\shine: Semantic Hierarchy Nexus}
\lblsec{mthd_shine}
Here, we 
describe
\shine, 
our proposed
semantic hierarchy \textit{nexus}-based classifier for improving \ovod. As illustrated in \reffig{approach}(top), for each target CoI $c \in \classTest$ (\textit{e.g.}, \mytexttt{"{\color{me_node}Bat}"}) in the user-defined vocabulary, we construct a \textit{nexus} point $\bnexus_c \in \mathbb{R}^{D}$ by {incorporating}
information from related super-/sub-categories derived from a semantic hierarchy $\hier$. \shine is \textit{training-free}. Upon constructing the \textit{nexus} points for the entire vocabulary \textit{offline}, the \textit{nexus}-based classifier $\bNexus$ is directly applied to an \textit{off-the-shelf} \ovod detector for inference. This replaces the conventional CoI name-based classifier $\bW$ with our hierarchy-aware \shine classifier. This enables the classification score $s_{m}(c,\bz_m) = \langle \bnexus_{c}, \bz_m \rangle$ to be high when the proposed region closely aligns with the semantic hierarchy ``theme'' embodied by the \textit{nexus} point. {This point}
% , which 
represents the fusion of a set of hierarchy-aware semantic sentences from specific to abstract that are relevant to the CoI $c$. Next, we detail the construction process.

\myparagraph{Querying the semantic hierarchy.}
To obtain related super-/sub-categories, a semantic hierarchy $\hier$ is crucial for our approach. In this study, we investigate two types of hierarchies: \textit{i)} dataset-specific class taxonomies~\cite{fan2020few, deng2009imagenet, cole2022label}, and \textit{ii)} hierarchies synthesized for the target test vocabulary using \llmlong{s} (\llm). To generate the synthetic hierarchy, we follow Novack \etal~\cite{novack2023chils} and query an LLM such as ChatGPT~\cite{chatgpt} to generate super-categories ($p=3$) and sub-categories ($q=10$) for each CoI $c \in \classTest$, creating a three-level hierarchy $\hier$
(see \refsupp{supp_hier} for details).
With the hierarchy available, as depicted in \reffig{approach}(1), for each target CoI $c$, we retrieve \textit{all} the related super-/sub-categories, which can assist in distinguishing $c$ from other concepts in the vocabulary across granularities~\cite{ge2023improving}. Note that we exclude the root node (\textit{e.g.}, \mytexttt{"entity"}) from this process, as it does not help differentiate $c$ from other categories.

\myparagraph{Hierarchy-aware semantic sentence integration.}
The collected categories contain {both abstract and specific}
semantics useful for guiding the classification process. However, methods like simple ensembling~\cite{novack2023chils} or concatenation~\cite{ge2023improving} overlook some valuable knowledge \textit{implicitly} provided by the hierarchy, namely the inherent internal relationships among concepts. Inspired by the hierarchy structure definition~\cite{silla2011survey}, we propose an \textbf{Is-A} connector to \textit{explicitly} model these \textbf{interrelationships}. Specifically, for each target CoI $c$, the \textbf{Is-A} connector 
{integrates the retrieved categories into sentences}
from the lowest sub-category (more specific) to the highest super-category (more abstract), including the target CoI name. As depicted in \reffig{approach}(2), this process yields a set of $K$ hierarchy-aware sentences $\{\sent_{k}^{c}\}_{k=1}^{K}$. Each sentence $\sent_{k}^{c}$ contains knowledge that spans from specific to abstract, all related to the target CoI and capturing their inherent relationships, as


\noindent \mytexttt{A {\color{sub_node}wooden baseball bat}, which \textbf{is a} {\color{sub_node}baseball bat}, which \textbf{is a} {\color{me_node}bat}, which \textbf{is a} {\color{sup_node}sports equipment}.}


where the sub-categories, target category, and super-categories are color-coded in {\color{sub_node} red}, {\color{me_node} green}, and {\color{sup_node} blue}.

\myparagraph{Semantic hierarchy \textit{Nexus} construction.}
A \textit{nexus} $\bnexus_c \in \mathbb{R}^{D}$ serves as a unifying embedding that fuses the hierarchy-aware knowledge contained in the integrated sentences $\{\sent_{k}^{c}\}_{k=1}^{K}$. As shown in \reffig{approach}(3), we employ a frozen \vlm~\cite{radford2021learning} text encoder $\enctxt$ to translate the integrated sentences into the region-language aligned semantic space compatible with the downstream \ovod detector. The semantic hierarchy \textit{nexus} for the CoI $c$ is then constructed by aggregating these individual sentence embeddings as:
\begin{align}
    % \small
    \bnexus_c &= 
    \text{Aggregator}
    \left(
        \left\{
        \enctxt\left(\sent_k^c\right)
        \right\}_{k=1}^{K}
    \right) \enspace
    \lbleq{aggregation}
\end{align}
where, by default, we employ a straightforward but effective \textbf{mean-aggregator} to compute the mean vector of the set of sentence embeddings. The goal of the aggregation process is to fuse the expressive and granularity-robust knowledge into the \textit{nexus} vector, as a ``theme'', from the encoded hierarchy-aware sentences. Inspired by text classification techniques in Natural Language Processing (NLP)~\cite{li1998classification, shin2018interpreting, gewers2021principal}, we also introduce an alternative aggregator, where we perform SVD decomposition of the sentence embeddings and replace the mean vector with the principal eigenvector as $\bnexus_c$. We study its effectiveness in \refsec{expt_selfstudy} and provide a detailed description in \refsupp{supp_impl_peigen}.


\subsection{Zero-shot Transfer with \shine}
\lblsec{mthd_zeroshot}
As shown in \reffig{approach}(bottom), once the \textit{nexus} points are constructed for each CoI in the target vocabulary, the \shine classifier $\bNexus$ can be directly applied 
to the \ovod detector
for inference, assigning class names to proposed regions as:
\vspace{-4.0mm}
\begin{align}
    \hat{c}_m = \argmax_{c \in \classTest} \langle \bnexus_{c}, \bz_m \rangle
    \lbleq{classifier_nexus} \enspace
\end{align}
where $\bz_m$ is the $m$-th region embedding.
Given that $\bnexus_c \in \mathbb{R}^{D}$, it becomes evident from Eq.~\ref{eq:classifier_nexus} that \shine {has the same}
computational complexity as the vanilla name-based \ovod classifier.
Let us note that \shine is not limited to detection, it can be adapted to open-vocabulary classification by
substituting the region embedding $\bz_m$ with an image one. 
We validate this claim by also benchmarking on ImageNet-1k~\cite{deng2009imagenet}.
We provide the pseudo-code and time complexity analysis of \shine in \refsupp{supp_impl_code} and \refsupp{supp_impl_time}, {respectively.}