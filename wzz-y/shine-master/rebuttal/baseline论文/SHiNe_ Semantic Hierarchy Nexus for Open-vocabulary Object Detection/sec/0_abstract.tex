\begin{abstract}
Open-vocabulary object detection (\ovod) has transformed detection into a language-guided task, empowering users to freely define their class vocabularies of interest during inference. However, our initial investigation indicates that existing \ovod detectors exhibit significant variability when dealing with vocabularies across various semantic granularities, posing a concern for real-world deployment. To this end, we introduce \textbf{S}emantic \textbf{Hi}erarchy \textit{\textbf{Ne}xus} (\textbf{\shine}), a novel classifier that 
uses
semantic knowledge from class hierarchies. It 
runs
\textit{offline} in three steps: \textit{i)} it retrieves relevant super-/sub-categories from a hierarchy for each target class; \textit{ii)} it integrates these categories into hierarchy-aware sentences; \textit{iii)} it fuses these sentence embeddings to generate the \textit{nexus} classifier vector. Our evaluation on various detection benchmarks demonstrates that \shine enhances robustness across diverse vocabulary granularities, achieving up to +31.9\% mAP50 with 
% an existing 
ground truth
hierarchies, while retaining improvements using hierarchies generated by large language models. Moreover, when applied to open-vocabulary classification on ImageNet-1k, \shine improves the CLIP zero-shot baseline by +2.8\% accuracy. \shine is training-free and can be seamlessly integrated with any \textit{off-the-shelf} \ovod detector, without incurring 
additional computational overhead
during inference. 
The code is  \href{https://github.com/naver/shine}{open source}.


\end{abstract}