\section{Experiments}
\lblsec{ovod_expt}
\input{tab/inat_fsod_hier}
\input{tab/training_data}

\myparagraph{Evaluation protocol and datasets.} We primarily follow the cross-dataset transfer evaluation (CDTE) protocol~\cite{zhu2023survey} in our experiments. In this scenario, the \ovod detector is trained on one dataset and then tested on other datasets in a zero-shot manner. This enables a thorough evaluation of model performance across diverse levels of 
vocabulary granularity. We conduct experiments on two detection datasets: \inattitle (\inat)~\cite{cole2022label} and \fsodtitle (\fsod)~\cite{fan2020few}, which have ground-truth hierarchies for evaluating object labeling at multiple levels of granularity.
\inat is a fine-grained detection dataset featuring a consistent six-level label hierarchy based on the 
{biological}
tree of life, along with bounding box annotations for its validation set. \fsod is assembled from OpenImages~\cite{kuznetsova2020open} and ImageNet~\cite{deng2009imagenet}, structured with a two-level label hierarchy. For a more comprehensive evaluation, we use \fsod's test split and manually construct one more hierarchy level atop its existing top level, resulting in a three-level label granularity for evaluation. \reftab{inat_fsod_hier} outlines the number of label hierarchy levels and the corresponding category counts for both datasets, accompanied by examples to demonstrate the semantic granularity. Detailed dataset statistics and their hierarchies are available in \refsupp{supp_data}. We use the mean Average Precision (mAP) at an Intersection-over-Union (IoU) threshold of 0.5 (mAP50) as our main evaluation metric. Additional experiments on COCO~\cite{lin2014microsoft} and LVIS~\cite{gupta2019lvis} under the open-vocabulary protocol~\cite{gu2021open} are provided in \refsupp{supp_expt_cocolvis}.

\begin{figure*}[!th]
    \centering
    \includegraphics[width=\linewidth]{fig/self_study_inat.pdf}
    \vspace{-7.0mm}
    \caption{
    Study of hierarchy-aware sentence integration methods \textbf{(left)} and aggregators \textbf{(right)} across various label granularity levels on the \inat dataset. Detic with a Swin-B backbone is used as the baseline. Darker background color indicates higher mAP50. The default components of \shine are \underline{underlined}. {Note that the experiment in (a) omits sub-categories and the aggregation step.}
    }
    % \lesspace
    \vspace{-6.0mm}
    \lblfig{self_study}
\end{figure*}

\myparagraph{Baseline detector.}
In our experiments, we use the pre-trained Detic~\cite{zhou2022detecting} method as the baseline detector, given its open-source code and strong performance. Detic is a two-stage \ovod detector that relies on CenterNet2~\cite{zhou2021probabilistic} and incorporates a frozen text classifier generated from the CLIP ViT-B/32 text encoder~\cite{radford2021learning} using a prompt of the form: \mytexttt{"a \{Class\}"}. Detic uses both detection and classification data (image-class weak supervisory signals) for training. In our experiments, we explore and compare with Detic under four variants of supervisory signal combinations as shown in \reftab{training_data}. We study a ResNet-50~\cite{he2016deep} and a Swin-B~\cite{liu2021swin} backbone pre-trained on ImageNet-21k-P~\cite{ridnik2021imagenet}.


\myparagraph{\shine implementation details.}
To directly apply our method to the baseline \ovod detector, we use the CLIP ViT-B/32~\cite{radford2021learning} text encoder to construct the \shine classifier and directly apply it to the baseline \ovod detector, following the pipeline described in \refsec{mthd_shine}. We use the mean-aggregator by default. In our experiments, we employ and study two sources for the hierarchy: the ground-truth hierarchy structure provided by the dataset and a synthetic hierarchy generated by an \llm. We use the
gpt-3.5-turbo model~\cite{chatgpt} as our \llm via its public API to produce a simple 3-level hierarchy (comprising one child and one parent level) for the given target CoI vocabulary with temperature 0.7, as outlined in \refsec{mthd_method}. We detail the hierarchy generation process in \refsupp{supp_hier} and report the statistics.

\input{tab/imprv_detic_swin_r50}


\subsection{Analysis of \shine}
\lblsec{expt_selfstudy}
We first study the core components of \shine on the \inat~\cite{cole2022label} using its \textit{ground-truth} hierarchy. Consistent findings on the FSOD~\cite{fan2020few} dataset are reported in \refsupp{supp_expt_ablation_fsod}.

\myparagraph{The Is-A connector effectively integrates hierarchy knowledge in natural sentences.}
To assess the effectiveness of our \textbf{Is-A} connector, we design control experiments for constructing the \ovod classifier with a \textit{single} sentence, omitting sub-categories and the aggregation step. Specifically, for a target CoI like \mytexttt{"{\color{me_node}Baseball bat}"}, we retrieve only its super-categories at each ascending hierarchy level. We then explore three ways to integrate the CoI with its ascending super-categories in natural language and create the classifier vector as follows: 
\begin{itemize}
    \item \textbf{Ensemble (Ens)}:
    \{\mytexttt{"{\color{me_node}baseball bat}"}, \mytexttt{"{\color{sup_node}bat}"}, \mytexttt{"{\color{sup_node}sports equipment}"}\}

    \item \textbf{Concatenate (Concat)}:
    \mytexttt{"A {\color{me_node}baseball bat} {\color{sup_node}bat {\color{sup_node}sports equipment}}"}

    \item \textbf{Is-A (Ours)}:
    \mytexttt{"A {\color{me_node}baseball bat}, which \textbf{is a} {\color{sup_node}bat}, which \textbf{is a} {\color{sup_node}sports equipment}"}
\end{itemize}
where the super-categories are colored in {\color{sup_node}blue}. For \textbf{Concat} and \textbf{Is-A}, we create the classifier vector for the target CoI by encoding the \textit{single} sentence with the CLIP text encoder. For the \textbf{Ens} method, we use the average embedding of the ensembled names as: $\frac{1}{3}(\enctxt(\mytexttt{"{\color{me_node}baseball bat}"})+\enctxt(\mytexttt{"{\color{sup_node}bat}"})+\enctxt(\mytexttt{"{\color{sup_node}sports equipment}"}))$. Next, we conduct control experiments to evaluate the three integration methods as well as the standard CoI name-based baseline methods. As shown in \reffig{self_study}(a), except for the top levels where all methods degrade to the standard baseline (no super-category nodes), all methods outperform the baseline across all granularity levels by directing the model's focus towards more abstract concepts via the included super-categories.
Among the methods compared, our \textbf{Is-A} connector excels across all granularity levels, boosting the baseline mAP50 by up to \textbf{\color{higher}+39.4} points (see last row and second column in \reffig{self_study}(a-L5)). This underscores the effectiveness of our \textbf{Is-A} connector, which integrates related semantic concepts into sentences and explicitly models their relationships, yielding hierarchy-aware embeddings.

\myparagraph{A simple mean-aggregator is sufficient for semantic branch fusion.}
We explored two aggregation methods: mean-aggregator (\textbf{M-Agg}) and principal eigenvector aggregator (\textbf{PE-Agg}). Note that in this experiment, all methods use the proposed \textbf{Is-A} connector to create \textit{a set of} hierarchy-aware sentences to aggregate, ranging from each retrieved sub-category to the super-categories, as elaborated in \refsec{mthd_method}. As \reffig{self_study}(b) shows, both methods improve performance over the baseline across various models and label granularities. Note that these aggregators revert to the simple \textbf{Is-A} method at the leaf level where no sub-categories are available for aggregation. The benefits of aggregation methods are more pronounced with coarser granularity, significantly outperforming the baseline and the \textbf{Is-A} method, with gains up to \textbf{\color{higher}+9.8} on \inat (see third row and second column in \reffig{self_study}(b-L1). Notably, \textbf{M-Agg} generally outperforms \textbf{PE-Agg} despite its simplicity, making it the default choice for \shine in the subsequent experiments. Nonetheless, we aim to highlight the effectiveness of \textbf{PE-Agg}: to the best of our knowledge, this is the first study using the principal eigenvector as a classifier vector in vision-language models.

\subsection{\shine on Open-vocabulary Detection}
\lblsec{expt_ovod}

\input{tab/imprv_vldet_codet_swinB}

\myparagraph{\shine operates with different hierarchies.}
In this section, we broaden our investigation to assess the effectiveness and the robustness of \shine with different semantic hierarchy sources. \reftab{imprv_detic_swin} shows the comparative analysis across various levels of label granularity between the baseline \ovod detector and our method, using either the ground-truth hierarchy or the LLM-generated hierarchy as proxies. We observe that our approach consistently surpasses the baseline by a large margin across all granularity levels on both datasets---and this holds true whether we employ the ground-truth or LLM-generated hierarchy. Averaged across all models and granularity levels on \inat, our method 
yields
an improvement of \textbf{\color{higher}+16.8} points using the ground-truth hierarchy and \textbf{\color{higher}+13.4} points with the LLM-generated hierarchy. For the \fsod dataset, we 
observe
gains of \textbf{\color{higher}+10.3} and \textbf{\color{higher}+2.9} points, respectively. Although the performance gains are smaller with the LLM-generated hierarchy, they nonetheless signify a clear enhancement over the baseline across label granularities on all examined datasets. 
This shows
that \shine is not reliant on ground-truth hierarchies. Even when applied to noisy, synthetic hierarchies, it yields substantial performance improvements. 
{Additional results are in ~\refsupp{supp_expt_more_swinb} and ~\refsupp{supp_expt_stat}.}

\myparagraph{\shine operates with other \ovod detectors.}
To evaluate \shine's generalizability, we apply \shine to additional \ovod detectors: CoDet~\cite{ma2024codet} and VLDet (\vldet)~\cite{lin2022learning}.
The evaluation results showcased in \reftab{imprv_vldet_codet_swinb} affirm that \shine consistently improves the performance of CoDet and VLDet significantly across different granularities on both datasets, with both hierarchies. Further, we assess \shine on another DETR-style~\cite{carion2020end} detector, CORA~\cite{wu2023cora}, in \refsupp{supp_expt_oth_ovod}.
\begin{figure}[!th]
    \vspace{-3.6mm}
    \centering
    \includegraphics[width=\linewidth]{fig/expanded_comp_pairs.pdf}
    \vspace{-6.0mm}
    \caption{
    Analysis of \ovod detection performance under noisy \textit{mis-specified} label vocabularies on \inat \textbf{(left)} and \fsod \textbf{(right)} datasets. We assess the detection performance of both the baseline detector ({\color{bl_meth}in grey}) and our method ({\color{ours_meth}in green}) under varied supervision signals, contrasting results between the original (\rotatebox[origin=c]{45}{$\square$}) and the expanded mis-specified ($\bigcirc$) vocabularies. \shine employs the LLM-generated hierarchy for both vocabularies. We report mAP50, highlighting the performance drop ($\Delta$).
    }
    \lesspace
    \lblfig{expanded_comp}
\end{figure}

\myparagraph{\shine is resilient to \textit{mis-specified} vocabularies.}
\lblsec{expt_ovod_expanded}
In real-world applications, an authentic open vocabulary text classifier may be constructed using a vocabulary comprising a wide array of CoIs, even though only a subset of those specified classes appear in the test data. We define these as \textit{mis-specified} vocabularies. Studying resilience in this challenging scenario is essential for practical applications. To this end, we gathered 500 class names from OpenImages~\cite{kuznetsova2020open} and 1203 from LVIS~\cite{gupta2019lvis}, resulting in 1466 unique classes after deduplication. These are added as ``noisy'' CoIs to the \inat and \fsod \textit{leaf} label vocabularies, creating expanded sets with 1966 and 1570 CoIs, respectively. Using ChatGPT, \shine generates simple 3-level hierarchies for each class in these expanded vocabularies. As shown in \reffig{expanded_comp}, mis-specified vocabularies cause a decrease in baseline detector performance, dropping an average of \textbf{\color{lower}-4.1} points on \inat and \textbf{\color{lower}-4.2} points on \fsod. However, interestingly, \shine not only continues to offer performance gains over the baseline detector but also mitigates the performance drop to \textbf{\color{lower}-1.4} on \inat and \textbf{\color{lower}-3.3} on \fsod, respectively. This suggests that \shine not only improves the robustness but also enhances the resilience of the baseline detector when confronted with a mis-specified vocabulary.


\subsection{\shine on Open-vocabulary Classification}
\lblsec{expt_cls}
In this section, we adapt \shine to open-vocabulary classification, by simply substituting the region embedding in Eq.~\ref{eq:classifier_nexus} with an image embedding from the CLIP image encoder~\cite{radford2021learning}. 
We
evaluate it on the zero-shot transfer classification task 
using
the well-established ImageNet-1k benchmark~\cite{deng2009imagenet}. We compare \shine with two state-of-the-art hierarchy-based methods: \chils~\cite{novack2023chils} and \hclip~\cite{ge2023improving}, which are specifically designed for 
classification.

\input{tab/cls_fps_comp}

\myparagraph{ImageNet-1k Benchmark.}
In \reftab{cls_comp}, we compare methods on
ImageNet in terms of accuracy and frames-per-second (FPS). We observe that our approach consistently outperforms 
related
methods. Comparing to the 
baseline that only uses class names, \shine improves its performance by an average of \textbf{\color{higher}+1.2\%} and \textbf{\color{higher}+2.4\%} across different model sizes using WordNet and LLM-generated hierarchies, {respectively}. 
Note that both \chils and \hclip introduce significant computational overheads due to their \textit{search-on-the-fly} mechanism, resulting in a considerable decrease in inference speed. Consequently, this limits their scalability to detection tasks that necessitate per-region proposal inference for each image. For example, when processing detection results for \textit{one} image with 300 region proposals, the overhead caused by \chils and \hclip would increase by $\approx$300$\times$. In contrast, \shine maintains the same inference speed as the baseline, 
preserving its scalability.

\input{tab/cls_breed_comp}

\myparagraph{BREEDS ImageNet Benchmark.} 
Next, we analyze different granularity levels within ImageNet as organized by BREEDS~\cite{santurkar2020breeds}.
In \reftab{cls_breed_comp}, we observe that \chils and \hclip surpass \shine at coarser granularity levels (L1 to L3). This 
is largely attributed to the BREEDS-modified hierarchy, where specific sub-classes 
in the hierarchy precisely correspond to the objects present in the test data.
Yet, our method yields more substantial performance improvements at finer granularity levels (L4 to L6). Overall, the 
performance gains exhibited
by all three 
methods underscore the benefits of using hierarchy information for improving 
open-vocabulary performance across granularities.


