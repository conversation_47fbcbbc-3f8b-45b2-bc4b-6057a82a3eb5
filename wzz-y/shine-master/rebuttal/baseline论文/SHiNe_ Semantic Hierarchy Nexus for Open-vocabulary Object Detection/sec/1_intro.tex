\vspace{-8.0mm}
\epigraph{\textit{A complicated series of connections between different things.}}{\small {Definition of \textit{Nexus}}, \textbf{Oxford Dictionary}}
\vspace{-8.0mm}


\section{Introduction}
\lblsec{introduction}

\begin{figure}[ht!]
    \centering
    \includegraphics[width=0.9\linewidth]{fig/teaser_all_onecol.pdf}
    \vspace{+2.0mm}
    \caption{
    \textbf{(Top)} Classifier comparison for open-vocabulary object detectors: \textbf{(Left)} standard methods use solely class names in the vocabulary specified by the user to extract text embeddings; \textbf{(Right)} our proposed \shine fuses information from super-/sub-categories into \textit{nexus} points to generate hierarchy-aware representations. \textbf{(Bottom)} Open-vocabulary detection performance at different levels of vocabulary granularity specified by users: A standard {\color{baseline_fig1}Baseline} under-performs and presents significant variability; {\color{ours_fig1} SHiNe} allows for improved and more uniform performance across various vocabularies. Results are on the \inat~\cite{cole2022label} dataset.
    }
    \lblfig{teaser}
\vspace{-4.0mm}
\end{figure}

Open-vocabulary object detection (\ovod)~\cite{gu2021open, zareian2021open, zhu2023survey,WuPAMI23TowardsOpenVocabularyLearning} transforms the object detection task into a language-guided matching problem between visual regions and class names. 
Leveraging 
weak supervisory signals and a pre-aligned vision-language space from Vision-Language Models (\vlm{s})~\cite{radford2021learning,jia2021scaling}, \ovod methods~\cite{zhu2023survey, zhou2022detecting, gu2021open, zareian2021open, lin2022learning} extend the ability {of models} to localize and categorize objects beyond the trained categories. 
Under the \ovod paradigm, target object classes are described using text prompts like \mytexttt{"a \{Class Name\}"}, rather than class indices. By altering the \mytexttt{"\{Class Name\}"}, \ovod methods enable users to \textit{freely} define their own Classes of Interest (CoIs) using natural language. This allows new classes of interest to be detected without the need for model re-training.

Yet, recent studies for open-vocabulary classification~\cite{novack2023chils,parashar2023prompting,ge2023improving} highlight a key challenge: open-vocabulary methods are sensitive to the choice of vocabulary. For instance, Parashar \etal~\cite{parashar2023prompting} enhanced CLIP's zero-shot performance by substituting scientific CoI names, like \mytexttt{"Rosa"}, with common English names, such as \mytexttt{"Rose"}. Recent \ovod models have improved performance by better aligning object features with the \vlm semantic space~\cite{wu2023aligning, kuo2022f}. However, a pivotal question remains: \textit{Are off-the-shelf \ovod detectors truly capable of handling an open vocabulary \textbf{across various semantic granularities}?}

In practical scenarios, Classes of Interest (CoIs) are in the eyes of the beholder. For example, consider a region crop of a \mytexttt{"Dog"}: one user may be interested in the specific breed (\textit{e.g.}, \mytexttt{"Labrador"}), while another might only be concerned about whether it is an \mytexttt{"Animal"}. Thus, the CoI is defined at varying levels of semantic granularity. Ideally, since these CoIs refer to the same visual region, the performance of an \ovod detector should be consistent across different granularities. However, our initial experiments (illustrated in \reffig{teaser}) reveal that the performance of an \ovod detector~\cite{zhou2022detecting} (see {\color{baseline_fig1} \textbf{Baseline}}) fluctuates based on the vocabulary granularity. 
This inconsistency in performance across granularities presents a significant concern for deploying \textit{off-the-shelf} \ovod models in real-world contexts, especially in safety-critical~\cite{knight2002safety} areas like autonomous driving~\cite{martinez2018autonomous}.

Although the same physical object, a \mytexttt{"Labrador"}, can be classified at varying levels of granularity, the inherent \textit{fact} that a \mytexttt{"Labrador is a dog, which is an animal"} remains \textit{constant}. This knowledge is readily available from a semantic hierarchy. Guided by this rationale, we aim to enhance the robustness of existing \ovod detectors to vocabularies specified at any granularity by leveraging knowledge inherent in semantic hierarchies. Recent research in open-vocabulary classification~\cite{novack2023chils,ge2023improving} has explored using super-/sub-categories of CoIs from hierarchies to improve accuracy. However, these methods involve searching through sub-categories or both super-/sub-categories at inference time, leading to 
additional
computational overhead and limiting their use in detection tasks.

We introduce the \textbf{S}emantic \textbf{Hi}erarchy \textit{\textbf{Ne}xus} (\textbf{\shine}), a novel classifier designed to enhance the robustness of \ovod to diverse vocabulary granularities. \shine is \textit{training-free}, and ensures that the inference procedure is \textit{linear} in complexity relative to the number of CoIs.
\shine first retrieves relevant super(abstract)-/sub(specific)-categories from a semantic hierarchy for each CoI in a vocabulary. It then uses an \textbf{Is-A} connector to integrate these categories into hierarchy-aware sentences, while \textit{explicitly} modeling their internal relationships. 
Lastly, it fuses these text embeddings into a vector, termed \textit{nexus}, using an aggregator (\textit{e.g.},
the mean operation)
to form a classifier weight for the target CoI. 
\shine can be directly integrated with any \textit{off-the-shelf} \vlm-based \ovod detector. As shown in \reffig{teaser}, \shine consistently improves performance across a range of CoI vocabulary granularities, while narrowing performance gaps at different granularities.

We evaluate \shine on various detection datasets~\cite{cole2022label, fan2020few}, that cover a broad range of label vocabulary granularities.
This includes scenarios with readily available hierarchies and cases \textit{without} them. In the latter, we utilize \llmlong{s}~\cite{chatgpt} to generate a synthetic~\cite{novack2023chils} three-level hierarchy for \shine. Our results demonstrate that \shine significantly and consistently improves the performance and robustness of baseline detectors, and showcase its generalizability to other \textit{off-the-shelf} \ovod detectors. Additionally, we extend \shine to open-vocabulary classification and further validate its effectiveness by comparing it with two state-of-the-art methods~\cite{novack2023chils, ge2023improving} on the ImageNet-1k~\cite{deng2009imagenet} dataset. The key contributions of this work are:
\begin{itemize}
    \item We show that the performance of existing \ovod detectors varies across vocabulary granularities. This highlights the need for enhanced robustness to 
    % varying 
    arbitrary
    granularities, especially for real-world applications.
    
    \item We introduce \shine, a novel classifier that improves the robustness of \ovod models to various vocabulary granularities using semantic knowledge from hierarchies. \shine is \textit{training-free} and compatible with existing and generated hierarchies. It can be seamlessly integrated into any \ovod detector \textit{without} 
    computational overhead.
    
    \item We demonstrate that \shine consistently enhances the performance 
    of \ovod detectors across various vocabulary granularities on \inat~\cite{cole2022label} and \fsod~\cite{fan2020few}, with gains of up to {\color{higher}\textbf{+31.9}} points
    in mAP50. 
    On open-vocabulary classification, \shine improves the CLIP~\cite{radford2021learning} zero-shot baseline by up to {\color{higher}\textbf{+2.8\%}} 
    on ImageNet-1k~\cite{deng2009imagenet}.
\end{itemize}