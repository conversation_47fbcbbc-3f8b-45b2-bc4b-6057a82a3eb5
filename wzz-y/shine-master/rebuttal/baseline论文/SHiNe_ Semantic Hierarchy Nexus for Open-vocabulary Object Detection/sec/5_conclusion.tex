\section{Conclusion}
\lblsec{conclusion}
Given the importance of the vocabulary in open-vocabulary object detection, the robustness to varying granularities becomes critical for off-the-shelf deployment of \ovod models. Our preliminary investigations uncovered notable performance variability in existing \ovod detectors across different vocabulary granularities. To address this, we 
introduced \shine, a novel method that utilizes semantic knowledge from hierarchies to build \textit{nexus}-based classifiers. \shine is training-free and can be seamlessly integrated with any \ovod detector, maintaining linear complexity relative to 
the number of classes.
We show that \shine yields consistent improvements over baseline detectors across granularities with ground truth and LLM-generated hierarchies. We also extend \shine to open-vocabulary classification and achieve notable gains on ImageNet-1k~\cite{deng2009imagenet}. 
