\section{Related Work}
\lblsec{relatedwork}
\myparagraph{Open-vocabulary object detection (OvOD)}~\cite{zhu2023survey,WuPAMI23TowardsOpenVocabularyLearning} is rapidly gaining traction due to its practical significance, allowing users to \textit{freely} define their Classes of Interest (CoIs) during inference and facilitating the detection of newly specified objects in a zero-shot way. With the aid of weak supervisory signals, OvOD surpasses zero-shot detectors~\cite{tan2021survey} by efficiently aligning visual region features with an embedding space that has been \textit{pre-aligned} with image and text by contrastive \vlmlong{s} (\vlm{s})~\cite{radford2021learning, jia2021scaling}. This process is approached from either the vision or text side to bridge the gap between region-class and image-class alignments. To this end, methods based on region-aware training~\cite{zareian2021open,zang2022open,yao2022detclip,wu2023cora}, pseudo-labeling~\cite{zhou2022detecting,zhong2022regionclip,feng2022promptdet,arandjelovic2023three}, knowledge distillation~\cite{gu2021open,du2022learning,wu2023aligning}, and transfer learning~\cite{kuo2022f,minderer2023scaling,zhang2023simple} are explored. In our study, we 
apply our method to pre-trained region-text aligned \ovod detectors,
improving their performance and robustness to vocabularies of diverse granularities. 
Our method shares conceptual similarities with the work of Kaul~\etal~\cite{kaul2023multi}, where they develop a multi-modal classifier that merges a text-based classifier enriched with descriptors~\cite{menon2022visual} from GPT-3~\cite{brown2020language} and a vision classifier grounded in image exemplars. This classifier is then used to train an \ovod detector \cite{zhou2022detecting} with an extra \textit{learnable} bias. In contrast, our proposed \shine is \textit{training-free}, enabling effortless integration with any OvOD detector.

\myparagraph{Prompt engineering}~\cite{gu2023systematic} has been extensively studied as a technique to enhance \vlm{s}~\cite{radford2021learning,jia2021scaling,zhang2023multi}. \textit{Prompt enrichment} methods~\cite{menon2022visual, pratt2023does, parashar2023prompting, roth2023waffling, yan2023learning} have focused on augmenting frozen \vlm text classifiers by incorporating additional class descriptions sourced from \llmlong{s} (\llm{s})~\citep{brown2020language}. In contrast, our work explores the acquisition of useful semantic knowledge from a hierarchy. \textit{Prompt tuning} methods~\cite{zhou2022learning,shu2022test,khattak2023maple,zhou2022conditional,ren2023prompt, wang2023transhp} introduced \textit{learnable} token vectors into text prompts, which are fine-tuned on downstream tasks. In contrast, our proposed method is \textit{training-free}. Our work is mostly related to two recent methods, CHiLS~\cite{novack2023chils} and H-CLIP~\cite{ge2023improving}, that improve CLIP's~\cite{radford2021learning} zero-shot classification performance by relying on a semantic hierarchy. CHiLS searches for higher logit score matches within the sub-categories, using the max score found to update the initial prediction. H-CLIP runs a combinatorial search over related super-/sub-categories prompt combinations for higher logit scores. However, both approaches incur 
additional
computational overhead due to their \textit{search-on-the-fly} mechanism during inference, constraining their use to classification tasks. In contrast, \shine operates offline and adds no
overhead at inference, 
{making it}
applicable to both classification and detection tasks.

\myparagraph{Semantic hierarchy}~\cite{fellbaum1998wordnet,van2018inaturalist,cole2022label,wah2011caltech} is a tree-like taxonomy~\cite{wu2005learning} or a directed acyclic graph~\cite{ruiz2002hierarchical} that structures semantic concepts following an \textit{asymmetric} and \textit{transitive} ``Is-A'' relation~\cite{silla2011survey}. Previous works have 
used
such hierarchies to benefit various vision tasks~\cite{barz2019hierarchy,deng2010does,frome2013devise,goodman2001classes,morin2005hierarchical,ruggiero2015higher,bertinetto2020making}. Cole \etal~\cite{cole2022label} introduce the extensive iNatLoc dataset with a six-level hierarchy to enhance weakly supervised object localization, showing that appropriate label granularity can improve model training. Shin 
\etal~\cite{shin2020hierarchical} and Hamamci \etal~\cite{hamamci2023diffusion} develop hierarchical architectures that incorporate multiple levels of a label hierarchy for training, enhancing multi-label object detection in remote sensing and dental X-ray images, respectively. Our work distinguishes itself from previous studies in {two} key 
ways: \textit{i)} We focus on multi-modal models; \textit{ii)} We improve 
OvOD detectors using label hierarchies as an external knowledge base, without
requiring
hierarchical annotations or
any training. Furthermore, \shine does not rely on a ground-truth hierarchy and can work with an LLM-generated~\cite{chatgpt} hierarchy.