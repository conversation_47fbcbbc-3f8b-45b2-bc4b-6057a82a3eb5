\clearpage
\maketitlesupplementary
\section*{Appendix}
\appendix

% \setcounter{page}{1}
% \setcounter{table}{0}
% \setcounter{figure}{0}

In this appendix, we begin by detailing the detection datasets in \refsupp{supp_data}. 
Then,
\refsupp{supp_hier} delves into the process of synthetic semantic hierarchy generation using \llm{s}, providing the \llm{} prompts and a thorough summary of the generated hierarchies' statistics. 
We provide in \refsupp{supp_impl} additional implementation specifics of \shine. 
We present in \refsupp{supp_expt_ablation_fsod} an extended analysis of \shine's components on the \fsod dataset. \refsupp{supp_expt_more_swinb} and \refsupp{supp_expt_stat} extend the main detection experiments, offering comprehensive summary statistics. \refsupp{supp_expt_cocolvis} includes additional investigation of \shine{'s performance} on the COCO and LVIS datasets. Finally, \refsupp{supp_qua} showcases some qualitative detection results. 
{Our code is publicly available at \href{https://github.com/naver/shine}{https://github.com/naver/shine}}.

\section{Dataset Details}
\lblsec{supp_data}
\input{tab/full_dataset_statistics}

In \reftab{full_dataset_statistics}, we present a comprehensive summary of the two detection datasets used in our evaluation, \fsod~\cite{fan2020few} and \inat~\cite{cole2022label}, under the cross-dataset transfer open-vocabulary evaluation protocol. Given that the original \fsod dataset~\cite{fan2020few} provides only a two-level hierarchy, we manually constructed the L1 level of the label hierarchy (the most abstract one) for a more comprehensive evaluation, under which we grouped the categories of level L2 (which corresponds to the upper level category in the original label space). The L1 level consists of the following 15 label categories:\\

\mytexttt{\{"liquid", "instrument", "food", "art", "plant", "component",  "animal", "body",  "wearable item", "infrastructure",  "vehicle", "furnishing", "fungi",  "equipment", "beauty product"\}} \\

For \fsod and \inat, we use their official test splits in our experiments, respectively. Notably, \fsod and \inat offer three and six levels of label hierarchies, respectively, each characterized by distinct semantic granularities. Specifically, for the same set of evaluation images and their associated box-label annotations, the actual \textit{label} used for evaluation can be mapped to different linked labels at each granularity level of the hierarchy. For example, in \fsod, a box region labeled as \mytexttt{"watermelon"} at the L3-level could be mapped to label \mytexttt{"fruit"} at the L2-level or \mytexttt{"food"} at the L1-level. This hierarchical approach to labeling facilitates the evaluation of these datasets at various granularity levels. 
{See the annotation files in our \href{https://github.com/naver/shine}{codebase}}.


\myparagraph{Evaluation level.}
During the evaluation, we consider only one hierarchy level at a time, where the label class vocabulary corresponding to the evaluation level of granularity serves as the target test (user-defined) vocabulary for both the methods being compared and our proposed \shine. This means that model has to assign labels solely from the given hierarchy level.




\section{Semantic Hierarchy Generation}
\lblsec{supp_hier}

\input{tab/statistics_generated_hiers}

Being a hierarchy-based method, validating \shine's effectiveness with various hierarchy sources is crucial. In real-world applications, an ideal semantic hierarchy for the target data might not always be available. Therefore, our study focuses on evaluating \shine using not only the dataset-specific class taxonomies~\cite{fan2020few,cole2022label,deng2009imagenet} (the ground-truth hierarchies provided by the datasets as described in \refsec{supp_data}) but also hierarchies synthesized for the target test vocabulary via \llmlong{s} (\llm{s}).
%
Our key idea is that encyclopedic textual information about semantic categories is readily available on the Internet. Contemporary \llm{s} like ChatGPT~\cite{chatgpt}, trained on vast internet-scale corpora, inherently encode the necessary semantic class taxonomic information in their weights. Similar to the approach used in \chils~\cite{novack2023chils}, we employ an \llm to automatically generate simple three-level semantic hierarchies for the target vocabularies.
%
We use the ChatGPT~\cite{chatgpt} gpt-3.5-turbo model as our \llm via its public API to generate the synthetic semantic hierarchy with a temperature parameter of 0.7.
%
{See our \href{https://github.com/naver/shine}{codebase} for the generated hierarchies}.

In the following subsections, we first detail the process of prompting \llm{s} to generate hierarchies (\refsec{supp_hier_prompt}) and then summarize the statistics of the hierarchies we generated (\refsec{supp_hier_stat}).



\subsection{Prompting LLMs}
\lblsec{supp_hier_prompt}
In scenarios where a ground-truth hierarchy is unavailable, and given a label vocabulary $\classTest$ representing the target Classes of Interest (CoIs) at a specific granularity level of the evaluation dataset, the true super-/sub-categories for each CoI are \textit{unknown}. To generate a simple 3-level hierarchy for $\classTest$, we first use ChatGPT~\cite{chatgpt} to generate a list of super-categories for each CoI $c \in \classTest$ using the following \textbf{super-category prompt}:

\begin{center}
    \mytexttt{Generate a list of \textbf{p} super-categories that the following {\textbf{[context]}} object belongs to and output the list separated by '\&': $\mathbf{c}$}
\end{center}

where $p=3$. Subsequently, following Novack \etal~\cite{novack2023chils}, for each CoI $c \in \classTest$, we query ChatGPT~\cite{chatgpt} 
to generate a list of sub-categories using the
following \textbf{sub-category prompt}:

\begin{center}
    \mytexttt{Generate a list of \textbf{q} types of the following {\textbf{[context]}} and output the list separated by '\&': $\mathbf{c}$}
\end{center}

where $q=10$. The \mytexttt{\textbf{\textbf{[context]}}} prompt is consistently set to \mytexttt{\textbf{object}} across all datasets, except for \inat~\cite{cole2022label}, where context-specific prompts like \mytexttt{\textbf{species}} or \mytexttt{\textbf{genus}} are used, aligning with its biological tree of life structure. The \mytexttt{'\&'} symbol serves as a separator prompt, facilitating the formatting of ChatGPT's responses for easier post-parsing of category names. Moreover, the final lists of super-categories and sub-categories are the \textit{union} of results from $t=3$ \llm{} queries. To be more specific, we employ the same super-/sub-category prompts for querying the \llm{} $t=3$ times for each target CoI, and then amalgamate these \llm{} responses to form the final results.

In order to generate hierarchies for all datasets, we fix $p=3$, $q=10$, and $t=3$. It is important to note that we did not perform any extensive hyperparameter tuning for $p$, $q$, and $t$, as our goal is to construct hierarchies automatically and validate \shine's effectiveness with open and noisy hierarchies. Apart from parsing category names from ChatGPT's responses, we do not perform any additional cleaning or organizing of the query results, ensuring an unbiased evaluation of our method's inherent efficacy. The hierarchies generated for the evaluation datasets are directly employed as \llm-generated hierarchies by \shine in our experiments to assess its performance.

\myparagraph{Discussion: Differences between our hierarchy generation process and the one from \chils~\cite{novack2023chils}.}
For any given target vocabulary, \chils uses GPT-3 to generate only sub-categories, forming a two-level hierarchy. In our work, we adopt the \textbf{sub-category prompt} from \chils for generating sub-categories. However, our hierarchy generation strategy significantly differs from \chils in three key respects: \textit{i)} We generate both super-categories and sub-categories, creating a more comprehensive three-level hierarchy. \textit{ii)} We query our \llm three times ($t=3$) and use the union of the outcomes of these queries as the final set, aiming to enrich and diversify the category sets with varied categorization principles. \textit{iii)} As a result of merging and de-duplicating the generated category names from three \llm{} queries, we do not have a predetermined (fixed) number of super-/sub-categories for each target CoI (class). Thus, our generated hierarchies are more varied and imbalanced, aligning more closely with real-world scenarios.


\myparagraph{Discussion: The rationale behind generating $p=3$ super-categories instead of just one.}
In real-world contexts, there is no single ``optimal" hierarchy for any given vocabulary set. A single vocabulary can have multiple, equally valid hierarchical arrangements, depending on the categorization principles applied. For example, \mytexttt{"Vegetable salad"} might be classified under various super-categories—such as \mytexttt{"Appetizer"}, \mytexttt{"Cold dish"}, \mytexttt{"Side dish"}, or simply \mytexttt{"Vegetable"}—based on cultural or contextual differences. Therefore, \textbf{a truly robust and effective hierarchy-based method should function with hierarchies open to diverse categorization principles.} In such open hierarchies, categories are open to multiple categorization principles (\ie, one class may link to several super-category nodes). Thus, we choose to generate $p=3$ super-categories per target CoI (category) in $\classTest$ at each single \llm{} query.
In our 3-level synthetic hierarchies, each target CoI falls under multiple super-categories generated from three times of \llm{} queries, reflecting various and diverse categorization principles. This approach allows us to rigorously evaluate the efficacy of our proposed \shine in realistic, diverse yet noisy categorization scenarios.




\subsection{Summary statistics of the \llm hierarchies}
\lblsec{supp_hier_stat}
In \reftab{statistics_generated_hiers}, we present comprehensive summary statistics for the synthetic hierarchies generated by the \llm{} across each dataset at every label vocabulary level. All synthetic hierarchies are created using $p=3$ and $q=10$, with the final super-/sub-categories a de-duplicated union of results from $t=3$ \llm{} queries. As shown in \reftab{statistics_generated_hiers}, the hierarchies synthesized are both highly open (each CoI is linked to multiple super-categories) and noisy (sub-categories might not be present in the dataset). Despite these challenges, as shown in \reftab{imprv_detic_swin}, \reftab{imprv_vldet_codet_swinb}, \reftab{cls_comp}, \reftab{base_novel_comp}, and \reffig{expanded_comp}, \shine performs effectively using such open and noisy synthetic hierarchies, consistently improving the baseline results. This underlines the adaptability and robustness of \shine in using open and noisy semantic hierarchies when the ground-truth hierarchies are not available.


\section{Further Implementation Details of \shine}
\lblsec{supp_impl}

\subsection{Hierarchy-aware Sentences Integration}
\lblsec{supp_impl_toyexp}

\begin{figure*}[!t]
    \centering
    \includegraphics[width=\linewidth]{fig/explain_hier.pdf}
    \lesspace
    \caption{
    Examples of integrating hierarchy-aware sentences with different hierarchy structures. We use \mytexttt{{"\color{me_node}{\textbf{Bat}}}"} as an example of the target Class of Interest (CoI) for example. The retrieved super-/sub-categories and the target CoI are color-coded in {\color{sup_node} blue} and {\color{sub_node} red}, and {\color{me_node} green}, respectively. \textbf{(a)} The target CoI is linked to a unique super-category at each higher hierarchy level and multiple sub-categories at each lower level, akin to the ground-truth hierarchy structure of the datasets. \textbf{(b)} The target CoI is associated with multiple super-categories at the upper hierarchy level and multiple sub-categories at the lower level, akin to the simple three-level \llm-generated hierarchy structures.
    }
    \lesspace
    \lblfig{explain_hier}
\end{figure*}

This section provides a further explanation of \shine's process for integrating hierarchy-aware sentences with different hierarchical structures, as illustrated in \reffig{explain_hier}.

\myparagraph{Single super-category path hierarchy case (ground-truth hierarchy structures).}
\reffig{explain_hier}(a) illustrates the case where the target Class of Interests (CoI) is linked to a unique super-category at each higher hierarchical level and multiple sub-categories at each lower level. In this case, \shine employs the \textbf{Is-A} connector to form hierarchy-aware sentences by integrating the lowest linked sub-category, the target CoI, and the highest super-category, following their hierarchical relationships in a bottom-up manner. As a result, the total number of constructed sentences in this case equals the number of the lowest linked sub-categories.

\myparagraph{Multiple super-category path hierarchy case (LLM-generated hierarchy structures).} \reffig{explain_hier}(b) displays the case where the target CoI is linked to multiple super-categories at the upper level and several sub-categories at the lower level. Here, \shine builds hierarchy-aware sentences by iterating through all combinations of the linked sub-categories, super-categories, and the target CoI. The \textbf{Is-A} connector is used to connect these categories in a specific-to-abstract order. The resulting number of constructed sentences in this case equals the product of the counts of the lowest linked sub-categories and the linked super-categories.


\subsection{Pseudo-code of \shine}
\lblsec{supp_impl_code}

We show the pseudocode for the core implementation of \shine in \refalg{code_appraoch}, tailored for a three-level hierarchy.

\begin{algorithm}[!ht]
\caption{Pseudocode for constructing \shine classifier \textit{offline} for \ovod detectors in a PyTorch-like style.}
\lblalg{code_appraoch}

\definecolor{codeblue}{rgb}{0.25,0.5,0.8}
\lstset{
  backgroundcolor=\color{white},
  basicstyle=\fontsize{7.2pt}{7.2pt}\ttfamily\selectfont,
  columns=fullflexible,
  breaklines=true,
  captionpos=b,
  commentstyle=\fontsize{7.2pt}{7.2pt}\color{codeblue},
  keywordstyle=\fontsize{7.2pt}{7.2pt},
%  frame=tb,
}
\begin{lstlisting}[language=python]
# target_vocabulary: input class vocabulary for
# inference
# shine_classifier: output SHiNe classifier for
# OvOD detectors
# hrchy: a semantic hierarchy for the target
# vocabulary
# aggregator: computes mean vector or principal
# eigenvector of the given embeddings
# tokenizer: tokenizes given text
# text_encoder: VLM text encoder

# container for SHiNe
shine_classifier = []

# the proposed Is-A connector
isa_connector = "which is a"

# build SHiNe classifier weight vector for each
# class in the vocabulary
for class_name in target_vocabulary:
    # retrieve super-category names
    super_names = hrchy.get_parents(class_name)
    # retrieve sub-category names
    sub_names = hrchy.get_children(class_name)

    # form specific-to-abstract branches combining
    # super-/sub-categories, and the target class
    # name
    branches = [
        [sub_name, class_name, super_name]
        for super_name in super_names
        for sub_name   in child_names
    ]

    # construct hierarch-aware sentences in natural
    # language using the Is-A connector
    sentences = [
        f"a {branch[0]}"
        + "".join([
        f", {isa_connector} {name}"
        for name in branch[1:]
        ])
        for branch in branches
    ]

    # tokenize the sentences
    text_tokens = tokenizer(sentences)
    # extract textual feature representations
    text_embeddings = text_encoder(text_tokens)
    
    # fuse the embeddings into a single nexus-based
    # classifier vector
    nexus_vector = aggregator(text_embeddings)
    
    # append the single classifier vector to the 
    # classifier container
    shine_classifier.extend(nexus_vector)

# stack all the constructed classifier vectors as
# the SHiNe classifier
shine_classifier = torch.stack(shine_classifier)

# l2-normalize the classifier vectors
shine_classifier = l2_normalize(shine_classifier,
                                           dim=1)

# the shine_classifier is output and applied
# directly to the OvOD detector
\end{lstlisting}
\end{algorithm}

\subsection{Time Complexity Analysis of \shine}
\lblsec{supp_impl_time}
Let $c$ be the number of Classes of Interest (CoIs) in a given vocabulary, and let $p$ and $q$ represent the average number of related super-categories and sub-categories per CoI, respectively, in a hierarchy. Our proposed method, \shine, aggregates hierarchy-aware information from both super-categories and sub-categories into $c$ \textit{nexus}-based embeddings (offline).
Consequently, at inference, both memory and time complexity of \shine scale linearly as $\mathcal{O}(c)$. It is important to note that this scalability at inference is unaffected by the number of related super-/sub-categories, because they are only used offline to generate $\bnexus_{c}$. The offline pipeline to construct \shine \ovod classifier needs to run only once.

In contrast, the time and memory complexities for \chils~\cite{novack2023chils} scale at inference as $\mathcal{O}(c(1 + q))$, because image-text similarity scores are computed for vocabulary nodes \textit{and} all their children. \hclip~\cite{ge2023improving}, on the other hand, involves a search procedure \textit{online} across $p \cdot (c+1)$ prompt combinations for the top $k$ (\eg, $k=5$) predicted CoIs, resulting in a time complexity of $\mathcal{O}(c + p \cdot (q+1) \cdot k)$. Crucially, the operations for $p \cdot (q+1) \cdot k$ only commence after the prediction based on the first $c$ standard prompts. Unlike \shine and \chils~\cite{novack2023chils}, for which the embeddings are precomputed and the class predictions can be fully parallelized, \hclip requires encoding the latter $p \cdot (q+1) \cdot k$ CLIP~\cite{radford2021learning} text embeddings at test time on-the-fly. Furthermore, it employs a search-on-the-fly mechanism, resulting in significant computational overheads. This makes \hclip a sub-optimal candidate for many applications, particularly those like detection and segmentation tasks that require per-box, per-mask, or even per-pixel prediction.

Given the extensive number of super-/sub-categories in the hierarchy employed in our experiments, as detailed in \reftab{statistics_generated_hiers}, the substantial computational overheads imposed by \chils and \hclip become evident.



\subsection{Implementation Details of Aggregators}
\lblsec{supp_impl_peigen}

\myparagraph{Mean-aggregator.}
During the semantic hierarchy \textit{nexus} classifier construction phase, as illustrated in \reffig{approach}(3), \shine, by default, uses Eq.~\refeq{eq:aggregation} where the ``Aggregator'' is the mean operation, as
\begin{align}
    % \small
    \bnexus_c &=
    \frac{1}{K}
    \sum_{k=1}^{K}
    \enctxt\left(\sent_k^c\right)\enspace ,
    \lbleq{mean_aggr}
\end{align}
where $\enctxt$ is the frozen CLIP~\cite{radford2021learning} text encoder, and $\{\sent_k^c\}_{k=1}^{K}$ represents the $K$ hierarchy-aware sentences, which are built by integrating all super-/sub-categories related to the target class (CoI) $c$ using our proposed \textbf{Is-A} connector. This aggregator, which we call the \textbf{mean-aggregator}, calculates the mean of the encoded sentences' embeddings to form the final \textit{nexus}-based classifier weight vector for $c$. This mean vector is the centroid represented within CLIP's embedding space, summarizing the general characteristics of the hierarchy-aware embeddings related to the target CoI.
At inference, the classification decision for a region is based on the cosine similarity between the visual embedding of the region and the hierarchy-aware representation defined by the mean vector $\bnexus$, which we call \textit{nexus}. This approach renders the decision-making process less sensitive to variations in the semantic granularity of the name $c$. Note that all the embeddings are $l$2-normalized.


\myparagraph{Principal Eigenvector Aggregator}
Drawing inspiration from text classification techniques in Natural Language Processing (NLP)~\cite{li1998classification, shin2018interpreting, gewers2021principal}, we introduce an alternative aggregation approach, called the \textbf{principal eigenvector aggregator}. This method uses the principal eigenvector of the sentence embeddings matrix as the classifier weight vector $\bnexus_c$. Specifically, for a set of hierarchy-aware sentences $\{\sent_{k}^{c}\}_{k=1}^{K}$, we first apply a Singular Vector Decomposition (SVD) operation on their embedding matrix as:
\begin{align}
    \rmU \rmS \rmV^{T} = 
    \text{SVD}\left(
        \textrm{concat}_{k=1}^{K} \left\{ \enctxt\left(\sent_k^c\right)
        \right\}
    \right) \enspace ,
    \lbleq{peigen_aggr}
\end{align}
where $\rmU$ and $\rmV$ are orthogonal matrices representing the left and right singular vectors, respectively, and $\rmS$ is a diagonal matrix with singular values in descending order. Subsequently, we can derive the principal eigenvector, corresponding to the largest singular value in the sentence embedding matrix, by selecting the first column of matrix $\rmV$ as:
\begin{align}
    \bnexus_c=\rmV[:, 0] \enspace ,
\end{align}
where $\bnexus_c$ serves as the \textit{nexus}-based classifier vector for the target class $c$. In contrast to the mean-aggregator, the \textbf{principal eigenvector aggregator} captures the dominant trend in the sentence embeddings (as known as their ``theme'', to maintain NLP terminology), to effectively represent the CoIs. Note that all the embeddings are $l$2-normalized. 


Next, we explain the rationale behind this aggregator design.
In high-dimensional semantic spaces like the 512-dimensional vision-language aligned embedding space of CLIP ViT-B/32, the principal eigenvector is able to capture the most significant semantic patterns or trends within the embeddings. This approach stems from the understanding that the direction of greatest variance in the space contains the most informative representation of semantic embeddings. Projecting the high-dimensional hierarchy-aware sentence embeddings of a target class (CoI) onto this principal eigenvector yields a condensed yet information-rich representation, preserving the essence of the original hierarchy-aware sentences. Consequently, during inference, classification decisions for a region are based on the cosine similarity between the region's embedding and the semantic pattern or trend depicted by the principal eigenvector. This differs from the representation centroid approach used by the mean-aggregator.

We compare the \textbf{mean-aggregator} and the \textbf{principal eigenvector aggregator} in \refsec{expt_selfstudy} of the main paper. While the principal eigenvector aggregator shows slightly lower performance compared to the mean-aggregator in general, its potential application in \vlm tasks might be interesting for future research. In general, given the intimate connection between computer vision and NLP in open-vocabulary models, we believe in the importance of enabling more connections between the two fields---in this case, drawing from the NLP field of topic modeling.



\section{Extended Analysis of \shine on FSOD}
\lblsec{supp_expt_ablation_fsod}

\begin{figure*}[!t]
    \centering
    \includegraphics[width=\linewidth]{fig/self_study.pdf}
    \vspace{-7mm}
    \caption{
    Further study of hierarchy-aware sentence integration methods \textbf{(left)} and aggregators \textbf{(right)} across various label granularity levels on both \inat and \fsod datasets. Darker color indicates higher mAP50. Components used by default in \shine are underlined. Detic~\cite{zhou2022detecting} with Swin-B backbone, trained using various combinations of supervisory signals described in \reftab{training_data}, serves as the baseline open-vocabulary detector for all methods evaluated. To evaluate the effectiveness of hierarchy-based components, we use the ground-truth hierarchy for all methods that rely on hierarchies.
    }
    % \lesspace
    \lblfig{self_study_both}
\end{figure*}

In \reffig{self_study_both}, we present an expanded study of the core components of \shine, examining their effectiveness across various levels of label granularity on both the \inat and \fsod datasets. The results from \fsod align with those observed in the \inat-only study shown in \reffig{self_study} of the main paper. Next, we provide further analysis of \shine's core components. 

\myparagraph{Extended discussion: the Is-A connector effectively integrates hierarchy knowledge in natural sentences.}
The effectiveness of the proposed \textbf{Is-A} connector is studied in \reffig{self_study_both}(a). Excluding the top (abstract) levels where all methods, including \textbf{Ens}, \textbf{Concat}, and \textbf{Is-A}, revert to the plain baseline due to the absence of further parent nodes, the methods leveraging super-category information consistently outperform the baseline across nearly all levels of granularity. This improvement is attributed to directing the model’s focus towards more general concepts via super-category-inclusive classifiers. An exception occurs at the second level of \fsod (\reffig{self_study_both}(a-FSOD-L2)), where no method exceeds the baseline. We speculate that at this level, target categories like \mytexttt{"Fruit"} are already highly abstract, rendering the addition of more abstract parent categories like \mytexttt{"Food"} redundant in clarifying ambiguities. Nevertheless, this challenge is alleviated when sub-categories are also included in the aggregation step. In comparative terms, the \textbf{Is-A} and \textbf{Concat} connectors yield greater gains than \textbf{Ens}, highlighting the advantage of capturing internal semantic relationships for distinguishing between classes. Notably, our \textbf{Is-A} connector surpasses \textbf{Concat} at all levels of granularity in both datasets, improving the baseline mAP50 by up to \textbf{\color{higher}+39.4} points on \inat (\reffig{self_study_both}(a-iNat-L5)) and \textbf{\color{higher}****} points on \fsod (\reffig{self_study_both}(a-FSOD-L3)). This indicates the superior effectiveness of \textbf{Is-A}'s explicit modeling of category relationships compared to the mere sequential ordering of class names from specific to abstract by \textbf{Concat}. Overall, the integration of more abstract concepts proves beneficial in object detection across diverse label granularities, with our \textbf{Is-A} connector particularly excelling due to its effective incorporation of hierarchical knowledge into natural language sentences, achieved by explicitly modeling internal category relationships.


\myparagraph{Extended discussion: A simple mean-aggregator is sufficient for hierarchy-aware sentences fusion.}
The impact of the aggregation step is analyzed in \reffig{self_study_both}(b), focusing on both the mean-aggregator (M-Agg) and the principal eigenvector aggregator (PE-Agg). These aggregators consistently outperform the baseline across various models and levels of label granularity in all datasets. Notably, their advantage becomes more pronounced with increasingly abstract target vocabularies, surpassing the benchmarks set by the \textbf{Is-A} method. This is especially evident in cases involving highly abstract label vocabularies, where these aggregation methods significantly improve baseline performance, achieving gains of up to \textbf{{\color{higher}****}} points in \inat (\reffig{self_study_both}(b-iNat-L1)) and \textbf{{\color{higher}+20.5}} points in \fsod (\reffig{self_study_both}(b-FSOD-L1)).

These results underscore the effectiveness of the aggregation step in fusing hierarchy-aware sentences into semantic \textit{nexus}-based classifiers. This fusion allows the \textit{nexus}-based classifier to use both specific knowledge from sub-categories and abstract knowledge from super-categories, thereby improving the baseline detector's ability to discriminate visual object robustly.

\begin{figure*}[!ttt]
    \centering
    \includegraphics[width=0.9\linewidth]{fig/summary_comp.pdf}
    \caption{
    Additional summary statistics across all levels for the main experimental results in \reftab{imprv_detic_swin} for \inat \textbf{(upper)} and \fsod \textbf{(lower)}, respectively. This summary includes various measures for mAP50, such as arithmetic mean (AM), harmonic mean (HM), geometric mean (GM), minimum value (Min), median (Med), and maximum value (Max), calculated across all granularity levels within each dataset. A larger area indicates better performance across various metrics. Gray dashed gridlines are scaled from 10 (innermost) to 100 (outermost).
    }
\lblfig{imprv_summary}
\end{figure*}


\section{Comparison with Additional Baselines}
\lblsec{supp_expt_ablation_root_ems}

To further validate the effectiveness of the proposed Is-A prompting method, we further compare \shine with two additional baselines: \textit{i)} \textbf{Root-Stmt} prompting, which explicitly states the root (target) class and its super/sub-classes using the template like \mytexttt{"A {\color{me_node}bat}, which is a {\color{sup_node}sports equipment} and can be instantiated in a {\color{sub_node}wooden baseball bat} or a {\color{sub_node}baseball bat}"}; \textit{ii)} \textbf{80-Prompts}, where we embed the target class name into the 80 hand-crafted prompts from CLIP~\cite{radford2021learning} and average the scores. As shown in \reftab{comp_additional_baselines}, methods leveraging a hierarchy consistently 
surpass the 80-prompt ensemble baseline, demonstrating the benefits of leveraging hierarchy knowledge. 
Moreover, \shine's superior performance to the Root-Stmt baseline suggests that 
Is-A prompting and nexus aggregation is more effective for combining hierarchy information.
\input{tab/comp_additional_baselines}




\section{Extended Main Experimental Results}
\lblsec{supp_expt_more_swinb}
\input{tab/imprv_detic_swinB_12only}

In \reftab{imprv_detic_swinB_12only}, we present additional experimental results from applying our proposed \shine to Detic~\cite{zhou2022detecting} with a Swin-B~\cite{liu2021swin} backbone, trained using only LVIS and LVIS combined with IN-L~\cite{deng2009imagenet} as auxiliary weak supervisory signals. This observation is consistent with those in \reftab{imprv_detic_swin} from the main paper, demonstrating that \shine consistently and substantially improves the performance of the baseline \ovod detector on both \inat and \fsod datasets. This improvement spans across various label vocabulary granularities and is evident with both the ground-truth hierarchy (GT-H) and a synthetic hierarchy generated by \llm{} (LLM-H).


\section{Summary  of the Main Experiments}
\lblsec{supp_expt_stat}
Beyond the per-level comparison in \reftab{imprv_detic_swin} of the main paper, \reffig{imprv_summary} offers an extended comparison (calculated from \reftab{imprv_detic_swin}) using various summary statistical metrics. As shown in \reffig{imprv_summary}, our proposed \shine consistently and markedly enhances the baseline \ovod detector's performance across a range of summary metrics, including arithmetic mean (AM), harmonic mean (HM), geometric mean(GM), on both datasets. The harmonic and geometric means are employed to present the evaluation results from diverse perspectives, particularly in contexts where extreme values might skew the interpretation. These means are less influenced by extreme values, such as exceptionally high or low mAP50 scores at specific granularity levels. The enhancement from \shine is apparent when employing both the ground-truth hierarchy and a synthetic hierarchy generated by the \llm{}. Notably, \shine most significantly improves the baseline's weakest performance (minimum mAP50), suggesting a notable improvement in performance consistency by improving the minimum achieved performance across granularity levels. These results demonstrate that \shine not only boosts overall performance but also enhances consistency across different vocabulary granularities, a crucial aspect for real-world applications.

\section{Experiments with other OvOD Detectors}
\lblsec{supp_expt_oth_ovod}

\input{tab/imprv_vldet_cora_rn50}
We further assess \shine{'s performance on top of} 
an additional OvOD detector, CORA~\cite{wu2023cora}, and present the results alongside VLDet~\cite{lin2022learning} with ResNet-50~\cite{he2016deep} in \reftab{imprv_vldet_cora_rn50}. These results and improvements are consistent with those using Detic~\cite{zhou2022detecting}, CoDet~\cite{ma2024codet}, and VLDet~\cite{lin2022learning}, further validating \shine's effectiveness.



\section{Further Experiments on COCO/LVIS}
\lblsec{supp_expt_cocolvis}

\input{tab/base_novel_comp}

This section extends the evaluation of \shine to COCO~\cite{lin2014microsoft} and LVIS~\cite{gupta2019lvis}, following the open-vocabulary evaluation (OVE) protocol as described in~\cite{zhu2023survey}. According to the OVE protocol, datasets are divided into base and novel classes; models are trained on base classes with bounding box annotations and then evaluated on novel classes and their union. The base classes are disjoint from the novel classes. We follow the base/novel class partitions for COCO and LVIS as used in \cite{zhou2022detecting}. Both datasets have a single, flat class vocabulary: COCO with 65 classes (48 base, 17 novel) and LVIS with 1203 classes (866 base, 337 novel).
\begin{figure*}[!ttt]
    \centering
    \includegraphics[width=0.9\linewidth]{fig/viz_fsod.pdf}
    \caption{
    Qualitative detection results of \shine applied to Detic~\cite{zhou2022detecting} with Swin-B~\cite{liu2021swin}, evaluated on the \fsod~\cite{fan2020few} dataset across three different label granularity levels. All models are trained using the LVIS + IN-L dataset as strong and weak supervisory signals, respectively. It is advisable to zoom in for a clearer view.
    }
    \lesspace
\lblfig{viz_fsod}
\end{figure*}
We use Detic~\cite{zhou2022detecting} with a ResNet-50~\cite{he2016deep} backbone, trained on the box-class annotated base classes with various weak supervisory signals, as the baseline \ovod detector in this experiment. Specifically, the baseline is trained on COCO-base with 48 classes or LVIS-base with 866 classes. We explore three types of weak supervisory signals as proposed in \cite{zhou2022detecting}: \textit{i)} \textbf{N/A}, using only strong supervisory signals; \textit{ii)} \textbf{IN-L}, a 997-class subset of ImageNet-21k~\cite{deng2009imagenet} intersecting with the LVIS vocabulary; \textit{iii)} Conceptual Captions~\cite{sharma2018conceptual} dataset; and \textit{iv)} COCO Captions~\cite{zhou2022detecting} dataset. For Conceptual Captions and COCO Captions, nouns are parsed from the captions, and both image labels and captions are used for weak supervision~\cite{zhou2022detecting}. We report mAP50 for COCO and the official mask mAP metric for LVIS as suggested in~\cite{gupta2019lvis}.

We evaluate and compare \shine with the baseline under the OVE protocol. In the absence of available ground-truth hierarchy information, we use the \llm{} to generate simple 3-level synthetic hierarchies for the target vocabularies of COCO and LVIS, as described in \reftab{statistics_generated_hiers}. Consequently, \shine is constructed using these generated hierarchies. As shown in the OVE evaluation results in \reftab{base_novel_comp}, \shine notably improves the performance of the baseline detector on both COCO and LVIS benchmarks under the OVE protocol. Interestingly, \shine yields a greater performance gain on the novel class partitions. However, this advantage becomes less pronounced when assessing combined base and novel classes. This is attributed to the model overfitting on the base classes to the text classifier based on the standard \mytexttt{"a \{Class Name\}"} prompts during strongly supervised training. Replacing this overfit classifier with the \shine classifier leads to significant gains on novel class partitions, but slightly reduces performance on base class partition test data. Nevertheless, the consistent improvements achieved by \shine across most cases in \reftab{base_novel_comp} underscore its effectiveness on the COCO and LVIS benchmarks.

\section{Qualitative Analysis of SHiNe}
\lblsec{supp_qua}
In \reffig{viz_fsod} and \reffig{viz_inat}, we showcase the qualitative detection results of \shine when applied to Detic~\cite{zhou2022detecting} across various label granularity levels on the \fsod and \inat datasets. For each granularity level, the same confidence threshold is consistently applied.


\clearpage

\begin{figure*}[!ttt]
    \centering
    \includegraphics[width=0.9\linewidth]{fig/viz_inat.pdf}
    \caption{
    Qualitative detection results of \shine applied to Detic~\cite{zhou2022detecting} with Swin-B~\cite{liu2021swin}, evaluated on the \inat~\cite{cole2022label} dataset across six different label granularity levels. All models are trained using the LVIS + IN-L dataset as strong and weak supervisory signals, respectively. It is advisable to zoom in for a clearer view.
    }
\lblfig{viz_inat}
\end{figure*}