%
% --- inline annotations
%
\usepackage[dvipsnames]{xcolor}
\newcommand{\red}[1]{{\color{red}#1}}
\newcommand{\todo}[1]{{\color{red}#1}}
\newcommand{\TODO}[1]{\textbf{\color{red}[TODO: #1]}}
% --- disable by uncommenting  
% \renewcommand{\TODO}[1]{}
% \renewcommand{\todo}[1]{#1}

\usepackage{epigraph}
\setlength\epigraphwidth{\linewidth}



% ======================================================================================================
% My packages
% ======================================================================================================
% Include other packages here, before hyperref.
\usepackage{graphicx}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{mathtools}
\usepackage{booktabs}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{wrapfig}
\usepackage{glossaries}
% \usepackage[table]{xcolor}
\usepackage{colortbl}
\usepackage{bbding}
\usepackage{tikz}
\usepackage{comment}
\usepackage{color}
\usepackage{xspace}
\usepackage{booktabs}
\usepackage{nicefrac}
\usepackage{bbm}
\usepackage{bm}
\usepackage{tabularx,verbatim}
\usepackage{multirow}
% \usepackage{hyperref}
% \usepackage{caption
% \usepackage{subcaption}
% \usepackage[small]{caption}
\usepackage[accsupp]{axessibility}  % % The "axessiblity" package can be found at: https://ctan.org/pkg/axessibility?lang=en. Improves PDF readability for those with disabilities.
% \usepackage{xcolor}
\usepackage{pifont}
\usepackage{tikz}



\definecolor{remark}{rgb}{1,.5,0} 
\definecolor{citecolor}{rgb}{0,0.443,0.737} 
\definecolor{linkcolor}{rgb}{0.956,0.298,0.235} 
% \definecolor{gray}{gray}{0.95}
\definecolor{cyan}{rgb}{0.831,0.901,0.945}

\usepackage{newfloat}
\usepackage{listings}
\DeclareCaptionStyle{ruled}{labelfont=normalfont,labelsep=colon,strut=off} % DO NOT CHANGE THIS
\lstset{%
	basicstyle={\footnotesize\ttfamily},% footnotesize acceptable for monospace
	numbers=left,numberstyle=\footnotesize,xleftmargin=2em,% show line numbers, remove this entire line if you don't want the numbers.
	aboveskip=0pt,belowskip=0pt,%
	showstringspaces=false,tabsize=2,breaklines=true}
\floatstyle{ruled}
\newfloat{listing}{tb}{lst}{}
\floatname{listing}{Listing}

% ======================================================================================================
% My macros
% ======================================================================================================
\input{util/my_math_commands.tex}
\input{util/my_commands.tex}
% \input{util/my_macros}
