% CVPR 2024 Paper Template; see https://github.com/cvpr-org/author-kit

\documentclass[10pt,twocolumn,letterpaper]{article}

%%%%%%%%% PAPER TYPE  - PLEASE UPDATE FOR FINAL VERSION
% \usepackage{cvpr}              % To produce the CAMERA-READY version
% \usepackage[review]{cvpr}      % To produce the REVIEW version
\usepackage[pagenumbers]{cvpr} % To force page numbers, e.g. for an arXiv version

% Import additional packages in the preamble file, before hyperref
\input{preamble}

\newcommand{\eli}[1]{\textcolor{green}{[\textbf{Elisa}: #1]}}
\newcommand{\elisah}[1]{\textcolor{green}{#1}} 

% It is strongly recommended to use hyperref, especially for the review version.
% hyperref with option pagebackref eases the reviewers' job.
% Please disable hyperref *only* if you encounter grave issues, 
% e.g. with the file validation for the camera-ready version.
%
% If you comment hyperref and then uncomment it, you should delete *.aux before re-running LaTeX.
% (Or just hit 'q' on the first LaTeX run, let it finish, and you should be clear).
\definecolor{cvprblue}{rgb}{0.21,0.49,0.74}

\usepackage[pagebackref,breaklinks,colorlinks,citecolor=cvprblue]{hyperref}

% Support for easy cross-referencing
\usepackage[capitalize]{cleveref}
\crefname{section}{Sec.}{Secs.}
\Crefname{section}{Section}{Sections}
\Crefname{table}{Table}{Tables}
\crefname{table}{Tab.}{Tabs.}
% \usepackage[pagebackref,breaklinks,colorlinks,citecolor=cvprblue]{hyperref}
% % Support for easy cross-referencing
% \usepackage[capitalize]{cleveref}
% \usepackage{hyperref}

%%%%%%%%% PAPER ID  - PLEASE UPDATE
\def\paperID{1464} % *** Enter the Paper ID here
\def\confName{CVPR}
\def\confYear{2024}

\input{util/my_macros}
%\input{util/my_macros}

%%%%%%%%% TITLE - PLEASE UPDATE
\title{SHiNe: Semantic Hierarchy Nexus for Open-vocabulary Object Detection}

% 
%%%%%%%%% AUTHORS - PLEASE UPDATE
\author{Mingxuan Liu$^{1,2}$\thanks{Correspondence to: {\tt\small <EMAIL>}~~~~~~~~}~~~~
Tyler L. Hayes$^{2}$~~~~
Elisa Ricci$^{1,3}$~~~~
Gabriela Csurka$^{2}$~~~~
Riccardo Volpi$^{2}$\\
$^{1}$ University of Trento \quad\quad\quad $^{2}$ NAVER LABS Europe \quad\quad\quad $^{3}$ Fondazione Bruno Kessler\\
}

\begin{document}
\maketitle

%%%%%%%%% MAIN PAPER
\input{sec/0_abstract}    
\input{sec/1_intro}
\input{sec/2_related}
\input{sec/3_method}
\input{sec/4_experiment}
\input{sec/5_conclusion}

%%%%%%%%% REFERENCE
% \clearpage

% \newline
\vspace{1.5 mm}
\noindent\small{\textbf{Acknowledgements.}~E.R. is supported by MUR PNRR project FAIR - Future AI Research (PE00000013), funded by NextGenerationEU and EU projects SPRING (No. 871245) and ELIAS (No. 01120237). M.L. is supported by the PRIN project LEGO-AI (Prot. 2020TA3K9N).
We thank Diane Larlus and Yannis Kalantidis for their helpful suggestions.
M.L. thanks Zhun Zhong and Margherita Potrich for their constant
support.
}

{
    \small
    \bibliographystyle{ieeenat_fullname}
    \bibliography{main}
}

%%%%%%%%% SUPPLEMENTARY MATERIALS
\input{sec/X_suppl}

\end{document}
