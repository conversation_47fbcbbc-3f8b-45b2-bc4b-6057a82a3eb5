@String(PAMI = {IEEE Trans. Pattern Anal. Mach. Intell.})
@String(IJCV = {Int. J. Comput. Vis.})
@String(CVPR= {IEEE Conf. Comput. Vis. Pattern Recog.})
@String(ICCV= {Int. Conf. Comput. Vis.})
@String(ECCV= {Eur. Conf. Comput. Vis.})
@String(NIPS= {Adv. Neural Inform. Process. Syst.})
@String(ICPR = {Int. Conf. Pattern Recog.})
@String(BMVC= {Brit. Mach. Vis. Conf.})
@String(TOG= {ACM Trans. Graph.})
@String(TIP  = {IEEE Trans. Image Process.})
@String(TVCG  = {IEEE Trans. Vis. Comput. Graph.})
@String(TMM  = {IEEE Trans. Multimedia})
@String(ACMMM= {ACM Int. Conf. Multimedia})
@String(ICME = {Int. Conf. Multimedia and Expo})
@String(ICASSP=	{ICASSP})
@String(ICIP = {IEEE Int. Conf. Image Process.})
@String(ACCV  = {ACCV})
@String(ICLR = {Int. Conf. Learn. Represent.})
@String(IJCAI = {IJCAI})
@String(PR   = {Pattern Recognition})
@String(AAAI = {AAAI})
@String(CVPRW= {IEEE Conf. Comput. Vis. Pattern Recog. Worksh.})
@String(CSVT = {IEEE Trans. Circuit Syst. Video Technol.})

@String(SPL	= {IEEE Sign. Process. Letters})
@String(VR   = {Vis. Res.})
@String(JOV	 = {J. Vis.})
@String(TVC  = {The Vis. Comput.})
@String(JCST  = {J. Comput. Sci. Tech.})
@String(CGF  = {Comput. Graph. Forum})
@String(CVM = {Computational Visual Media})

@String(PAMI  = {IEEE TPAMI})
@String(IJCV  = {IJCV})
@String(CVPR  = {CVPR})
@String(ICCV  = {ICCV})
@String(ECCV  = {ECCV})
@String(NIPS  = {NeurIPS})
@String(ICPR  = {ICPR})
@String(BMVC  =	{BMVC})
@String(TOG   = {ACM TOG})
@String(TIP   = {IEEE TIP})
@String(TVCG  = {IEEE TVCG})
@String(TCSVT = {IEEE TCSVT})
@String(TMM   =	{IEEE TMM})
@String(ACMMM = {ACM MM})
@String(ICME  =	{ICME})
@String(ICASSP=	{ICASSP})
@String(ICIP  = {ICIP})
@String(ACCV  = {ACCV})
@String(ICLR  = {ICLR})
@String(IJCAI = {IJCAI})
@String(PR = {PR})
@String(AAAI = {AAAI})
@String(CVPRW= {CVPRW})
@String(CSVT = {IEEE TCSVT})

@misc{Authors14,
 author = {FirstName LastName},
 title = {The frobnicatable foo filter},
 note = {Face and Gesture submission ID 324. Supplied as supplemental material {\tt fg324.pdf}},
 year = 2014
}

@misc{Authors14b,
 author = {FirstName LastName},
 title = {Frobnication tutorial},
 note = {Supplied as supplemental material {\tt tr.pdf}},
 year = 2014
}

@article{Alpher02,
author = {FirstName Alpher},
title = {Frobnication},
journal = PAMI,
volume = 12,
number = 1,
pages = {234--778},
year = 2002
}

@article{Alpher03,
author = {FirstName Alpher and  FirstName Fotheringham-Smythe},
title = {Frobnication revisited},
journal = {Journal of Foo},
volume = 13,
number = 1,
pages = {234--778},
year = 2003
}

@article{Alpher04,
author = {FirstName Alpher and FirstName Fotheringham-Smythe and FirstName Gamow},
title = {Can a machine frobnicate?},
journal = {Journal of Foo},
volume = 14,
number = 1,
pages = {234--778},
year = 2004
}

@inproceedings{Alpher05,
author = {{FirstName Alpher and FirstName Gamow}},
title = {Can a computer frobnicate?},
booktitle = {CVPR},
pages = {234--778},
year = 2005
}


@inproceedings{redmon2017yolo9000,
  title={{YOLO9000: Better, Faster, Stronger}},
  author={Redmon, Joseph and Farhadi, Ali},
  booktitle={CVPR},
  year={2017}
}

@misc{sohn2020simple,
	Author		= {Sohn, Kihyuk and Zhang, Zizhao and Li, Chun-Liang and Zhang, Han and Lee, Chen-Yu and Pfister, Tomas},
	Title		= {{A Simple Semi-Supervised Learning Framework for Object Detection}},
	Howpublished= {arXiv:2005.04757},
	Year		= {2020}}

@inproceedings{bilen2016weakly,
  title={{Weakly Supervised Deep Detection Networks}},
  author={Bilen, Hakan and Vedaldi, Andrea},
  booktitle={CVPR},
  year={2016}
}

@inproceedings{ramanathan2020dlwl,
  title={{DLWL: Improving Detection for Lowshot Classes with Weakly Labelled Data}},
  author={Ramanathan, Vignesh and Wang, Rui and Mahajan, Dhruv},
  booktitle={CVPR},
  year={2020}
}

@inproceedings{ye2019cap2det,
  title={{Cap2Det: Learning to Amplify Weak Caption Supervision for Object Detection}},
  author={Ye, Keren and Zhang, Mingda and Kovashka, Adriana and Li, Wei and Qin, Danfeng and Berent, Jesse},
  booktitle={ICCV},
  year={2019}
}



@inproceedings{zhu2020don,
  title={{Don't Even Look Once: Synthesizing Features for Zero-Shot Detection}},
  author={Zhu, Pengkai and Wang, Hanxiao and Saligrama, Venkatesh},
  booktitle={CVPR},
  year={2020}
}

@inproceedings{rahman2020improved,
  title={{Improved Visual-Semantic Alignment for Zero-Shot Object Detection}},
  author={Rahman, Shafin and Khan, Salman and Barnes, Nick},
  booktitle={AAAI},
  year={2020}
}

@Article{kuznetsova2020open,
    Title		= {{The Open Images Dataset V4}},
    Author		= {Kuznetsova, Alina and Rom, Hassan and Alldrin, Neil and Uijlings, Jasper and Krasin, Ivan and Pont-Tuset, Jordi and Kamali, Shahab and Popov, Stefan and Malloci, Matteo and Kolesnikov, Alexander and Duerig,  Tom  and  Ferrari, Vittorio   },
    Journal		= {IJCV},
    Volume 		= {128},
    Pages		= {1956--1981},
    Year		= {2020},
    Publisher	= {Springer}}


@inproceedings{gupta2019lvis,
  title={{LVIS: A Dataset for Large Vocabulary Instance Segmentation}},
  author={Gupta, Agrim and Dollar, Piotr and Girshick, Ross},
  booktitle={CVPR},
  year={2019}
}

@inproceedings{lin2014microsoft,
  title={{Microsoft COCO: Common Objects in Context}},
  author={Lin, Tsung-Yi and Maire, Michael and Belongie, Serge and Hays, James and Perona, Pietro and Ramanan, Deva and Doll{\'a}r, Piotr and Zitnick, C. Lawrence},
  booktitle={ECCV},
  year={2014},
}

@inproceedings{cole2022label,
  title={{On Label Granularity and Object Localization}},
  author={Cole, Elijah and Wilber, Kimberly and Van Horn, Grant and Yang, Xuan and Fornoni, Marco and Perona, Pietro and Belongie, Serge and Howard, Andrew and Aodha, Oisin Mac},
  booktitle={ECCV},
  year={2022},
}

@inproceedings{deng2009imagenet,
  title={{ImageNet: a Large-Scale Hierarchical Image Database}},
  author={Deng, Jia and Dong, Wei and Socher, Richard and Li, Li-Jia and Li, Kai and Fei-Fei, Li},
  booktitle={CVPR},
  year={2009},
}

@inproceedings{sharma2018conceptual,
  title={{Conceptual Captions: A Cleaned, Hypernymed, Image Alt-text Dataset For Automatic Image Captioning}},
  author={Sharma, Piyush and Ding, Nan and Goodman, Sebastian and Soricut, Radu},
  booktitle={ACL},
  year={2018}
}

@inproceedings{wang2023v3det,
  title={{V3Det: Vast Vocabulary Visual Detection Dataset}},
  author={Wang, Jiaqi and Zhang, Pan and Chu, Tao and Cao, Yuhang and Zhou, Yujie and Wu, Tong and Wang, Bin and He, Conghui and Lin, Dahua},
  booktitle={ICCV},
  year={2023}
}

@inproceedings{zhou2022detecting,
  title={{Detecting Twenty-thousand Classes using Image-level Supervision}},
  author={Zhou, Xingyi and Girdhar, Rohit and Joulin, Armand and Kr{\"a}henb{\"u}hl, Philipp and Misra, Ishan},
  booktitle={ECCV},
  year={2022},
}

@inproceedings{novack2023chils,
  title={{CHiLS: Zero-Shot Image Classification with Hierarchical Label Sets}},
  author={Novack, Zachary and McAuley, Julian and Lipton, Zachary Chase and Garg, Saurabh},
  booktitle={ICML},
  year={2023},
}

@inproceedings{mao2023doubly,
  title={{Doubly Right Object Recognition: A Why Prompt for Visual Rationales}},
  author={Mao, Chengzhi and Teotia, Revant and Sundar, Amrutha and Menon, Sachit and Yang, Junfeng and Wang, Xin and Vondrick, Carl},
  booktitle={CVPR},
  year={2023}
}

@inproceedings{ge2023improving,
  title={{Improving Zero-shot Generalization and Robustness of Multi-modal Models}},
  author={Ge, Yunhao and Ren, Jie and Gallagher, Andrew and Wang, Yuxiao and Yang, Ming-Hsuan and Adam, Hartwig and Itti, Laurent and Lakshminarayanan, Balaji and Zhao, Jiaping},
  booktitle={CVPR},
  year={2023}
}

@inproceedings{bertinetto2020making,
  title={{Making Better Mistakes: Leveraging Class Hierarchies with Deep Networks}},
  author={Bertinetto, Luca and Mueller, Romain and Tertikas, Konstantinos and Samangooei, Sina and Lord, Nicholas A.},
  booktitle={CVPR},
  year={2020}
}

@inproceedings{wang2023hierarchical,
    Title		= {{Hierarchical Open-vocabulary Universal Image Segmentation}},
    Author		= {Wang, Xudong and Li, Shufan and Kallidromitis, Konstantinos and Kato, Yusuke and Kozuka, Kazuki and Darrell, Trevor},
    Booktitle	= {NeurIPS},
    Year		= {2023}}
    

@inproceedings{kuo2022f,
    Author		= {Kuo, Weicheng and Cui, Yin and Gu, Xiuye and Piergiovanni, AJ and Angelova, Anelia},
    Title		= {{F-VLM: Open-Vocabulary Object Detection upon Frozen Vision and Language Models}},
    Booktitle	= {ICLR},
    Year		= {2023}}
	

@inproceedings{zhao2017open,
  title={{Open Vocabulary Scene Parsing}},
  author={Zhao, Hang and Puig, Xavier and Zhou, Bolei and Fidler, Sanja and Torralba, Antonio},
  booktitle={ICCV},
  year={2017}
}

@inproceedings{wu2023aligning,
  title={{Aligning Bag of Regions for Open-Vocabulary Object Detection}},
  author={Wu, Size and Zhang, Wenwei and Jin, Sheng and Liu, Wentao and Loy, Chen Change},
  booktitle={CVPR},
  year={2023}
}

@article{WuPAMI23TowardsOpenVocabularyLearning,
	Title		= {{Towards Open Vocabulary Learning: A Survey}},
	Author		= {Wu, Jianzong and Li, Xiangtai and Yuan, Shilin Xu Haobo and Ding, Henghui and Yang, Yibo and Li, Xia and Zhang, Jiangning and Tong, Yunhai and Jiang, Xudong and Ghanem, Bernard and Tao, Dacheng },	
	Journal		= PAMI,
	Year		= {2024} 
}

@inproceedings{li2023blip,
	Title		= {{BLIP-2: Bootstrapping Language-Image Pre-training with Frozen Image Encoders and Large Language Models}},
	Author		= {Li, Junnan and Li, Dongxu and Savarese, Silvio and Hoi, Steven },
	Booktitle	= {ICML},
	Year		= {2023}}



@misc{wu2023protect,
    Title		= {{ProTeCt: Prompt Tuning for Hierarchical Consistency}},
    Author		= {Wu, Tz-Ying and Ho, Chih-Hui and Vasconcelos, Nuno},
    Howpublished= {arXiv:2306.02240},
    Year		= {2023}}	

@inproceedings{rombach2022high,
  title={{High-Resolution Image Synthesis with Latent Diffusion Models}},
  author={Rombach, Robin and Blattmann, Andreas and Lorenz, Dominik and Esser, Patrick and Ommer, Bj{\"o}rn},
  booktitle={CVPR},
  year={2022}
}


@inproceedings{gu2021open,
    Author		= {Gu, Xiuye and Lin, Tsung-Yi and Kuo, Weicheng and Cui, Yin},
    Title		= {{Open-vocabulary Object Detection via Vision and Language Knowledge Distillation}},
    Booktitle	= {ICLR},
    Year		= {2022}}

@inproceedings{ding2022pla,
    Author		= {Ding, Runyu and Yang, Jihan and Xue, Chuhui and Zhang, Wenqing and Bai, Song and Qi, Xiaojuan },
    Title		= {{PLA: Language-Driven Open-Vocabulary 3D Scene Understanding}},
    Booktitle	= {CVPR},
    Year		= {2023}}
    
@inproceedings{lin2023match,
    Author		= {Lin, Wei and Karlinsky, Leonid and Shvetsova, Nina and Possegger, Horst and Kozinski, Mateusz and Panda, Rameswar and Feris, Rogerio and Kuehne, Hilde and Bischof, Horst},
    Title		= {{MAtch, eXpand and Improve: Unsupervised Finetuning for Zero-Shot Action Recognition with Language Knowledge}},
    Booktitle	= {ICCV},
    Year		= {2023}}



@inproceedings{kaul2023multi,
    Title		= {{Multi-Modal Classifiers for Open-Vocabulary Object Detection}},
    Author		= {Kaul, Prannay and Xie, Weidi and Zisserman, Andrew },
    Booktitle	= {ICML},
    Year		= {2023}}



@inproceedings{brown2020language,
  title={{Language Models are Few-Shot Learners}},
  author={Brown, Tom and Mann, Benjamin and Ryder, Nick and Subbiah, Melanie and Kaplan, Jared D. and Dhariwal, Prafulla and Neelakantan, Arvind and Shyam, Pranav and Sastry, Girish and Askell, Amanda and others},
  booktitle={NeurIPS},
  year={2020}
}


@inproceedings{radford2021learning,
  title={{Learning Transferable Visual Models From Natural Language Supervision}},
  author={Radford, Alec and Kim, Jong Wook and Hallacy, Chris and Ramesh, Aditya and Goh, Gabriel and Agarwal, Sandhini and Sastry, Girish and Askell, Amanda and Mishkin, Pamela and Clark, Jack and   Krueger, Gretchen and Sutskever, Ilya},
  booktitle={ICML},
  year={2021},
}

@book{fellbaum1998wordnet,
  title={{WordNet: an Electronic Lexical Database}},
  author={Fellbaum, Christiane},
  year={1998},
  publisher={MIT Press}
}

@inproceedings{romera2015embarrassingly,
  title={{An Embarrassingly Simple Approach to Zero-Shot Learning}},
  author={Romera-Paredes, Bernardino and Torr, Philip},
  booktitle={ICML},
  year={2015},
}

@inproceedings{he2023open,
  title={{Open-Vocabulary Multi-Label Classification via Multi-Modal Knowledge Transfer}},
  author={He, Sunan and Guo, Taian and Dai, Tao and Qiao, Ruizhi and Shu, Xiujun and Ren, Bo and Xia, Shu-Tao},
  booktitle={AAAI},
  year={2023}
}

@inproceedings{yao2023howtoeval,
	Title		= {{How to Evaluate the Generalization of Detection? A Benchmark for Comprehensive Open-Vocabulary Detection}},
	Author		= {Yiyang Yao and Peng Liu and Tiancheng Zhao and Qianqian Zhang and Jiajia Liao and Chunxin Fang and Kyusong Lee and Qing Wang  },
	Booktitle	= {AAAI},
	Year		= {2024}}

@InProceedings{cheng2021boundary,
    Author 		= {Cheng, Bowen  and  Girshick, Ross and  Doll\{a}r, Piotrand C. Berg,  Alexander and Kirillov, Alexander   },
    Title		= {{Boundary IoU: Improving Object-Centric Image Segmentation Evaluation}},
    Booktitle 	= {CVPR},
    Year 		= {2021}}


%%%%%%%%%%%%%%%%%%%%%%%%%%5
@misc{zhu2023survey,
    Title		= {{A Survey on Open-Vocabulary Detection and Segmentation: Past, Present, and Future}},
    Author		= {Zhu, Chaoyang and Chen, Long},
    Howpublished= {arXiv:2307.09220},
    Year		= {2023}}



@inproceedings{jia2021scaling,
  title={{Scaling up visual and vision-language representation learning with noisy text supervision}},
  author={Jia, Chao and Yang, Yinfei and Xia, Ye and Chen, Yi-Ting and Parekh, Zarana and Pham, Hieu and Le, Quoc and Sung, Yun-Hsuan and Li, Zhen and Duerig, Tom},
  booktitle={ICML},
  year={2021},
}


@article{silla2011survey,
  title={{A Survey of Hierarchical Classification across Different Application Domains}},
  author={Silla, Carlos N. and Freitas, Alex A.},
  journal={{Data Mining and Knowledge Discovery}},
  volume={22},
  pages={31--72},
  year={2011},
  publisher={Springer}
}


@inproceedings{fan2020few,
  title={{Few-Shot Object Detection with Attention-RPN and Multi-Relation Detector}},
  author={Fan, Qi and Zhuo, Wei and Tang, Chi-Keung and Tai, Yu-Wing},
  booktitle={CVPR},
  year={2020}
}


@misc{chatgpt,
    author = {{OpenAI}},
    title = {{ChatGPT: A Large-Scale GPT-3.5-Based Model}},
    howpublished = {\url{https://openai.com/blog/chatgpt}},
    year = {2022},
}



@misc{touvron2023llama,
  title={{Llama 2: Open Foundation and Fine-Tuned Chat Models}},
  author={Touvron, Hugo and Martin, Louis and Stone, Kevin and Albert, Peter and Almahairi, Amjad and Babaei, Yasmine and Bashlykov, Nikolay and Batra, Soumya and Bhargava, Prajjwal and Bhosale, Shruti and others},
  howpublished={arXiv:2307.09288},
  year={2023}
}


@article{li1998classification,
  title={{Classification of Text Documents}},
  author={Li, Yong H and Jain, Anil K.},
  journal={The Computer Journal},
  volume={41},
  number={8},
  pages={537--546},
  year={1998},
  publisher={Oxford University Press}
}
@inproceedings{shin2018interpreting,
  title={{Interpreting Word Embeddings with Eigenvector
Analysis}},
  author={Shin, Jamin and Madotto, Andrea and Fung, Pascale},
  booktitle={NeurIPS, IRASL workshop},
  year={2018}
}
@article{gewers2021principal,
  title={{Principal Component Analysis: A Natural Approach to Data Exploration}},
  author={Gewers, Felipe L. and Ferreira, Gustavo R. and {de Arruda}, Henrique F.  and Silva, Filipi N. and Comin, Cesar H. and Amancio, Diego R. and Costa, Luciano da F.},
  journal={ACM Computing Surveys (CSUR)},
  volume={54},
  number={4},
  pages={1--34},
  year={2021},
  publisher={ACM New York, NY, USA}
}


@inproceedings{lin2022learning,
  title={{Interpreting Word Embeddings with Eigenvector
Analysis}},
  author={Lin, Chuang and Sun, Peize and Jiang, Yi and Luo, Ping and Qu, Lizhen and Haffari, Gholamreza and Yuan, Zehuan and Cai, Jianfei},
  booktitle={ICLR},
  year={2023}
}

@inproceedings{wang2023object,
  title={{Object-Aware Distillation Pyramid for Open-Vocabulary Object Detection}},
  author={Wang, Luting and Liu, Yi and Du, Penghui and Ding, Zihan and Liao, Yue and Qi, Qiaosong and Chen, Biaolong and Liu, Si},
  booktitle={CVPR},
  year={2023}
}

@inproceedings{chen2023ovarnet,
  title={{OvarNet: Towards Open-vocabulary Object Attribute Recognition}},
  author={Chen, Keyan and Jiang, Xiaolong and Hu, Yao and Tang, Xu and Gao, Yan and Chen, Jianqi and Xie, Weidi},
  booktitle={CVPR},
  year={2023}
}


@misc{chen2022open,
  title={{Open Vocabulary Object Detection with Proposal Mining and Prediction Equalization}},
  author={Chen, Peixian and Sheng, Kekai and Zhang, Mengdan and Lin, Mingbao and Shen, Yunhang and Lin, Shaohui and Ren, Bo and Li, Ke},
  howpublished={arXiv:2206.11134},
  year={2022}
}



@inproceedings{gao2022open,
  title={{Open Vocabulary Object Detection with Pseudo Bounding-Box Labels}},
  author={Gao, Mingfei and Xing, Chen and Niebles, Juan Carlos and Li, Junnan and Xu, Ran and Liu, Wenhao and Xiong, Caiming},
  booktitle={ECCV},
  year={2022},
}


@inproceedings{santurkar2020breeds,
  title={{BREEDS: Benchmarks for Subpopulation Shift}},
  author={Santurkar, Shibani and Tsipras, Dimitris and Madry, Aleksander},
  booktitle={ICLR},
  year={2021}
}

@inproceedings{liu2021swin,
  title={{Swin Transformer: Hierarchical Vision Transformer using Shifted Windows}},
  author={Liu, Ze and Lin, Yutong and Cao, Yue and Hu, Han and Wei, Yixuan and Zhang, Zheng and Lin, Stephen and Guo, Baining},
  booktitle={ICCV},
  year={2021}
}

@misc{zhou2021probabilistic,
  title={{Probabilistic two-stage Detection}},
  author={Zhou, Xingyi and Koltun, Vladlen and Kr{\"a}henb{\"u}hl, Philipp},
  howpublished={arXiv:2103.07461},
  year={2021}
}


@inproceedings{he2016deep,
  title={{Deep Residual Learning for Image Recognition}},
  author={He, Kaiming and Zhang, Xiangyu and Ren, Shaoqing and Sun, Jian},
  booktitle={CVPR},
  year={2016}
}

@inproceedings{ridnik2021imagenet,
    Title		= {{ImageNet-21K Pretraining for the Masses}},
    Author		= {Ridnik, Tal and Ben-Baruch, Emanuel and Noy, Asaf and Zelnik-Manor, Lihi},
    Booktitle	= {NeurIPS},
    Year		= {2021}}



@inproceedings{ren2015faster,
  title={{Faster R-CNN: Towards Real-time Object Detection with Region Proposal Networks}},
  author={Ren, Shaoqing and He, Kaiming and Girshick, Ross and Sun, Jian},
  booktitle={NeurIPS},
  year={2015}
}


@inproceedings{du2022learning,
  title={{Learning to Prompt for Open-Vocabulary Object Detection with Vision-Language Model}},
  author={Du, Yu and Wei, Fangyun and Zhang, Zihe and Shi, Miaojing and Gao, Yue and Li, Guoqi},
  booktitle={CVPR},
  year={2022}
}

@inproceedings{menon2022visual,
  title={{Visual Classification via Description from Large Language Models}},
  author={Menon, Sachit and Vondrick, Carl},
booktitle={ICLR},
  year={2023}
}

@inproceedings{pratt2023does,
  title={{What Does a Platypus Look Like? Generating Customized Prompts for Zero-shot Image Classification}},
  author={Pratt, Sarah and Covert, Ian and Liu, Rosanne and Farhadi, Ali},
  booktitle={ICCV},
  year={2023}
}

@inproceedings{parashar2023prompting,
    title={{Prompting Scientific Names for Zero-Shot Species Recognition}},
    author={Parashar, Shubham and Lin, Zhiqiu and Li, Yanan and Kong, Shu},
    booktitle={EMNLP},
    year={2023}
}

@inproceedings{ren2023prompt,
  title={{Prompt Pre-Training with Twenty-Thousand Classes for Open-Vocabulary Visual Recognition}},
  author={Ren, Shuhuai and Zhang, Aston and Zhu, Yi and Zhang, Shuai and Zheng, Shuai and Li, Mu and Smola, Alex and Sun, Xu},
  booktitle={NeurIPS},
  year={2023}
}
@inproceedings{jia2022visual,
  title={{Visual Prompt Tuning}},
  author={Jia, Menglin and Tang, Luming and Chen, Bor-Chun and Cardie, Claire and Belongie, Serge and Hariharan, Bharath and Lim, Ser-Nam},
  booktitle={ECCV},
  year={2022},
}

@article{zhou2022learning,
    Title		= {{Learning to Prompt for Vision-Language Models}},
    Author		= {Zhou, Kaiyang and Yang, Jingkang and Loy, Chen Change and Liu, Ziwei},
    Journal		= IJCV,
    Volume 		= {130},
    Number		= {7},
    Pages		= {2337–2348},
    Year		= {2022},
    Publisher	= {Springer}}
    

@inproceedings{khattak2023maple,
  title={{MaPLe: Multi-modal Prompt Learning}},
  author={Khattak, Muhammad Uzair and Rasheed, Hanoona and Maaz, Muhammad and Khan, Salman and Khan, Fahad Shahbaz},
  booktitle={CVPR},
  year={2023}
}

@misc{gu2023systematic,
  title={{A Systematic Survey of Prompt Engineering on Vision-Language Foundation Models}},
  author={Gu, Jindong and Han, Zhen and Chen, Shuo and Beirami, Ahmad and He, Bailan and Zhang, Gengyuan and Liao, Ruotong and Qin, Yao and Tresp, Volker and Torr, Philip},
  howpublished= {arXiv:2307.12980},
  year={2023}
}



@article{liu2023pre,
  title={{Pre-train, Prompt, and Predict: A Systematic Survey of Prompting Methods in Natural Language Processing}},
  author={Liu, Pengfei and Yuan, Weizhe and Fu, Jinlan and Jiang, Zhengbao and Hayashi, Hiroaki and Neubig, Graham},
  journal={ACM Computing Surveys},
  volume={55},
  number={9},
  pages={1--35},
  year={2023},
  publisher={ACM New York, NY}
}

@inproceedings{hamamci2023diffusion,
  title={{Diffusion-Based Hierarchical Multi-Label Object Detection to Analyze Panoramic Dental X-rays}},
  author={Hamamci, Ibrahim Ethem and Er, Sezgin and Simsar, Enis and Sekuboyina, Anjany and Gundogar, Mustafa and Stadlinger, Bernd and Mehl, Albert and Menze, Bjoern},
  booktitle={MICCAI},
  year={2023}
}
@article{shin2020hierarchical,
  title={{Hierarchical Multi-Label Object Detection Framework for Remote Sensing Images}},
  author={Shin, Su-Jin and Kim, Seyeob and Kim, Youngjung and Kim, Sungho},
  journal={Remote Sensing},
  volume={12},
  number={17},
  pages={2734},
  year={2020},
  publisher={MDPI}
}
@inproceedings{shtedritski2023does,
  title={{What does CLIP know about a red circle? Visual prompt engineering for VLMs}},
  author={Shtedritski, Aleksandar and Rupprecht, Christian and Vedaldi, Andrea},
  booktitle={ICCV},
  year={2023}
}
@inproceedings{shu2022test,
  title={{Test-Time Prompt Tuning for Zero-Shot Generalization in Vision-Language Models}},
  author={Shu, Manli and Nie, Weili and Huang, De-An and Yu, Zhiding and Goldstein, Tom and Anandkumar, Anima and Xiao, Chaowei},
  booktitle={NeurIPS},
  year={2022}
}
@inproceedings{zhou2022conditional,
  title={{Conditional Prompt Learning for Vision-Language Models}},
  author={Zhou, Kaiyang and Yang, Jingkang and Loy, Chen Change and Liu, Ziwei},
  booktitle={CVPR},
  year={2022}
}

@misc{bahng2022exploring,
  title={{Exploring Visual Prompts for Adapting Large-Scale Models}},
  author={Bahng, Hyojin and Jahanian, Ali and Sankaranarayanan, Swami and Isola, Phillip},
  howpublished= {arXiv:2203.17274},
  year={2022}
}

@inproceedings{roth2023waffling,
  title={{Waffling around for Performance: Visual Classification with Random Words and Broad Concepts}},
  author={Roth, Karsten and Kim, Jae Myung and Koepke, A. Sophia and Vinyals, Oriol and Schmid, Cordelia and Akata, Zeynep},
  booktitle={ICCV},
  year={2023}
}
@inproceedings{deng2012hedging,
  title={{Hedging Your Bets: Optimizing Accuracy-Specificity Trade-offs in Large Scale Visual Recognition}},
  author={Deng, Jia and Krause, Jonathan and Berg, Alexander C. and Fei-Fei, Li},
  booktitle={CVPR},
  year={2012},
}

@misc{openai2023gpt4,
      title={{GPT-4 Technical Report}}, 
      author={{OpenAI}},
      year={2023},
    howpublished={arXiv:2303.08774}}


@article{Saravia_Prompt_Engineering_Guide_2022,
author = {Saravia, Elvis},
journal = {https://github.com/dair-ai/Prompt-Engineering-Guide},
month = {12},
title = {{Prompt Engineering Guide}},
year = {2022}
}
@article{tan2021survey,
  title={{A Survey of Zero Shot Detection: Methods and Applications}},
  author={Tan, Chufeng and Xu, Xing and Shen, Fumin},
  journal={Cognitive Robotics},
  volume={1},
  pages={159--167},
  year={2021},
  publisher={Elsevier}
}
@inproceedings{girshick2015fast,
  title={{Fast R-CNN}},
  author={Girshick, Ross},
  booktitle={ICCV},
  year={2015}
}
@inproceedings{zareian2021open,
  title={{Open-Vocabulary Object Detection Using Captions}},
  author={Zareian, Alireza and Rosa, Kevin Dela and Hu, Derek Hao and Chang, Shih-Fu},
  booktitle={CVPR},
  year={2021}
}
@inproceedings{zang2022open,
  title={{Open-Vocabulary DETR with Conditional Matching}},
  author={Zang, Yuhang and Li, Wei and Zhou, Kaiyang and Huang, Chen and Loy, Chen Change},
  booktitle={ECCV},
  year={2022},
}

@inproceedings{yao2023detclipv2,
  title={{DetCLIPv2: Scalable Open-Vocabulary Object Detection Pre-training via Word-Region Alignment}},
  author={Yao, Lewei and Han, Jianhua and Liang, Xiaodan and Xu, Dan and Zhang, Wei and Li, Zhenguo and Xu, Hang},
  booktitle={CVPR},
  year={2023}
}

@inproceedings{yao2022detclip,
  title={{DetCLIP: Dictionary-Enriched Visual-Concept
Paralleled Pre-training for Open-world Detection}},
  author={Yao, Lewei and Han, Jianhua and Wen, Youpeng and Liang, Xiaodan and Xu, Dan and Zhang, Wei and Li, Zhenguo and Xu, Chunjing and Xu, Hang},
  booktitle={NeurIPS},
  year={2022}
}
@inproceedings{wu2023cora,
  title={{CORA: Adapting CLIP for Open-Vocabulary Detection with Region Prompting and Anchor Pre-Matching}},
  author={Wu, Xiaoshi and Zhu, Feng and Zhao, Rui and Li, Hongsheng},
  booktitle={CVPR},
  year={2023}
}
@misc{arandjelovic2023three,
  Title={{Three ways to Improve Feature Alignment for Open Vocabulary Detection}},
  Author={Arandjelovi{\'c}, Relja and Andonian, Alex and Mensch, Arthur and H{\'e}naff, Olivier J. and Alayrac, Jean-Baptiste and Zisserman, Andrew},
  Howpublished={arXiv:2303.13518},
  Year={2023}
}
@inproceedings{feng2022promptdet,
  title={{PromptDet: Towards Open-vocabulary Detection using Uncurated Images}},
  author={Feng, Chengjian and Zhong, Yujie and Jie, Zequn and Chu, Xiangxiang and Ren, Haibing and Wei, Xiaolin and Xie, Weidi and Ma, Lin},
  booktitle={ECCV},
  year={2022},
}
@inproceedings{zhong2022regionclip,
  title={{RegionCLIP: Region-based Language-Image Pretraining}},
  author={Zhong, Yiwu and Yang, Jianwei and Zhang, Pengchuan and Li, Chunyuan and Codella, Noel and Li, Liunian Harold and Zhou, Luowei and Dai, Xiyang and Yuan, Lu and Li, Yin and Gao, Jianfeng},
  booktitle={CVPR},
  year={2022}
}
%%%%%%%%% 2
@inproceedings{minderer2023scaling,
  title={{Scaling Open-Vocabulary Object Detection}},
  author={Matthias Minderer and  Alexey Gritsenko and Neil Houlsby},
  booktitle={NeurIPS},
  year={2023},
}
@inproceedings{zhang2023simple,
  title={{A Simple Framework for Open-Vocabulary Segmentation and Detection}},
  author={Zhang, Hao and Li, Feng and Zou, Xueyan and Liu, Shilong and Li, Chunyuan and Yang, Jianwei and Zhang, Lei},
  booktitle={ICCV},
  year={2023}
}
@inproceedings{zhang2023multi,
  title={{Multi-event Video-Text Retrieval}},
  author={Zhang, Gengyuan and Ren, Jisen and Gu, Jindong and Tresp, Volker},
  booktitle={ICCV},
  year={2023}
}
@inproceedings{wu2005learning,
  title={{Learning Classifiers Using Hierarchically Structured Class Taxonomies}},
  author={Wu, Feihong and Zhang, Jun and Honavar, Vasant},
  booktitle={SARA},
  year={2005},
}
@article{ruiz2002hierarchical,
  title={{Hierarchical Text Categorization Using Neural Networks}},
  author={Ruiz, Miguel E. and Srinivasan, Padmini},
  journal={Information retrieval},
  volume={5},
  number= {1},
  pages={87--118},
  year={2002},
  publisher={Springer}
}
@inproceedings{van2018inaturalist,
  title={{The iNaturalist Species Classification and Detection Dataset}},
  author={Van Horn, Grant and Mac Aodha, Oisin and Song, Yang and Cui, Yin and Sun, Chen and Shepard, Alex and Adam, Hartwig and Perona, Pietro and Belongie, Serge},
  booktitle={CVPR},
  year={2018}
}
%%%%%
@inproceedings{barz2019hierarchy,
  title={{Hierarchy-based Image Embeddings for Semantic Image Retrieval}},
  author={Barz, Bj{\"o}rn and Denzler, Joachim},
  booktitle={WACV},
  year={2019},
}
@inproceedings{deng2010does,
  title={{What Does Classifying more than 10,000 Image
Categories Tell Us?}},
  author={Deng, Jia and Berg, Alexander C. and Li, Kai and Fei-Fei, Li},
  booktitle={ECCV},
  year={2010},
}
@inproceedings{frome2013devise,
  title={{DeViSE: A Deep Visual-Semantic Embedding Model}},
  author={Frome, Andrea and Corrado, Greg S. and Shlens, Jon and Bengio, Samy and Dean, Jeff and Ranzato, Marc'Aurelio and Mikolov, Tomas},
  booktitle={NeurIPS},
  year={2013}
}
@inproceedings{goodman2001classes,
  title={{Classes for Fast Maximum Entropy Training}},
  author={Goodman, Joshua},
  booktitle={ICASSP},
  year={2001},
}
@inproceedings{morin2005hierarchical,
  title={{Hierarchical Probabilistic Neural Network Language Model}},
  author={Morin, Frederic and Bengio, Yoshua},
  booktitle={{International Workshop on Artificial Intelligence and Statistics}},
  year={2005},
}
@article{ruggiero2015higher,
  title={{A Higher Level Classification of All Living Organisms}},
  author={Ruggiero, Michael A. and Gordon, Dennis P. and Orrell, Thomas M. and Bailly, Nicolas and Bourgoin, Thierry and Brusca, Richard C. and Cavalier-Smith, Thomas and Guiry, Michael D. and Kirk, Paul M.},
  journal={{PLOS ONE}},
  volume={10},
  number={4},
  pages={e0119248},
  year={2015},
  publisher={Public Library of Science San Francisco, CA USA}
}
@inproceedings{yan2023learning,
  title={{Learning Concise and Descriptive Attributes for Visual Recognition}},
  author={Yan, An and Wang, Yu and Zhong, Yiwu and Dong, Chengyu and He, Zexue and Lu, Yujie and Wang, William Yang and Shang, Jingbo and McAuley, Julian},
  booktitle={ICCV},
  year={2023}
}
@inproceedings{wang2023transhp,
  title={{TransHP: Image Classification with Hierarchical Prompting}},
  author={Wang, Wenhao and Sun, Yifan and Li, Wei and Yang, Yi},
  booktitle={NeurIPS},
  year={2023}
}

@TechReport{wah2011caltech,
    Title		= {{The {Caltech-UCSD} Birds-200-2011 Dataset}},
    Author		= {Wah, Catherine and Branson, Steve and Welinder, Peter and Perona, Pietro and Belongie, Serge},
    Institution	= {{California Institute of Technology}},
    Number		= {CNS-TR-2011-001},
    Year		= {2011}}


@article{martinez2018autonomous,
  title={{Autonomous Vehicles: Theoretical and Practical Challenges}},
  author={Mart{\'\i}nez-D{\'\i}az, Margarita and Soriguera, Francesc},
  journal={Transportation Research Procedia},
  volume={33},
  pages={275--282},
  year={2018},
  publisher={Elsevier}
}
@inproceedings{knight2002safety,
  title={{Safety Critical Systems: Challenges and Directions}},
  author={Knight, John C.},
  booktitle={ICSE},
  year={2002}
}

% only in supp
@inproceedings{bansal2018zero,
  title={{Zero-Shot Object Detection}},
  author={Bansal, Ankan and Sikka, Karan and Sharma, Gaurav and Chellappa, Rama and Divakaran, Ajay},
  booktitle={ECCV},
  year={2018}
}
@misc{chen2015microsoft,
  Title={{Microsoft COCO Captions: Data Collection and Evaluation Server}},
  Author={Chen, Xinlei and Fang, Hao and Lin, Tsung-Yi and Vedantam, Ramakrishna and Gupta, Saurabh and Doll{\'a}r, Piotr and Zitnick, C. Lawrence},
  Howpublished= {arXiv:1504.00325},
  year={2015}
}

@inproceedings{ma2024codet,
  title={{CoDet: Co-Occurrence Guided Region-Word
Alignment for Open-Vocabulary Object Detection}},
  author={Ma, Chuofan and Jiang, Yi and Wen, Xin and Yuan, Zehuan and Qi, Xiaojuan},
  booktitle={NeurIPS},
  year={2023}
}
@inproceedings{carion2020end,
  title={{End-to-end object detection with transformers}},
  author={Carion, Nicolas and Massa, Francisco and Synnaeve, Gabriel and Usunier, Nicolas and Kirillov, Alexander and Zagoruyko, Sergey},
  booktitle={ECCV},
  year={2020},
}