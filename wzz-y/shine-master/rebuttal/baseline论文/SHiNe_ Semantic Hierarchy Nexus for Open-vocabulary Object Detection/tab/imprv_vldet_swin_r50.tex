\begin{table}[!t]
    \centering
    \small
    \tablestyle{2.1pt}{0.8}
    \caption{
    Comparison with VLDet (\vldet)~\cite{lin2022learning} 
    on \inat and \fsod.
    ResNet-50~\cite{he2016deep} and Swin-B~\cite{liu2021swin} are compared. Box-annotated LVIS~\cite{gupta2019lvis} and image-caption-annotated CC3M~\cite{sharma2018conceptual} are used as supervisory signals. mAP50 (\%) is reported.
    }
    \begin{tabular}{ll|lll|lll|}
         \toprule
         &\multicolumn{1}{c}{}
         & \multicolumn{3}{c}{ResNet-50 Backbone} 
         & \multicolumn{3}{c}{Swin-B Backbone} \\

         \cmidrule(r){3-5}
         \cmidrule(r){6-8}

         \multirow{2}{*}{\rotatebox[origin=c]{90}{Set}} 
         & \multirow{2}{*}{\rotatebox[origin=c]{90}{Level}} 
         & \multicolumn{1}{c}{\multirow{2}{*}{\vldet}} & \multicolumn{1}{c}{\shine} & \multicolumn{1}{c|}{\shine} 
         & \multicolumn{1}{c}{\multirow{2}{*}{\vldet}} & \multicolumn{1}{c}{\shine} & \multicolumn{1}{c|}{\shine} \\
         
         &
         && \multicolumn{1}{c}{(GT-\hierarchy)} & \multicolumn{1}{c|}{(LLM-\hierarchy)}
         &&\multicolumn{1}{c}{(GT-\hierarchy)} & \multicolumn{1}{c|}{(LLM-\hierarchy)}\\

         \cmidrule(r){3-5}
         \cmidrule(r){6-8}
         
         \multirow{6}{*}{\rotatebox[origin=c]{90}{\inat}}

         & L6
         & 48.9 & 62.4({\color{higher}+13.5}) & \bf64.8({\color{higher}+15.9}) 
         & 81.7 & \bf84.0({\color{higher}+2.3})  & 83.8({\color{higher}+2.1})\\
								
         & L5 
         & 44.3 & \bf60.6({\color{higher}+16.3}) & 52.6({\color{higher}+8.3}) 
         & 83.7 & \bf84.7({\color{higher}+1.0})  & 82.1({\color{lower}-1.6})\\
 										
         & L4 
         & 42.9 & \bf58.7({\color{higher}+15.8}) & 53.6\bf({\color{higher}+10.7}) 
         & 82.1 & 84.5({\color{higher}+2.4}) & \bf85.8({\color{higher}+3.7})\\

         & L3 
         & 43.8 & \bf63.5({\color{higher}+19.7}) & 58.7({\color{higher}+14.9}) 
         & 77.7 & \bf83.9({\color{higher}+6.2})  & 83.3({\color{higher}+5.6})\\
				
         & L2 
         & 34.3 & \bf54.4({\color{higher}+20.1}) & 46.9\bf({\color{higher}+12.6}) 
         & 71.2 & 75.2({\color{higher}+4.0}) & \bf77.2({\color{higher}+6.0})\\
 											
         & L1
         & 37.0 & \bf43.2({\color{higher}+6.2}) & 38.6({\color{higher}+1.6}) 
         & 66.1 & 66.7({\color{higher}+0.6})    & \bf71.2({\color{higher}+5.1})\\
         
         \cmidrule(r){3-5}
         \cmidrule(r){6-8}
         
         \multirow{3}{*}{\rotatebox[origin=c]{90}{\fsod}}
         
         & L3
         & 48.8 & \bf53.8({\color{higher}+5.0}) & 53.6({\color{higher}+4.8}) 
         & 60.5 & \bf63.7({\color{higher}+3.2}) & 63.3({\color{higher}+2.8})\\
 									
         & L2 
         & 23.5 & \bf39.3({\color{higher}+15.8}) & 29.8({\color{higher}+6.3}) 
         & 33.9 & \bf49.2({\color{higher}+15.3}) & 37.4({\color{higher}+3.5})\\
         
         & L1
         & 12.8 & \bf31.0({\color{higher}+18.2}) & 17.3({\color{higher}+4.5}) 
         & 20.8 & \bf41.6({\color{higher}+20.8}) & 26.2({\color{higher}+5.4})\\
         
         \bottomrule
    \end{tabular}
    \lesspace
    \lbltab{imprv_vldet_swin_r50}
\end{table}
