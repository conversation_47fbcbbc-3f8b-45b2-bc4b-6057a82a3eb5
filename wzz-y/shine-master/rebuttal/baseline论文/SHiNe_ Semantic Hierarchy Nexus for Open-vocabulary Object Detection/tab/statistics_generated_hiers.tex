\begin{table*}[!t]
    \centering
    \tablestyle{8.5pt}{1.1}
    \caption{
    Summary statistics of synthetic hierarchies generated by the \llm{} for our experiments. We present the number of label classes in the target vocabulary, the total number of generated super-categories and sub-categories, and the average number of generated super-categories and sub-categories per target class (CoI) for each dataset at each label vocabulary level. Additionally, links are provided to the experiments using these \llm-generated hierarchies. Note: N/A indicates that only one level of label vocabulary exists in the dataset. \dag: At the most abstract (coarsest) level L1 of \inat, all target classes belong to the single super-category Kingdom \texttt{"Animalia"}.
    }
    \lesspace
    \begin{tabular}{lccccccc}
         \toprule
         \multirow{2}{*}{Dataset} & Corresponding & Label Vocabulary & Number of 
         & \multicolumn{2}{c}{Number of Super-categories} & \multicolumn{2}{c}{Number of Sub-categories} \\
         
         & Experiments & Level & Label Classes & Total & Avg. per class & Total & Avg. per class\\
         
         \midrule

         \multirow{6}{*}{\inat~\cite{cole2022label}}
         & \multirow{6}{*}{
         \reftab{imprv_detic_swin}, \ref{tbl:imprv_vldet_codet_swinb}, \ref{tbl:comp_additional_baselines},
         \ref{tbl:imprv_detic_swinB_12only}, \ref{tbl:imprv_vldet_cora_rn50}
         }
         & L6 & 500 
         & 317 & 0.6 
         & 10909 & 21.8 \\

         &
         & L5 & 317 
         & 184 & 0.6 
         & 6675 & 21.1 \\

         &
         & L4 & 184 
         & 64 & 0.3 
         & 3102 & 16.9 \\
         
         &
         & L3 & 64 
         & 18 & 0.3 
         & 1018 & 16.7 \\

         &
         & L2 & 18 
         & 5 & 0.3 
         & 273 & 15.2 \\

         &
         & L1 & 5 
         & 1\dag & 0.2 
         & 63 & 12.6 \\

         & \reffig{expanded_comp}(a)
         & Mis-spe. L6 & 1966 
         & 7585 & 3.9 
         & 35151 & 17.9 \\
         \midrule

         \multirow{3}{*}{\fsod~\cite{fan2020few}}
         & \multirow{3}{*}{
         \reftab{imprv_detic_swin}, \ref{tbl:imprv_vldet_codet_swinb}, \ref{tbl:comp_additional_baselines},
         \ref{tbl:imprv_detic_swinB_12only}, \ref{tbl:imprv_vldet_cora_rn50}
         }
         & L3 & 200 
         & 1298 & 6.5 
         & 4140 & 20.7 \\

         &
         & L2 & 46 
         & 295 & 6.4 
         & 702 & 15.2 \\

         &
         & L1 & 15 
         & 92 & 6.1 
         & 239 & 15.9 \\

         & \reffig{expanded_comp}(b)
         & Mis-spe. L3 & 1570 
         & 8069 & 5.1 
         & 26684 & 17.0 \\

        \midrule
         COCO~\cite{lin2014microsoft}
         & \reftab{base_novel_comp}
         & N/A & 65
         & 395 & 6.1 
         & 1303 & 20.1 \\      

         \midrule
         LVIS~\cite{gupta2019lvis}
         & \reftab{base_novel_comp}
         & N/A & 1203
         & 6016 & 5.0 
         & 19975 & 16.6 \\      

         \midrule
         ImageNet-1k~\cite{deng2009imagenet}
         & \reftab{cls_comp}
         & N/A & 1000
         & 6361 & 6.4 
         & 19741 & 19.7 \\      

         \bottomrule
    \end{tabular}
    \lesspace
    \lbltab{statistics_generated_hiers}
\end{table*}