\begin{table}
    \centering
    \small
    \tablestyle{4.0pt}{1}

    \caption{
    BREEDS-structured~\cite{santurkar2020breeds} ImageNet-1k zero-shot classification (with varying granularity). All methods use the BREED hierarchy and use CLIP ViT-B/16. Top-1 accuracy (\%) reported.
    }
    \begin{tabular}{clllll}
        \toprule
        
        \multicolumn{1}{c}{Level} & \multicolumn{1}{c}{\# Classes} & \multicolumn{1}{c}{CLIP} & \multicolumn{1}{c}{H-CLIP~\cite{ge2023improving}} & \multicolumn{1}{c}{CHiLS~\cite{novack2023chils}} & \multicolumn{1}{c}{\shine} \\
        
        % \hline
        \midrule
        
        L1 & 10  & 56.2 & 67.9 ({\color{higher}+11.7})  & \bf73.8 ({\color{higher}+17.6})& 50.4({\color{lower}-5.8})\\
        L2 & 29  & 56.8 & \bf69.3 ({\color{higher}+12.5})  & 67.2 ({\color{higher}+10.4})& 60.9({\color{higher}+4.1})\\
        L3 & 128 & 43.3 & \bf62.4 ({\color{higher}+19.1}) & 62.2 ({\color{higher}+18.9})& 54.7({\color{higher}+11.4})\\
        L4 & 466 & 55.2 & 69.6 ({\color{higher}+14.4}) & 70.1 ({\color{higher}+14.9})& \bf70.3({\color{higher}+15.1})\\
        L5 & 591 & 62.4 & 65.9 ({\color{higher}+3.5})  & 64.5 ({\color{higher}+2.1})& \bf69.1({\color{higher}+6.7})\\
        L6 & 98  & 73.1 & 75.4 ({\color{higher}+2.3})  & 73.5 ({\color{higher}+0.4})& \bf78.9({\color{higher}+5.8})\\
        
        \bottomrule
    \end{tabular}
    \lesspace
    \lbltab{cls_breed_comp}
\end{table}