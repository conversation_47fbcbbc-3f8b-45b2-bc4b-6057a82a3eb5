\begin{table*}[!t]
    \centering
    \small
    \setlength{\tabcolsep}{2.0pt} % Set the space between columns
    \caption{
    Detection performance across varying label granularity levels, ranging from finest (F) to coarsest (C), on \inat \textbf{(upper)} and \fsod \textbf{(lower)} datasets. \shine is directly applied to the baseline detector (BL)~\cite{zhou2022detecting} with ground-truth (GT-\hierarchy) and LLM-generated (LLM-\hierarchy) hierarchies. ResNet-50~\cite{he2016deep} \textbf{(left)} and Swin-B~\cite{liu2021swin} \textbf{(right)} backbones~\cite{liu2021swin} are compared. Four types of supervisory signal combinations are investigated. Note (\dag): At the L1-/L6-level of GT-\hierarchy, no super-/sub-categories categories are used, respectively. mAP50 (\%) is reported.
    }
    \begin{tabular}{lll|lll|lll|lll|lll|}
         \toprule
         &&\multicolumn{1}{c}{}
         & \multicolumn{6}{c}{ResNet-50 Backbone} 
         & \multicolumn{6}{c}{Swin-B Backbone} \\

         \cmidrule(r){4-9}
         \cmidrule(r){10-15}

         &&\multicolumn{1}{c}{}
         & \multicolumn{3}{c}{\textbf{I} - LVIS} 
         & \multicolumn{3}{c}{\textbf{II} - LVIS + IN-L} 
         & \multicolumn{3}{c}{\textbf{III} - LVIS + IN-21k} 
         & \multicolumn{3}{c}{\textbf{IV} - LVIS \& COCO + IN-21k}\\

         \cmidrule(r){4-6}
         \cmidrule(r){7-9}
         \cmidrule(r){10-12}
         \cmidrule(r){13-15}

         \multirow{2}{*}{\rotatebox[origin=c]{90}{Set}} & \multirow{2}{*}{\rotatebox[origin=c]{90}{Gran}} & \multirow{2}{*}{\rotatebox[origin=c]{90}{Level}} 
         & \multicolumn{1}{c}{\multirow{2}{*}{BL}} & \multicolumn{1}{c}{\shine} & \multicolumn{1}{c|}{\shine} 
         & \multicolumn{1}{c}{\multirow{2}{*}{BL}} & \multicolumn{1}{c}{\shine} & \multicolumn{1}{c|}{\shine} 
         & \multicolumn{1}{c}{\multirow{2}{*}{BL}} & \multicolumn{1}{c}{\shine} & \multicolumn{1}{c|}{\shine} 
         & \multicolumn{1}{c}{\multirow{2}{*}{BL}} & \multicolumn{1}{c}{\shine} & \multicolumn{1}{c|}{\shine} \\
         
         &&
         && \multicolumn{1}{c}{(GT-\hierarchy)} & \multicolumn{1}{c|}{(LLM-\hierarchy)} &  & \multicolumn{1}{c}{(GT-\hierarchy)} & \multicolumn{1}{c|}{(LLM-\hierarchy)} & & \multicolumn{1}{c}{(GT-\hierarchy)} & \multicolumn{1}{c|}{(LLM-\hierarchy)} &  & \multicolumn{1}{c}{(GT-\hierarchy)} & \multicolumn{1}{c|}{(LLM-\hierarchy)} \\

         \cmidrule(r){4-6}
         \cmidrule(r){7-9}
         \cmidrule(r){10-12}
         \cmidrule(r){13-15}
         
         \multirow{6}{*}{\rotatebox[origin=c]{90}{\inat}} & \multirow{6}{*}{\rotatebox[origin=c]{90}{(C $\xleftarrow{\hspace{1.2cm}}$ F)}}

         & L6\dag
         & 32.0 & 48.4({\color{higher}+16.4}) & \bf52.8({\color{higher}+20.8}) 
         & 35.2 & 57.1({\color{higher}+21.9}) & \bf58.3({\color{higher}+23.1})
         & 58.6 & \bf86.3({\color{higher}+27.7}) & 84.5({\color{higher}+25.9}) 
         & 60.2 & \bf86.4({\color{higher}+26.2}) & 82.7({\color{higher}+22.5})\\
								
         && L5 
         & 28.2 & \bf49.4({\color{higher}+21.2}) & 41.1({\color{higher}+12.9}) 
         & 30.3 & \bf59.0({\color{higher}+28.7}) & 46.6({\color{higher}+16.3})
         & 54.9 & \bf86.8({\color{higher}+31.9}) & 76.3({\color{higher}+21.4})  
         & 57.5 & \bf86.3({\color{higher}+28.8}) & 76.1({\color{higher}+18.6})\\
 										
         && L4 
         & 40.1 & \bf51.5({\color{higher}+11.4}) & 50.4({\color{higher}+10.3}) 
         & 43.4 & \bf61.4({\color{higher}+18.0}) & 57.5({\color{higher}+14.1})
         & 73.1 & \bf87.7({\color{higher}+14.6}) & 84.0({\color{higher}+10.9})  
         & 74.9 & \bf86.2({\color{higher}+11.3}) & 83.4({\color{higher}+8.5})\\
								
         && L3 
         & 38.8 & 56.5({\color{higher}+17.7}) & \bf57.2({\color{higher}+18.4}) 
         & 41.6 & \bf65.3({\color{higher}+23.7}) & 61.7({\color{higher}+20.1})
         & 63.8 & \bf86.9({\color{higher}+23.1}) & 83.6({\color{higher}+19.8})  
         & 67.2 & \bf84.3({\color{higher}+17.1}) & 81.7({\color{higher}+14.5})\\
				
         && L2 
         & 34.4 & \bf45.0({\color{higher}+10.6}) & 43.9({\color{higher}+9.5}) 
         & 39.3 & \bf53.7({\color{higher}+14.4}) & 50.5({\color{higher}+11.2})
         & 65.3 & \bf78.1({\color{higher}+12.8}) & 77.2({\color{higher}+11.9}) 
         & 67.2 & 73.8({\color{higher}+6.6}) & \bf74.5({\color{higher}+7.3})\\
 											
         && L1\dag 
         & 31.6 & \bf33.6({\color{higher}+2.0}) & 33.5({\color{higher}+1.9}) 
         & 32.5 & \bf43.3({\color{higher}+10.8}) & 36.9({\color{higher}+4.4})
         & 65.4 & \bf70.3({\color{higher}+4.9}) & 63.8({\color{lower}-1.6}) 
         & 64.4 & \bf64.9({\color{higher}+0.5}) & 62.1({\color{lower}-2.3})\\
         
         \cmidrule(r){4-6}
         \cmidrule(r){7-9}
         \cmidrule(r){10-12}
         \cmidrule(r){13-15}
         
         \multirow{3}{*}{\rotatebox[origin=c]{90}{\fsod}} & \multirow{3}{*}{\rotatebox[origin=c]{90}{(C $\leftarrow$ F)}}
 										
         & L3\dag 
         & 49.7 & 52.1({\color{higher}+2.4}) & \bf52.2({\color{higher}+2.5}) 
         & 51.9 & 53.6({\color{higher}+1.7}) & \bf53.7({\color{higher}+1.8})
         & 66.0 & \bf66.7({\color{higher}+0.7}) & 66.3({\color{higher}+0.3}) 
         & 65.6 & \bf66.4({\color{higher}+0.8}) & \bf66.4({\color{higher}+0.8}) \\

         && L2 
         & 28.2 & \bf39.9({\color{higher}+11.7}) & 30.9({\color{higher}+2.7}) 
         & 27.8 & \bf39.8({\color{higher}+12.0}) & 29.8({\color{higher}+2.0})
         & 38.4 & \bf51.4({\color{higher}+13.0}) & 40.3({\color{higher}+1.9}) 
         & 39.4 & \bf52.4({\color{higher}+13.0}) & 41.5({\color{higher}+2.1})\\
         
         && L1\dag 
         & 16.0 & \bf34.3({\color{higher}+18.3}) & 22.0({\color{higher}+6.0}) 
         & 16.5 & \bf31.4({\color{higher}+14.9}) & 21.0({\color{higher}+4.5})
         & 24.7 & \bf42.2({\color{higher}+17.5}) & 30.2({\color{higher}+5.5}) 
         & 25.0 & \bf42.5({\color{higher}+17.5}) & 29.6({\color{higher}+4.6})\\
         
         \bottomrule
    \end{tabular}
    \lesspace
    \lbltab{imprv_detic_swin}
\end{table*}