\begin{table}[!t]
    \centering
    \small
    \tablestyle{1.9pt}{0.8}
    \caption{
    Comparison with CORA~\cite{wu2023cora} and VLDet (\vldet)~\cite{lin2022learning} 
    on \inat and \fsod.
    \shine is applied to the baseline methods, respectively.
    All methods employ ResNet-50~\cite{he2016deep} as backbone. Note that CORA uses only box-annotated COCO~\cite{lin2014microsoft} base split for training, while VLDet uses box-annotated LVIS~\cite{gupta2019lvis} and image-caption-annotated CC3M~\cite{sharma2018conceptual} as supervisory signals. mAP50 (\%) is reported.
    }
    \begin{tabular}{ll|lll|lll|}
         \toprule

         \multirow{2}{*}{\rotatebox[origin=c]{90}{Set}} 
         & \multirow{2}{*}{\rotatebox[origin=c]{90}{Level}} 
         & \multicolumn{1}{c}{\multirow{2}{*}{CORA}} & \multicolumn{1}{c}{\shine} & \multicolumn{1}{c|}{\shine} 
         & \multicolumn{1}{c}{\multirow{2}{*}{\vldet}} & \multicolumn{1}{c}{\shine} & \multicolumn{1}{c|}{\shine} \\
         
         &
         && \multicolumn{1}{c}{(GT-\hierarchy)} & \multicolumn{1}{c|}{(LLM-\hierarchy)}
         &&\multicolumn{1}{c}{(GT-\hierarchy)} & \multicolumn{1}{c|}{(LLM-\hierarchy)}\\

         \cmidrule(r){3-5}
         \cmidrule(r){6-8}
         
         \multirow{6}{*}{\rotatebox[origin=c]{90}{\inat}}

         & L6
         & 31.2 & 54.2({\color{higher}+23.0}) & \bf54.8({\color{higher}+23.6}) 
         & 48.9 & 62.4({\color{higher}+13.5}) & \bf64.8({\color{higher}+15.9})\\ 
								
         & L5
         & 22.6 & \bf51.9({\color{higher}+29.3}) & 35.7({\color{higher}+13.1}) 
         & 44.3 & \bf60.6({\color{higher}+16.3}) & 52.6({\color{higher}+8.3})\\ 
 										
         & L4
         & 21.7 & \bf50.7({\color{higher}+29.0}) & 36.2({\color{higher}+14.5}) 
         & 42.9 & \bf58.7({\color{higher}+15.8}) & 53.6\bf({\color{higher}+10.7})\\  

         & L3
         & 26.0 & \bf50.5({\color{higher}+24.5}) & 43.4({\color{higher}+17.4})
         & 43.8 & \bf63.5({\color{higher}+19.7}) & 58.7({\color{higher}+14.9})\\ 
				
         & L2 
         & 20.0 & \bf33.2({\color{higher}+13.2}) & 24.8({\color{higher}+4.8}) 
         & 34.3 & \bf54.4({\color{higher}+20.1}) & 46.9\bf({\color{higher}+12.6})\\  
 											
         & L1
         & 18.3 & 16.2({\color{lower}-2.1}) & 13.0({\color{lower}-5.3}) 
         & 37.0 & \bf43.2({\color{higher}+6.2}) & 38.6({\color{higher}+1.6})\\ 
         
         \cmidrule(r){3-5}
         \cmidrule(r){6-8}
         
         \multirow{3}{*}{\rotatebox[origin=c]{90}{\fsod}}
         
         & L3
         & 49.3 & \bf51.4({\color{higher}+2.1}) & 51.1({\color{higher}+1.8}) 
         & 48.8 & \bf53.8({\color{higher}+5.0}) & 53.6({\color{higher}+4.8})\\ 
 									
         & L2
         & 21.9 & \bf33.6({\color{higher}+11.7}) & 23.5({\color{higher}+1.6}) 
         & 23.5 & \bf39.3({\color{higher}+15.8}) & 29.8({\color{higher}+6.3})\\ 
         
         & L1
         & 11.6 & \bf26.2({\color{higher}+14.6}) & 14.1({\color{higher}+2.5})
         & 12.8 & \bf31.0({\color{higher}+18.2}) & 17.3({\color{higher}+4.5})\\  
         
         \bottomrule
    \end{tabular}
    \lesspace
    \lbltab{imprv_vldet_cora_rn50}
\end{table}
