\begin{table}[!t]
    \centering
    \small
    \tablestyle{1.9pt}{0.8}
    \caption{
    Comparison with CoDet~\cite{ma2024codet} and VLDet (\vldet)~\cite{lin2022learning} 
    on \inat and \fsod.
    \shine is applied to the baseline methods, respectively.
    All methods employ Swin-B~\cite{liu2021swin} as backbone. Box-annotated LVIS~\cite{gupta2019lvis} and image-caption-annotated CC3M~\cite{sharma2018conceptual} are used as supervisory signals. mAP50 (\%) is reported.
    }
    \begin{tabular}{ll|lll|lll|}
         \toprule

         \multirow{2}{*}{\rotatebox[origin=c]{90}{Set}} 
         & \multirow{2}{*}{\rotatebox[origin=c]{90}{Level}} 
         & \multicolumn{1}{c}{\multirow{2}{*}{CoDet}} & \multicolumn{1}{c}{\shine} & \multicolumn{1}{c|}{\shine} 
         & \multicolumn{1}{c}{\multirow{2}{*}{\vldet}} & \multicolumn{1}{c}{\shine} & \multicolumn{1}{c|}{\shine} \\
         
         &
         && \multicolumn{1}{c}{(GT-\hierarchy)} & \multicolumn{1}{c|}{(LLM-\hierarchy)}
         &&\multicolumn{1}{c}{(GT-\hierarchy)} & \multicolumn{1}{c|}{(LLM-\hierarchy)}\\

         \cmidrule(r){3-5}
         \cmidrule(r){6-8}
         
         \multirow{6}{*}{\rotatebox[origin=c]{90}{\inat}}

         & L6
         & 48.7 & \bf80.1({\color{higher}+31.4}) & 75.1({\color{higher}+26.4})
         & 81.7 & \bf84.0({\color{higher}+2.3})  & 83.8({\color{higher}+2.1})\\
								
         & L5 
         & 43.2 & \bf80.9({\color{higher}+37.7}) & 63.1({\color{higher}+19.9})
         & 83.7 & \bf84.7({\color{higher}+1.0})  & 82.1({\color{lower}-1.6})\\
 										
         & L4 
         & 64.0 & \bf80.5({\color{higher}+16.5}) & 73.8({\color{higher}+9.8})
         & 82.1 & 84.5({\color{higher}+2.4}) & \bf85.8({\color{higher}+3.7})\\

         & L3 
         & 56.1 & \bf79.3({\color{higher}+23.2}) & 76.7({\color{higher}+20.6})
         & 77.7 & \bf83.9({\color{higher}+6.2})  & 83.3({\color{higher}+5.6})\\
				
         & L2 
         & 61.3 & 65.3({\color{higher}+4.0}) & \bf66.0({\color{higher}+4.7})
         & 71.2 & 75.2({\color{higher}+4.0}) & \bf77.2({\color{higher}+6.0})\\
 											
         & L1
         & 52.3 & \bf54.9({\color{higher}+2.6}) & 50.4({\color{lower}-1.9})
         & 66.1 & 66.7({\color{higher}+0.6})    & \bf71.2({\color{higher}+5.1})\\
         
         \cmidrule(r){3-5}
         \cmidrule(r){6-8}
         
         \multirow{3}{*}{\rotatebox[origin=c]{90}{\fsod}}
         
         & L3
         & 60.5 & \bf62.5({\color{higher}+2.0}) & 61.6({\color{higher}+1.1})
         & 60.5 & \bf63.7({\color{higher}+3.2}) & 63.3({\color{higher}+2.8})\\
 									
         & L2 
         & 33.5 & \bf48.5({\color{higher}+15.0}) & 36.6({\color{higher}+3.1})
         & 33.9 & \bf49.2({\color{higher}+15.3}) & 37.4({\color{higher}+3.5})\\
         
         & L1
         & 19.9 & \bf39.7({\color{higher}+19.8}) & 25.4({\color{higher}+5.5})
         & 20.8 & \bf41.6({\color{higher}+20.8}) & 26.2({\color{higher}+5.4})\\
         
         \bottomrule
    \end{tabular}
    \lesspace
    \lbltab{imprv_vldet_codet_swinb}
\end{table}
