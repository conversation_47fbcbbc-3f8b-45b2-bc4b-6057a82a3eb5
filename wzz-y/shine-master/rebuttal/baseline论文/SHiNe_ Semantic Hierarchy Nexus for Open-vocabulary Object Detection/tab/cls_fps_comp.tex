\begin{table}
    \centering
    \small
    \tablestyle{3.5pt}{0.95}
    \caption{
    ImageNet-1k~\cite{deng2009imagenet} zero-shot classification. We compare with two state-of-the-art hierarchy-based methods under WordNet (WDN) and LLM-generated hierarchies. Vanilla CLIP~\cite{radford2021learning} 
    serves as the baseline. We report top-1 accuracy, and FPS measured on the same NVIDIA RTX 2070 GPU with a batch size 1 and averaged over 10 runs. \dag: For fair comparison, we reproduce H-CLIP's results without its uncertainty estimation step and its \textit{refined} WordNet hierarchy. In the original H-CLIP paper, a top-1 accuracy of 67.78\% on ImageNet-1k was achieved using ViT-B/16 encoders.
    }
    \begin{tabular}{ll|ll|ll|ll}
         \toprule
         \multicolumn{2}{l|}{}
         & \multicolumn{2}{c|}{ViT-B/32} & \multicolumn{2}{c|}{ViT-B/16} & \multicolumn{2}{c}{ViT-L/14} \\

         \cline{3-4}
         \cline{5-6}
         \cline{7-8}

         &
         & \multicolumn{1}{c}{Acc(\%)} & \multicolumn{1}{c|}{FPS} 
         & \multicolumn{1}{c}{Acc(\%)} & \multicolumn{1}{c|}{FPS} 
         & \multicolumn{1}{c}{Acc(\%)} & \multicolumn{1}{c}{FPS} \\
        
         \hline
         \rowcolor{baselineRowColor} &  CLIP
         & 58.9 & 150
         & 63.9 & 152
         & 72.0 & 81 \\
         
         \hline
         
         \multirow{3}{*}{\rotatebox[origin=c]{90}{WDN}}
         
         & H-CLIP\dag
         & 58.7({\color{lower}-0.2}) & 3
         & 63.8({\color{lower}-0.1}) & 3 
         & 70.6({\color{lower}-1.4}) & 2 \\
         
         & CHiLS
         & 59.6({\color{higher}+0.7}) & 27
         & 64.6({\color{higher}+0.7}) & 28
         & 72.1({\color{higher}+0.1}) & 23 \\
         
         & \shine 
         & \bf60.3({\color{higher}+1.4}) & 142
         & \bf65.5({\color{higher}+1.6}) & 150
         & \bf73.1({\color{higher}+1.1}) & 81\\
		
	\hline
 
         \multirow{3}{*}{\rotatebox[origin=c]{90}{LLM}}
         
         & H-CLIP
         & 55.8({\color{lower}-3.1}) & 2
         & 60.1({\color{lower}-3.8}) & 2
         & 66.9({\color{lower}-5.1}) & 1 \\
         
         & CHiLS 
         & 61.1({\color{higher}+2.2}) & 26
         & 66.1({\color{higher}+2.2}) & 27
         & 73.4({\color{higher}+1.4}) & 23\\
         
         & \shine 
         & \bf61.6({\color{higher}+2.7}) & 141
         & \bf66.7({\color{higher}+2.8}) & 149
         & \bf73.6({\color{higher}+1.6}) & 81\\
         \bottomrule
    \end{tabular}
    \lesspace
    \lbltab{cls_comp}
\end{table}