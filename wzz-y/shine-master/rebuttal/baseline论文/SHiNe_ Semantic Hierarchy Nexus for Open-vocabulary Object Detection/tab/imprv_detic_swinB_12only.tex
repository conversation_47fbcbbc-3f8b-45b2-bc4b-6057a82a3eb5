\begin{table}[!ht]
    \centering
    \small
    \tablestyle{2.1pt}{1}
    \caption{Additional results are provided for Detic~\cite{zhou2022detecting} with a Swin-B backbone, trained using both \textbf{I}-LVIS and \textbf{II}-LVIS+IN-L supervisory signal combinations. Detection performance across varying label granularity levels on \inat \textbf{(upper)} and \fsod \textbf{(lower)} datasets are reported. \shine is directly applied to the baseline detector (BL)~\cite{zhou2022detecting} with ground-truth (GT-\hierarchy) and LLM-generated (LLM-\hierarchy) hierarchies. mAP50 (\%) is reported.
    }
    
    \begin{tabular}{ll|lll|lll|}
         \toprule
         &\multicolumn{1}{c}{}
         & \multicolumn{6}{c}{Swin-B Backbone} \\
         
         \cmidrule(r){3-8}
         
         &\multicolumn{1}{c}{}
         & \multicolumn{3}{c}{\textbf{I} - LVIS} 
         & \multicolumn{3}{c}{\textbf{II} - LVIS + IN-L} \\
         
         \cmidrule(r){3-5}
         \cmidrule(r){6-8}

         \multirow{2}{*}{\rotatebox[origin=c]{90}{Set}} & \multirow{2}{*}{\rotatebox[origin=c]{90}{Level}} 
         & \multicolumn{1}{c}{\multirow{2}{*}{BL}} & \multicolumn{1}{c}{\shine} & \multicolumn{1}{c|}{\shine} 
         & \multicolumn{1}{c}{\multirow{2}{*}{BL}} & \multicolumn{1}{c}{\shine} & \multicolumn{1}{c|}{\shine} \\
         
         &
         && \multicolumn{1}{c}{(GT-\hierarchy)} & \multicolumn{1}{c|}{(LLM-\hierarchy)} 
         && \multicolumn{1}{c}{(GT-\hierarchy)} & \multicolumn{1}{c|}{(LLM-\hierarchy)} \\

         \cmidrule(r){3-5}
         \cmidrule(r){6-8}
         
         \multirow{6}{*}{\rotatebox[origin=c]{90}{\inat}}

         & L6
         & 52.1 & \bf76.7({\color{higher}+24.6}) & 74.0({\color{higher}+21.9}) 
         & 50.9 & \bf83.8({\color{higher}+32.9}) & 78.8({\color{higher}+27.9}) \\
								
         & L5 
         & 49.4 & \bf77.7({\color{higher}+28.3}) & 68.4({\color{higher}+19.0}) 
         & 45.6 & \bf84.8({\color{higher}+39.2}) & 69.0({\color{higher}+23.4}) \\
 										
         & L4 
         & 64.3 & \bf76.6({\color{higher}+12.3}) & 73.7({\color{higher}+9.4})
         & 63.8 & \bf84.4({\color{higher}+20.6})    & 79.3({\color{higher}+15.5}) \\
										
         & L3 
         & 54.7 & \bf75.3({\color{higher}+20.6}) & 73.2({\color{higher}+18.5})
         & 52.5 & \bf83.0({\color{higher}+30.5}) & 78.7({\color{higher}+26.2}) \\
				
         & L2 
         & 54.3 & 62.1({\color{higher}+7.8}) & \bf62.8({\color{higher}+8.5}) 
         & 60.4 & 73.1({\color{higher}+12.7}) & \bf75.1({\color{higher}+14.7}) \\
 											
         & L1
         & 49.4 & 49.7({\color{higher}+0.3}) & \bf50.7({\color{higher}+1.3}) 
         & 49.7 & \bf59.4({\color{higher}+9.7}) & 49.8({\color{higher}+0.1}) \\
         
         \cmidrule(r){3-5}
         \cmidrule(r){6-8}
         
         \multirow{3}{*}{\rotatebox[origin=c]{90}{\fsod}}
 										
         & L3
         & 58.6 & \bf61.1({\color{higher}+2.5}) & 61.0({\color{higher}+2.4}) 
         & 60.4 & \bf62.7({\color{higher}+2.3}) & 62.1({\color{higher}+1.7}) \\
 									
         & L2 
         & 32.2 & \bf46.5({\color{higher}+14.3}) & 35.6({\color{higher}+3.4}) 
         & 31.6 & \bf46.6({\color{higher}+15.0}) & 33.5({\color{higher}+1.9}) \\
         
         & L1\dag 
         & 18.0 & \bf38.5({\color{higher}+20.5}) & 23.7({\color{higher}+5.7}) 
         & 18.2 & \bf35.9({\color{higher}+17.7}) & 22.3({\color{higher}+4.1}) \\
         
         \bottomrule
    \end{tabular}
    \lesspace
    \lbltab{imprv_detic_swinB_12only}
\end{table}