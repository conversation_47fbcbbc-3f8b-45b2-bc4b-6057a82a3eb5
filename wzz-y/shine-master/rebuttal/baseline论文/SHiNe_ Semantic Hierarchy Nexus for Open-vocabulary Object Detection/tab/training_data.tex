\begin{table}[!h]
    \centering
    \footnotesize
    \tablestyle{8pt}{0.8}
    \caption{
    Training signal combinations. LVIS~\cite{gupta2019lvis} and COCO~\cite{lin2014microsoft} are used as strong box-level supervision. ImageNet-21k~\cite{deng2009imagenet} (IN-21k) and the 997-class subset (IN-L) of ImageNet-21k that overlaps with LVIS are used as weak image-level supervision.
    }
    \begin{tabular}{c|cc}
         \toprule
          Notation & Strong Supervision & Weak Supervision\\
         \midrule
         \textbf{I}   & LVIS & N/A           \\
         \textbf{II}  & LVIS & IN-L          \\
         \textbf{III} & LVIS & IN-21k        \\
         \textbf{IV}  & LVIS \& COCO & IN-21k \\
         \bottomrule
    \end{tabular}
    \lbltab{training_data}
    \lesspace
\end{table}