\begin{table}[!hhh]
    \centering
    \small
    \tablestyle{5.1pt}{1}
    \caption{
    Comparison of detection performance on COCO and LVIS benchmarks using the OVE protocol. We use Detic~\cite{zhou2022detecting} with a ResNet-50 backbone as the baseline detector (BL). \shine is applied to the baseline using hierarchies generated by \llm{}. All models receive strong supervision on the base class partitions of both datasets, with box-class annotations. A comparison of different weak supervisory signals is also included. mAP50$_{novel}$ and mAP$_{novel}$ denote performance evaluated on the novel class partitions (17 classes for COCO and 337 classes for LVIS), while mAP50$_{all}$ and mAP$_{all}$ represent evaluations on both base and novel classes (65 classes for COCO and 1203 classes for LVIS).
    }
    \begin{tabular}{ll|ll|ll|}
         \toprule
         
         \multirow{5}{*}{\rotatebox[origin=c]{90}{COCO}}& \multicolumn{1}{c}{\multirow{2}{*}{$\dataWeak$}} & \multicolumn{2}{c}{mAP50$_{novel}$} & \multicolumn{2}{c}{mAP50$_{all}$} \\
         \cmidrule(r){3-4}
         \cmidrule(r){5-6}
         &  & \multicolumn{1}{c}{BL} & \multicolumn{1}{c|}{\shine} & \multicolumn{1}{c}{BL} & \multicolumn{1}{c|}{\shine} \\
         \cmidrule(r){2-2}
         \cmidrule(r){3-4}
         \cmidrule(r){5-6}
         & N/A
         & 1.3 & \bf3.2({\color{higher}+1.9}) & 39.3 & \bf39.8({\color{higher}+0.5}) \\
         & COCO Captions 
         & 24.0 & \bf24.3({\color{higher}+0.3}) & 44.8 & \bf44.9({\color{higher}+0.1}) \\
         
         \hline
         
         \multirow{6}{*}{\rotatebox[origin=c]{90}{LVIS}}& \multicolumn{1}{c}{\multirow{2}{*}{$\dataWeak$}} & \multicolumn{2}{c}{mAP$_{novel}$} & \multicolumn{2}{c}{mAP$_{all}$} \\
         
         \cmidrule(r){3-4}
         \cmidrule(r){5-6}
         
         &  & \multicolumn{1}{c}{BL} & \multicolumn{1}{c|}{\shine} & \multicolumn{1}{c}{BL} & \multicolumn{1}{c|}{\shine} \\
         \cmidrule(r){2-2}
         \cmidrule(r){3-4}
         \cmidrule(r){5-6}
         & N/A 
         & 17.6 & \bf20.9({\color{higher}+3.3}) & 33.3 & \bf33.6({\color{higher}+0.3}) \\
         & IN-L
         & \bf26.7 & 25.5({\color{lower}-1.2}) & \bf35.8 & 35.3({\color{lower}-0.5}) \\
         & Conceptual Captions 
         & 19.3 & \bf21.5({\color{higher}+2.2}) & 33.4 & \bf33.5({\color{higher}+0.1}) \\
         \bottomrule
    \end{tabular}

    \lbltab{base_novel_comp}
\end{table}