import numpy as np
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
from sklearn.metrics.pairwise import cosine_distances, euclidean_distances
import umap


def plot_embeddings(embeddings, labels, method="pca", level_name="Level"):
    if method == "pca":
        reducer = PCA(n_components=3)
        title = f'PCA Visualization of {level_name}'
    elif method == "tsne":
        reducer = TSNE(n_components=3, random_state=42, perplexity=30, n_iter=1000)
        title = f't-SNE Visualization of {level_name}'
    elif method == "umap":
        reducer = umap.UMAP(n_components=3, random_state=42)
        title = f'UMAP Visualization of {level_name}'
    else:
        raise ValueError(f"Unknown method: {method}")

    reduced_embeddings = reducer.fit_transform(embeddings)

    plt.figure(figsize=(10, 8))
    scatter = plt.scatter(reduced_embeddings[:, 0], reduced_embeddings[:, 1], c=labels, cmap='tab10', alpha=0.7)
    plt.colorbar(scatter, label='Classes')
    plt.title(title)
    plt.show()


def calculate_centroid_distances(embeddings, labels, distance_metric="euclidean"):
    unique_labels = np.unique(labels)
    centroids = []
    for label in unique_labels:
        class_embeddings = embeddings[labels == label]
        centroid = np.mean(class_embeddings, axis=0)
        centroids.append(centroid)

    centroids = np.array(centroids)

    if distance_metric == "euclidean":
        dist_matrix = euclidean_distances(centroids)
    elif distance_metric == "cosine":
        dist_matrix = cosine_distances(centroids)
    else:
        raise ValueError(f"Unknown distance metric: {distance_metric}")

    print(f"Centroid Distance Matrix ({distance_metric}):")
    print(dist_matrix)


# Load the embeddings
level_name = "l1"  # Choose the level to visualize
embeddings = np.load(f'nexinat/rn50/baseline/inat_clip_hrchy_{level_name}.npy')

# Here you can assign labels for the embeddings (use your actual class labels)
# For example, using a simple range as placeholders
num_classes = embeddings.shape[0]
labels = np.arange(num_classes)

# Visualize using PCA, t-SNE or UMAP
plot_embeddings(embeddings, labels, method="pca", level_name=level_name)
plot_embeddings(embeddings, labels, method="tsne", level_name=level_name)
plot_embeddings(embeddings, labels, method="umap", level_name=level_name)

# Calculate centroid distances between classes
calculate_centroid_distances(embeddings, labels, distance_metric="euclidean")
calculate_centroid_distances(embeddings, labels, distance_metric="cosine")
