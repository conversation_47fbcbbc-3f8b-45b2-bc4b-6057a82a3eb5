{"0": {"node_name": "whale", "node_id_at_leaf": -1, "parent_names": ["aquatic mammal"], "child_names": ["grey whale, gray whale, devilfish, <PERSON><PERSON><PERSON><PERSON> gibbosus, <PERSON><PERSON><PERSON><PERSON> robustus", "killer whale, killer, orca, grampus, sea wolf, Orcinus orca"], "child_labels": [148, 147]}, "1": {"node_name": "rapeseed", "node_id_at_leaf": 984, "parent_names": ["fruit"], "child_names": [], "child_labels": []}, "2": {"node_name": "ballplayer, baseball player", "node_id_at_leaf": 981, "parent_names": ["person, individual, someone, somebody, mortal, soul"], "child_names": [], "child_labels": []}, "3": {"node_name": "ship", "node_id_at_leaf": -1, "parent_names": ["vessel, watercraft"], "child_names": ["pirate, pirate ship", "schooner", "wreck", "passenger ship", "cargo ship, cargo vessel", "warship, war vessel, combat ship"], "child_labels": [780, 403, 913, 724, 628, 510]}, "4": {"node_name": "CD player", "node_id_at_leaf": 485, "parent_names": ["electronic equipment"], "child_names": [], "child_labels": []}, "5": {"node_name": "iron, smoothing iron", "node_id_at_leaf": 606, "parent_names": ["home appliance, household appliance"], "child_names": [], "child_labels": []}, "6": {"node_name": "sloth, tree sloth", "node_id_at_leaf": -1, "parent_names": ["edentate"], "child_names": ["three-toed sloth, ai, Bradypus tridactylus"], "child_labels": [364]}, "7": {"node_name": "lizard", "node_id_at_leaf": -1, "parent_names": ["saurian"], "child_names": ["chameleon, chamaeleon", "venomous lizard", "teiid lizard, teiid", "iguanid, iguanid lizard", "gecko", "agamid, agamid lizard", "anguid lizard", "lacertid lizard, lacertid", "monitor, monitor lizard, varan"], "child_labels": [48, 42, 46, 40, 45, 39, 44, 41, 38, 47, 43]}, "8": {"node_name": "albatross, mollymawk", "node_id_at_leaf": 146, "parent_names": ["aquatic bird"], "child_names": [], "child_labels": []}, "9": {"node_name": "crab", "node_id_at_leaf": -1, "parent_names": ["crustacean"], "child_names": ["Dungeness crab, Cancer magister", "rock crab, Cancer irroratus", "fiddler crab", "king crab, Alaska crab, Alaskan king crab, Alaska king crab, Paralithodes camtschatica"], "child_labels": [119, 120, 118, 121]}, "10": {"node_name": "tiger, Panthera tigris", "node_id_at_leaf": 292, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "11": {"node_name": "salamander", "node_id_at_leaf": -1, "parent_names": ["dummy8"], "child_names": ["European fire salamander, <PERSON><PERSON>dra salamandra", "spotted salamander, Ambystoma maculatum", "axolo<PERSON>, mud puppy, Ambystoma mexicanum", "newt, triton"], "child_labels": [25, 28, 29, 26, 27]}, "12": {"node_name": "reel", "node_id_at_leaf": 758, "parent_names": ["tool"], "child_names": [], "child_labels": []}, "13": {"node_name": "plate rack", "node_id_at_leaf": 729, "parent_names": ["kitchen utensil"], "child_names": [], "child_labels": []}, "14": {"node_name": "marmot", "node_id_at_leaf": 336, "parent_names": ["rodent, gnawer"], "child_names": [], "child_labels": []}, "15": {"node_name": "bee eater", "node_id_at_leaf": 92, "parent_names": ["coraciiform bird"], "child_names": [], "child_labels": []}, "16": {"node_name": "stew", "node_id_at_leaf": -1, "parent_names": ["dish"], "child_names": ["hot pot, hotpot"], "child_labels": [926]}, "17": {"node_name": "dummy49", "node_id_at_leaf": -1, "parent_names": ["building, edifice"], "child_names": ["restaurant, eating house, eating place, eatery"], "child_labels": [762]}, "18": {"node_name": "warthog", "node_id_at_leaf": 343, "parent_names": ["ungulate, hoofed mammal"], "child_names": [], "child_labels": []}, "19": {"node_name": "towel", "node_id_at_leaf": -1, "parent_names": ["piece of cloth, piece of material"], "child_names": ["bath towel", "paper towel"], "child_labels": [700, 434]}, "20": {"node_name": "washer, automatic washer, washing machine", "node_id_at_leaf": 897, "parent_names": ["home appliance, household appliance"], "child_names": [], "child_labels": []}, "21": {"node_name": "hair spray", "node_id_at_leaf": 585, "parent_names": ["cosmetic"], "child_names": [], "child_labels": []}, "22": {"node_name": "lemur", "node_id_at_leaf": -1, "parent_names": ["primate"], "child_names": ["Madagascar cat, ring-tailed lemur, Lemur catta", "indri, indris, <PERSON>dr<PERSON> indri, Indri brevicaudatus"], "child_labels": [383, 384]}, "23": {"node_name": "crane", "node_id_at_leaf": 134, "parent_names": ["aquatic bird"], "child_names": [], "child_labels": []}, "24": {"node_name": "burrito", "node_id_at_leaf": 965, "parent_names": ["dish"], "child_names": [], "child_labels": []}, "25": {"node_name": "coral reef", "node_id_at_leaf": 973, "parent_names": ["reef"], "child_names": [], "child_labels": []}, "26": {"node_name": "cracker", "node_id_at_leaf": -1, "parent_names": ["baked goods"], "child_names": ["pretzel"], "child_labels": [932]}, "27": {"node_name": "flamingo", "node_id_at_leaf": 130, "parent_names": ["aquatic bird"], "child_names": [], "child_labels": []}, "28": {"node_name": "salmon", "node_id_at_leaf": -1, "parent_names": ["bony fish"], "child_names": ["coho, cohoe, coho salmon, blue jack, silver salmon, Oncorhynchus kisutch"], "child_labels": [391]}, "29": {"node_name": "horse, Equus caballus", "node_id_at_leaf": -1, "parent_names": ["ungulate, hoofed mammal"], "child_names": ["sorrel"], "child_labels": [339]}, "30": {"node_name": "beetle", "node_id_at_leaf": -1, "parent_names": ["insect"], "child_names": ["tiger beetle", "ladybug, ladybeetle, lady beetle, ladybird, ladybird beetle", "ground beetle, carabid beetle", "long-horned beetle, longicorn, longicorn beetle", "leaf beetle, chrysomelid", "weevil", "scarabaeid beetle, scarabaeid, scarabaean"], "child_labels": [300, 303, 307, 306, 302, 305, 304, 301]}, "31": {"node_name": "zebra", "node_id_at_leaf": 340, "parent_names": ["ungulate, hoofed mammal"], "child_names": [], "child_labels": []}, "32": {"node_name": "quilt, comforter, comfort, puff", "node_id_at_leaf": 750, "parent_names": ["bedclothes, bed clothing, bedding"], "child_names": [], "child_labels": []}, "33": {"node_name": "artichoke, globe artichoke", "node_id_at_leaf": 944, "parent_names": ["vegetable, veggie, veg"], "child_names": [], "child_labels": []}, "34": {"node_name": "boat", "node_id_at_leaf": -1, "parent_names": ["vessel, watercraft"], "child_names": ["canoe", "fireboat", "gondola", "lifeboat", "speedboat", "yawl", "sailboat, sailing boat"], "child_labels": [554, 576, 484, 814, 625, 472, 871, 914]}, "35": {"node_name": "sock", "node_id_at_leaf": 806, "parent_names": ["footwear, legwear"], "child_names": [], "child_labels": []}, "36": {"node_name": "sheath", "node_id_at_leaf": -1, "parent_names": ["dummy39"], "child_names": ["holster", "scabbard"], "child_labels": [597, 777]}, "37": {"node_name": "stage", "node_id_at_leaf": 819, "parent_names": ["area"], "child_names": [], "child_labels": []}, "38": {"node_name": "domestic cat, house cat, Felis domesticus, Felis catus", "node_id_at_leaf": -1, "parent_names": ["carnivore"], "child_names": ["tabby, tabby cat", "tiger cat", "Persian cat", "Siamese cat, Siamese", "Egyptian cat"], "child_labels": [284, 282, 283, 281, 285]}, "39": {"node_name": "orange", "node_id_at_leaf": 950, "parent_names": ["fruit"], "child_names": [], "child_labels": []}, "40": {"node_name": "microscope", "node_id_at_leaf": -1, "parent_names": ["scientific instrument"], "child_names": ["hand glass, simple microscope, magnifying glass"], "child_labels": [633]}, "41": {"node_name": "stringed instrument", "node_id_at_leaf": -1, "parent_names": ["musical instrument, instrument"], "child_names": ["banjo", "cello, violoncello", "harp", "violin, fiddle", "guitar"], "child_labels": [420, 546, 486, 594, 889, 402]}, "42": {"node_name": "flatworm, platyhelminth", "node_id_at_leaf": 110, "parent_names": ["dummy14"], "child_names": [], "child_labels": []}, "43": {"node_name": "glove", "node_id_at_leaf": -1, "parent_names": ["handwear, hand wear"], "child_names": ["mitten"], "child_labels": [658]}, "44": {"node_name": "indigo bunting, indigo finch, indigo bird, Passerina cyanea", "node_id_at_leaf": 14, "parent_names": ["passerine, passeriform bird"], "child_names": [], "child_labels": []}, "45": {"node_name": "bridge, span", "node_id_at_leaf": -1, "parent_names": ["dummy73"], "child_names": ["steel arch bridge", "suspension bridge", "viaduct"], "child_labels": [821, 839, 888]}, "46": {"node_name": "sea cow, sirenian mammal, sirenian", "node_id_at_leaf": -1, "parent_names": ["aquatic mammal"], "child_names": ["dugong, Dugong dugon"], "child_labels": [149]}, "47": {"node_name": "place of worship, house of prayer, house of God, house of worship", "node_id_at_leaf": -1, "parent_names": ["building, edifice"], "child_names": ["church, church building", "mosque", "shrine"], "child_labels": [832, 497, 668]}, "48": {"node_name": "broccoli", "node_id_at_leaf": 937, "parent_names": ["vegetable, veggie, veg"], "child_names": [], "child_labels": []}, "49": {"node_name": "snow leopard, ounce, Panthera uncia", "node_id_at_leaf": 289, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "50": {"node_name": "balloon", "node_id_at_leaf": 417, "parent_names": ["aircraft"], "child_names": [], "child_labels": []}, "51": {"node_name": "acarine", "node_id_at_leaf": -1, "parent_names": ["arachnid, arachnoid"], "child_names": ["tick"], "child_labels": [78]}, "52": {"node_name": "scarf", "node_id_at_leaf": -1, "parent_names": ["neckwear"], "child_names": ["feather boa, boa", "stole"], "child_labels": [824, 552]}, "53": {"node_name": "bun, roll", "node_id_at_leaf": -1, "parent_names": ["baked goods"], "child_names": ["bagel, beigel"], "child_labels": [931]}, "54": {"node_name": "chain saw, chainsaw", "node_id_at_leaf": 491, "parent_names": ["tool"], "child_names": [], "child_labels": []}, "55": {"node_name": "grasshopper, hopper", "node_id_at_leaf": 311, "parent_names": ["insect"], "child_names": [], "child_labels": []}, "56": {"node_name": "brambling, Fringilla montifringilla", "node_id_at_leaf": 10, "parent_names": ["passerine, passeriform bird"], "child_names": [], "child_labels": []}, "57": {"node_name": "breakwater, groin, groyne, mole, bulwark, seawall, jetty", "node_id_at_leaf": 460, "parent_names": ["barrier"], "child_names": [], "child_labels": []}, "58": {"node_name": "guinea pig, Cavia co<PERSON>a", "node_id_at_leaf": 338, "parent_names": ["rodent, gnawer"], "child_names": [], "child_labels": []}, "59": {"node_name": "espresso", "node_id_at_leaf": 967, "parent_names": ["coffee, java"], "child_names": [], "child_labels": []}, "60": {"node_name": "bus, autobus, coach, charabanc, double-decker, jitney, motorbus, motorcoach, omnibus, passenger vehicle", "node_id_at_leaf": -1, "parent_names": ["motor vehicle, automotive vehicle"], "child_names": ["minibus", "recreational vehicle, RV, R.V.", "school bus", "trolleybus, trolley coach, trackless trolley"], "child_labels": [654, 779, 874, 757]}, "61": {"node_name": "cock", "node_id_at_leaf": 7, "parent_names": ["dummy0"], "child_names": [], "child_labels": []}, "62": {"node_name": "wombat", "node_id_at_leaf": 106, "parent_names": ["marsupial, pouched mammal"], "child_names": [], "child_labels": []}, "63": {"node_name": "hermit crab", "node_id_at_leaf": 125, "parent_names": ["crustacean"], "child_names": [], "child_labels": []}, "64": {"node_name": "banana", "node_id_at_leaf": 954, "parent_names": ["fruit"], "child_names": [], "child_labels": []}, "65": {"node_name": "damselfish, demoiselle", "node_id_at_leaf": -1, "parent_names": ["bony fish"], "child_names": ["anemone fish"], "child_labels": [393]}, "66": {"node_name": "hen", "node_id_at_leaf": 8, "parent_names": ["dummy1"], "child_names": [], "child_labels": []}, "67": {"node_name": "pineapple, ananas", "node_id_at_leaf": 953, "parent_names": ["fruit"], "child_names": [], "child_labels": []}, "68": {"node_name": "outbuilding", "node_id_at_leaf": -1, "parent_names": ["building, edifice"], "child_names": ["barn", "greenhouse, nursery, glasshouse", "shed"], "child_labels": [410, 425, 449, 580]}, "69": {"node_name": "tray", "node_id_at_leaf": 868, "parent_names": ["kitchen utensil"], "child_names": [], "child_labels": []}, "70": {"node_name": "dock, dockage, docking facility", "node_id_at_leaf": 536, "parent_names": ["landing, landing place"], "child_names": [], "child_labels": []}, "71": {"node_name": "wild boar, boar, Sus scrofa", "node_id_at_leaf": 342, "parent_names": ["ungulate, hoofed mammal"], "child_names": [], "child_labels": []}, "72": {"node_name": "necktie, tie", "node_id_at_leaf": -1, "parent_names": ["neckwear"], "child_names": ["bolo tie, bolo, bola tie, bola", "bow tie, bow-tie, bowtie", "Windsor tie"], "child_labels": [457, 451, 906]}, "73": {"node_name": "walking stick, walkingstick, stick insect", "node_id_at_leaf": 313, "parent_names": ["insect"], "child_names": [], "child_labels": []}, "74": {"node_name": "toucan", "node_id_at_leaf": 96, "parent_names": ["piciform bird"], "child_names": [], "child_labels": []}, "75": {"node_name": "frog, toad, toad frog, anuran, batrachian, salientian", "node_id_at_leaf": -1, "parent_names": ["dummy7"], "child_names": ["tree frog, tree-frog", "tailed frog, bell toad, ribbed toad, tailed toad, Ascaphus trui", "true frog, ranid"], "child_labels": [31, 32, 30]}, "76": {"node_name": "harvestman, daddy longlegs, Phalangium opilio", "node_id_at_leaf": 70, "parent_names": ["arachnid, arachnoid"], "child_names": [], "child_labels": []}, "77": {"node_name": "screw", "node_id_at_leaf": 783, "parent_names": ["tool"], "child_names": [], "child_labels": []}, "78": {"node_name": "spatula", "node_id_at_leaf": 813, "parent_names": ["kitchen utensil"], "child_names": [], "child_labels": []}, "79": {"node_name": "reservoir", "node_id_at_leaf": -1, "parent_names": ["tank, storage tank"], "child_names": ["water tower"], "child_labels": [900]}, "80": {"node_name": "lacewing, lacewing fly", "node_id_at_leaf": 318, "parent_names": ["insect"], "child_names": [], "child_labels": []}, "81": {"node_name": "parrot", "node_id_at_leaf": -1, "parent_names": ["dummy6"], "child_names": ["African grey, African gray, Psittacus erithacus", "macaw", "cockatoo", "lory"], "child_labels": [88, 87, 90, 89]}, "82": {"node_name": "opener", "node_id_at_leaf": -1, "parent_names": ["kitchen utensil"], "child_names": ["can opener, tin opener", "bottle opener"], "child_labels": [512, 473]}, "83": {"node_name": "gar, garfish, garpike, billfish, Lepisosteus osseus", "node_id_at_leaf": 395, "parent_names": ["bony fish"], "child_names": [], "child_labels": []}, "84": {"node_name": "lawn mower, mower", "node_id_at_leaf": 621, "parent_names": ["home appliance, household appliance"], "child_names": [], "child_labels": []}, "85": {"node_name": "theater, theatre, house", "node_id_at_leaf": -1, "parent_names": ["building, edifice"], "child_names": ["cinema, movie theater, movie theatre, movie house, picture palace"], "child_labels": [498]}, "86": {"node_name": "firearm, piece, small-arm", "node_id_at_leaf": -1, "parent_names": ["weapon, arm, weapon system"], "child_names": ["rifle", "pistol, handgun, side arm, shooting iron", "automatic firearm, automatic gun, automatic weapon"], "child_labels": [413, 763, 764]}, "87": {"node_name": "microphone, mike", "node_id_at_leaf": 650, "parent_names": ["electronic equipment"], "child_names": [], "child_labels": []}, "88": {"node_name": "promontory, headland, head, foreland", "node_id_at_leaf": 976, "parent_names": ["dummy69"], "child_names": [], "child_labels": []}, "89": {"node_name": "<PERSON><PERSON><PERSON><PERSON>", "node_id_at_leaf": -1, "parent_names": ["gallinaceous bird, gallinacean"], "child_names": ["peacock", "quail", "partridge"], "child_labels": [84, 85, 86]}, "90": {"node_name": "curtain, drape, drapery, mantle, pall", "node_id_at_leaf": -1, "parent_names": ["dummy56"], "child_names": ["shower curtain", "theater curtain, theatre curtain"], "child_labels": [794, 854]}, "91": {"node_name": "seashore, coast, seacoast, sea-coast", "node_id_at_leaf": 978, "parent_names": ["shore"], "child_names": [], "child_labels": []}, "92": {"node_name": "motorcycle, bike", "node_id_at_leaf": -1, "parent_names": ["motor vehicle, automotive vehicle"], "child_names": ["motor scooter, scooter", "minibike, motorbike"], "child_labels": [670, 665]}, "93": {"node_name": "jewelry, jewellery", "node_id_at_leaf": -1, "parent_names": ["neckwear"], "child_names": ["necklace"], "child_labels": [679]}, "94": {"node_name": "wine, vino", "node_id_at_leaf": -1, "parent_names": ["alcohol, alcoholic drink, alcoholic beverage, intoxicant, inebriant"], "child_names": ["red wine"], "child_labels": [966]}, "95": {"node_name": "mercantile establishment, retail store, sales outlet, outlet", "node_id_at_leaf": -1, "parent_names": ["building, edifice"], "child_names": ["bakery, bakeshop, bakehouse", "barbershop", "bookshop, bookstore, bookstall", "butcher shop, meat market", "confectionery, confectionary, candy store", "grocery store, grocery, food market, market", "shoe shop, shoe-shop, shoe store", "tobacco shop, tobacconist shop, tobacconist", "toyshop"], "child_labels": [860, 865, 788, 454, 582, 424, 467, 509, 415]}, "96": {"node_name": "mantis, mantid", "node_id_at_leaf": 315, "parent_names": ["insect"], "child_names": [], "child_labels": []}, "97": {"node_name": "junco, snowbird", "node_id_at_leaf": 13, "parent_names": ["passerine, passeriform bird"], "child_names": [], "child_labels": []}, "98": {"node_name": "bobsled, bobsleigh, bob", "node_id_at_leaf": 450, "parent_names": ["sled, sledge, sleigh"], "child_names": [], "child_labels": []}, "99": {"node_name": "potpie", "node_id_at_leaf": 964, "parent_names": ["dish"], "child_names": [], "child_labels": []}, "100": {"node_name": "hamster", "node_id_at_leaf": 333, "parent_names": ["rodent, gnawer"], "child_names": [], "child_labels": []}, "101": {"node_name": "geyser", "node_id_at_leaf": 974, "parent_names": ["spring, fountain, outflow, outpouring, natural spring"], "child_names": [], "child_labels": []}, "102": {"node_name": "snipe", "node_id_at_leaf": -1, "parent_names": ["aquatic bird"], "child_names": ["dowitcher"], "child_labels": [142]}, "103": {"node_name": "ostrich, <PERSON><PERSON><PERSON>o camelus", "node_id_at_leaf": 9, "parent_names": ["ratite, ratite bird, flightless bird"], "child_names": [], "child_labels": []}, "104": {"node_name": "slug", "node_id_at_leaf": 114, "parent_names": ["gastropod, univalve"], "child_names": [], "child_labels": []}, "105": {"node_name": "cougar, puma, catamount, mountain lion, painter, panther, Felis concolor", "node_id_at_leaf": 286, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "106": {"node_name": "brace", "node_id_at_leaf": -1, "parent_names": ["neckwear"], "child_names": ["neck brace"], "child_labels": [678]}, "107": {"node_name": "hand blower, blow dryer, blow drier, hair dryer, hair drier", "node_id_at_leaf": 589, "parent_names": ["dryer, drier"], "child_names": [], "child_labels": []}, "108": {"node_name": "dragonfly, darning needle, devil's darning needle, sewing needle, snake feeder, snake doctor, mosquito hawk, skeeter hawk", "node_id_at_leaf": 319, "parent_names": ["insect"], "child_names": [], "child_labels": []}, "109": {"node_name": "hairpiece, false hair, postiche", "node_id_at_leaf": -1, "parent_names": ["headdress, headgear"], "child_names": ["wig"], "child_labels": [903]}, "110": {"node_name": "shirt", "node_id_at_leaf": -1, "parent_names": ["garment"], "child_names": ["jersey, T-shirt, tee shirt"], "child_labels": [610]}, "111": {"node_name": "spoon", "node_id_at_leaf": -1, "parent_names": ["tableware"], "child_names": ["wooden spoon"], "child_labels": [910]}, "112": {"node_name": "soup", "node_id_at_leaf": -1, "parent_names": ["dish"], "child_names": ["consomme"], "child_labels": [925]}, "113": {"node_name": "fountain", "node_id_at_leaf": 562, "parent_names": ["dummy67"], "child_names": [], "child_labels": []}, "114": {"node_name": "strawberry", "node_id_at_leaf": 949, "parent_names": ["fruit"], "child_names": [], "child_labels": []}, "115": {"node_name": "earthstar", "node_id_at_leaf": 995, "parent_names": ["basidiomycete, basidiomycetous fungi"], "child_names": [], "child_labels": []}, "116": {"node_name": "sea cucumber, holothurian", "node_id_at_leaf": 329, "parent_names": ["dummy12"], "child_names": [], "child_labels": []}, "117": {"node_name": "seal", "node_id_at_leaf": -1, "parent_names": ["aquatic mammal"], "child_names": ["sea lion"], "child_labels": [150]}, "118": {"node_name": "modem", "node_id_at_leaf": 662, "parent_names": ["electronic equipment"], "child_names": [], "child_labels": []}, "119": {"node_name": "gyromitra", "node_id_at_leaf": 993, "parent_names": ["ascomycete"], "child_names": [], "child_labels": []}, "120": {"node_name": "wind instrument, wind", "node_id_at_leaf": -1, "parent_names": ["musical instrument, instrument"], "child_names": ["bassoon", "cornet, horn, trumpet, trump", "flute, transverse flute", "French horn, horn", "harmonica, mouth organ, harp, mouth harp", "oboe, hautboy, hautbois", "ocarina, sweet potato", "sax, saxophone", "trombone", "pipe"], "child_labels": [593, 683, 776, 432, 699, 875, 566, 684, 558, 513]}, "121": {"node_name": "mongoose", "node_id_at_leaf": 298, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "122": {"node_name": "memorial, monument", "node_id_at_leaf": -1, "parent_names": ["dummy77"], "child_names": ["brass, memorial tablet, plaque", "megalith, megalithic structure", "triumphal arch"], "child_labels": [458, 649, 873]}, "123": {"node_name": "car, auto, automobile, machine, motorcar", "node_id_at_leaf": -1, "parent_names": ["motor vehicle, automotive vehicle"], "child_names": ["ambulance", "beach wagon, station wagon, wagon, estate car, beach waggon, station waggon, waggon", "cab, hack, taxi, taxicab", "convertible", "go-kart", "golfcart, golf cart", "jeep, landrover", "limousine, limo", "minivan", "Model T", "police van, police wagon, paddy wagon, patrol wagon, wagon, black Maria", "racer, race car, racing car", "sports car, sport car"], "child_labels": [734, 575, 573, 511, 468, 609, 436, 627, 656, 661, 407, 751, 817]}, "124": {"node_name": "projector", "node_id_at_leaf": 745, "parent_names": ["electronic equipment"], "child_names": [], "child_labels": []}, "125": {"node_name": "sandwich", "node_id_at_leaf": -1, "parent_names": ["dish"], "child_names": ["hotdog, hot dog, red hot", "hamburger, beefburger, burger"], "child_labels": [934, 933]}, "126": {"node_name": "fan", "node_id_at_leaf": -1, "parent_names": ["home appliance, household appliance"], "child_names": ["electric fan, blower"], "child_labels": [545]}, "127": {"node_name": "grouse", "node_id_at_leaf": -1, "parent_names": ["gallinaceous bird, gallinacean"], "child_names": ["black grouse", "ptarmigan", "ruffed grouse, partridge, Bonasa umbellus", "prairie chicken, prairie grouse, prairie fowl"], "child_labels": [83, 80, 81, 82]}, "128": {"node_name": "hawk", "node_id_at_leaf": -1, "parent_names": ["bird of prey, raptor, raptorial bird"], "child_names": ["kite"], "child_labels": [21]}, "129": {"node_name": "owl, bird of <PERSON><PERSON>, bird of night, hooter", "node_id_at_leaf": -1, "parent_names": ["bird of prey, raptor, raptorial bird"], "child_names": ["great grey owl, great gray owl, Strix nebulosa"], "child_labels": [24]}, "130": {"node_name": "fly", "node_id_at_leaf": 308, "parent_names": ["insect"], "child_names": [], "child_labels": []}, "131": {"node_name": "camel", "node_id_at_leaf": -1, "parent_names": ["ungulate, hoofed mammal"], "child_names": ["Arabian camel, dromedary, Camelus dromedarius"], "child_labels": [354]}, "132": {"node_name": "sandpiper", "node_id_at_leaf": -1, "parent_names": ["aquatic bird"], "child_names": ["red-backed sandpiper, dunlin, Erolia alpina", "<PERSON>shank, Tringa totanus"], "child_labels": [140, 141]}, "133": {"node_name": "phalanger, opossum, possum", "node_id_at_leaf": -1, "parent_names": ["marsupial, pouched mammal"], "child_names": ["koala, koala bear, kangaroo bear, native bear, Phascolarctos cinereus"], "child_labels": [105]}, "134": {"node_name": "dress, frock", "node_id_at_leaf": -1, "parent_names": ["garment"], "child_names": ["gown"], "child_labels": [578]}, "135": {"node_name": "abacus", "node_id_at_leaf": 398, "parent_names": ["scientific instrument"], "child_names": [], "child_labels": []}, "136": {"node_name": "bib", "node_id_at_leaf": 443, "parent_names": ["neckwear"], "child_names": [], "child_labels": []}, "137": {"node_name": "carpenter's kit, tool kit", "node_id_at_leaf": 477, "parent_names": ["tool"], "child_names": [], "child_labels": []}, "138": {"node_name": "swimsuit, swimwear, bathing suit, swimming costume, bathing costume", "node_id_at_leaf": -1, "parent_names": ["garment"], "child_names": ["bikini, two-piece", "maillot, tank suit", "swimming trunks, bathing trunks"], "child_labels": [639, 445, 842]}, "139": {"node_name": "spoonbill", "node_id_at_leaf": 129, "parent_names": ["aquatic bird"], "child_names": [], "child_labels": []}, "140": {"node_name": "cockroach, roach", "node_id_at_leaf": 314, "parent_names": ["insect"], "child_names": [], "child_labels": []}, "141": {"node_name": "water ouzel, dipper", "node_id_at_leaf": 20, "parent_names": ["passerine, passeriform bird"], "child_names": [], "child_labels": []}, "142": {"node_name": "printer", "node_id_at_leaf": 742, "parent_names": ["electronic equipment"], "child_names": [], "child_labels": []}, "143": {"node_name": "valley, vale", "node_id_at_leaf": 979, "parent_names": ["dummy71"], "child_names": [], "child_labels": []}, "144": {"node_name": "bison", "node_id_at_leaf": 347, "parent_names": ["ungulate, hoofed mammal"], "child_names": [], "child_labels": []}, "145": {"node_name": "wolf", "node_id_at_leaf": -1, "parent_names": ["carnivore"], "child_names": ["timber wolf, grey wolf, gray wolf, Canis lupus", "white wolf, Arctic wolf, Canis lupus tundrarum", "red wolf, maned wolf, Canis rufus, Canis niger", "coyote, prairie wolf, brush wolf, Canis latrans"], "child_labels": [271, 269, 270, 272]}, "146": {"node_name": "refrigerator, icebox", "node_id_at_leaf": 760, "parent_names": ["home appliance, household appliance"], "child_names": [], "child_labels": []}, "147": {"node_name": "spider", "node_id_at_leaf": -1, "parent_names": ["arachnid, arachnoid"], "child_names": ["black and gold garden spider, <PERSON><PERSON><PERSON><PERSON> aurantia", "barn spider, Araneus cavaticus", "garden spider, Aranea diademata", "black widow, <PERSON><PERSON><PERSON><PERSON> mactans", "tarantula", "wolf spider, hunting spider"], "child_labels": [73, 76, 75, 72, 74, 77]}, "148": {"node_name": "binoculars, field glasses, opera glasses", "node_id_at_leaf": 447, "parent_names": ["scientific instrument"], "child_names": [], "child_labels": []}, "149": {"node_name": "shovel", "node_id_at_leaf": 792, "parent_names": ["tool"], "child_names": [], "child_labels": []}, "150": {"node_name": "keyboard instrument", "node_id_at_leaf": -1, "parent_names": ["musical instrument, instrument"], "child_names": ["accordion, piano accordion, squeeze box", "organ, pipe organ", "piano, pianoforte, forte-piano"], "child_labels": [579, 881, 401, 687]}, "151": {"node_name": "pin", "node_id_at_leaf": -1, "parent_names": ["tool"], "child_names": ["safety pin"], "child_labels": [772]}, "152": {"node_name": "meter", "node_id_at_leaf": -1, "parent_names": ["measuring instrument, measuring system, measuring device"], "child_names": ["odometer, hodometer, mileometer, milometer"], "child_labels": [685]}, "153": {"node_name": "mouse, computer mouse", "node_id_at_leaf": 673, "parent_names": ["electronic equipment"], "child_names": [], "child_labels": []}, "154": {"node_name": "swan", "node_id_at_leaf": -1, "parent_names": ["aquatic bird"], "child_names": ["black swan, Cygnus atratus"], "child_labels": [100]}, "155": {"node_name": "remote control, remote", "node_id_at_leaf": 761, "parent_names": ["electronic equipment"], "child_names": [], "child_labels": []}, "156": {"node_name": "stocking", "node_id_at_leaf": -1, "parent_names": ["footwear, legwear"], "child_names": ["Christmas stocking"], "child_labels": [496]}, "157": {"node_name": "pot", "node_id_at_leaf": -1, "parent_names": ["kitchen utensil"], "child_names": ["caldron, cauldron", "coffeepot", "Dutch oven", "teapot"], "child_labels": [849, 544, 505, 469]}, "158": {"node_name": "drilling platform, offshore rig", "node_id_at_leaf": 540, "parent_names": ["rig"], "child_names": [], "child_labels": []}, "159": {"node_name": "mosquito net", "node_id_at_leaf": 669, "parent_names": ["screen"], "child_names": [], "child_labels": []}, "160": {"node_name": "leafhopper", "node_id_at_leaf": 317, "parent_names": ["insect"], "child_names": [], "child_labels": []}, "161": {"node_name": "radio, wireless", "node_id_at_leaf": 754, "parent_names": ["electronic equipment"], "child_names": [], "child_labels": []}, "162": {"node_name": "cabinet", "node_id_at_leaf": -1, "parent_names": ["wall unit"], "child_names": ["china cabinet, china closet", "file, file cabinet, filing cabinet", "medicine chest, medicine cabinet"], "child_labels": [495, 553, 648]}, "163": {"node_name": "jackfruit, jak, jack", "node_id_at_leaf": 955, "parent_names": ["fruit"], "child_names": [], "child_labels": []}, "164": {"node_name": "car, railcar, railway car, railroad car", "node_id_at_leaf": -1, "parent_names": ["train, railroad train"], "child_names": ["freight car", "passenger car, coach, carriage"], "child_labels": [705, 565]}, "165": {"node_name": "cardoon", "node_id_at_leaf": 946, "parent_names": ["vegetable, veggie, veg"], "child_names": [], "child_labels": []}, "166": {"node_name": "photocopier", "node_id_at_leaf": 713, "parent_names": ["electronic equipment"], "child_names": [], "child_labels": []}, "167": {"node_name": "nematode, nematode worm, roundworm", "node_id_at_leaf": 111, "parent_names": ["dummy15"], "child_names": [], "child_labels": []}, "168": {"node_name": "bottle", "node_id_at_leaf": -1, "parent_names": ["tableware"], "child_names": ["beer bottle", "pop bottle, soda bottle", "water bottle", "wine bottle"], "child_labels": [907, 440, 737, 898]}, "169": {"node_name": "syringe", "node_id_at_leaf": 845, "parent_names": ["medical instrument"], "child_names": [], "child_labels": []}, "170": {"node_name": "factory, mill, manufacturing plant, manufactory", "node_id_at_leaf": -1, "parent_names": ["building, edifice"], "child_names": ["lumbermill, sawmill"], "child_labels": [634]}, "171": {"node_name": "ape", "node_id_at_leaf": -1, "parent_names": ["primate"], "child_names": ["orangutan, orang, orangutang, Pongo pygmaeus", "gorilla, Gorilla gorilla", "chimpanzee, chimp, Pan troglodytes", "gibbon, Hylobates lar", "<PERSON><PERSON><PERSON>, Hylob<PERSON> syndactylus, Symphalangus syndactylus"], "child_labels": [366, 367, 368, 369, 365]}, "172": {"node_name": "helmet", "node_id_at_leaf": -1, "parent_names": ["headdress, headgear"], "child_names": ["crash helmet", "football helmet", "pickelhaube"], "child_labels": [518, 715, 560]}, "173": {"node_name": "space heater", "node_id_at_leaf": 811, "parent_names": ["home appliance, household appliance"], "child_names": [], "child_labels": []}, "174": {"node_name": "plunger, plumber's helper", "node_id_at_leaf": 731, "parent_names": ["tool"], "child_names": [], "child_labels": []}, "175": {"node_name": "sharpener", "node_id_at_leaf": -1, "parent_names": ["tool"], "child_names": ["pencil sharpener"], "child_labels": [710]}, "176": {"node_name": "rail", "node_id_at_leaf": -1, "parent_names": ["aquatic bird"], "child_names": ["coot"], "child_labels": [137]}, "177": {"node_name": "signboard, sign", "node_id_at_leaf": -1, "parent_names": ["dummy72"], "child_names": ["scoreboard", "street sign", "traffic light, traffic signal, stoplight"], "child_labels": [920, 781, 919]}, "178": {"node_name": "fence, fencing", "node_id_at_leaf": -1, "parent_names": ["barrier"], "child_names": ["chainlink fence", "picket fence, paling", "stone wall", "rail fence"], "child_labels": [716, 825, 912, 489]}, "179": {"node_name": "custard apple", "node_id_at_leaf": 956, "parent_names": ["fruit"], "child_names": [], "child_labels": []}, "180": {"node_name": "groom, bridegroom", "node_id_at_leaf": 982, "parent_names": ["person, individual, someone, somebody, mortal, soul"], "child_names": [], "child_labels": []}, "181": {"node_name": "pelican", "node_id_at_leaf": 144, "parent_names": ["aquatic bird"], "child_names": [], "child_labels": []}, "182": {"node_name": "mat", "node_id_at_leaf": -1, "parent_names": ["floor cover, floor covering"], "child_names": ["doormat, welcome mat"], "child_labels": [539]}, "183": {"node_name": "bar<PERSON><PERSON><PERSON>, snoek", "node_id_at_leaf": 389, "parent_names": ["bony fish"], "child_names": [], "child_labels": []}, "184": {"node_name": "cauliflower", "node_id_at_leaf": 938, "parent_names": ["vegetable, veggie, veg"], "child_names": [], "child_labels": []}, "185": {"node_name": "space shuttle", "node_id_at_leaf": 812, "parent_names": ["spacecraft, ballistic capsule, space vehicle"], "child_names": [], "child_labels": []}, "186": {"node_name": "cheetah, chetah, Acinony<PERSON> jubatus", "node_id_at_leaf": 293, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "187": {"node_name": "nail", "node_id_at_leaf": 677, "parent_names": ["tool"], "child_names": [], "child_labels": []}, "188": {"node_name": "cricket", "node_id_at_leaf": 312, "parent_names": ["insect"], "child_names": [], "child_labels": []}, "189": {"node_name": "measuring stick, measure, measuring rod", "node_id_at_leaf": -1, "parent_names": ["measuring instrument, measuring system, measuring device"], "child_names": ["rule, ruler"], "child_labels": [769]}, "190": {"node_name": "snake, serpent, ophidian", "node_id_at_leaf": -1, "parent_names": ["serpentes"], "child_names": ["thunder snake, worm snake, Carphophis amoenus", "ringneck snake, ring-necked snake, ring snake", "hognose snake, puff adder, sand viper", "green snake, grass snake", "king snake, kingsnake", "garter snake, grass snake", "water snake", "vine snake", "night snake, Hypsiglena torquata", "boa constrictor, Constrictor constrictor", "sea snake", "horned viper, cerastes, sand viper, horned asp, <PERSON>rastes cornutus", "cobra", "python", "rattlesnake, rattler", "mamba"], "child_labels": [62, 58, 52, 61, 54, 53, 57, 66, 65, 55, 68, 59, 60, 56, 64, 67, 63]}, "191": {"node_name": "display, video display", "node_id_at_leaf": -1, "parent_names": ["electronic equipment"], "child_names": ["screen, CRT screen"], "child_labels": [782]}, "192": {"node_name": "weight, free weight, exercising weight", "node_id_at_leaf": -1, "parent_names": ["sports equipment"], "child_names": ["barbell", "dumbbell"], "child_labels": [422, 543]}, "193": {"node_name": "nightwear, sleepwear, nightclothes", "node_id_at_leaf": -1, "parent_names": ["garment"], "child_names": ["pajama, pyjama, pj's, jammies"], "child_labels": [697]}, "194": {"node_name": "cliff, drop, drop-off", "node_id_at_leaf": 972, "parent_names": ["dummy68"], "child_names": [], "child_labels": []}, "195": {"node_name": "lock", "node_id_at_leaf": -1, "parent_names": ["tool"], "child_names": ["combination lock", "padlock"], "child_labels": [695, 507]}, "196": {"node_name": "cassette player", "node_id_at_leaf": 482, "parent_names": ["electronic equipment"], "child_names": [], "child_labels": []}, "197": {"node_name": "heron", "node_id_at_leaf": -1, "parent_names": ["aquatic bird"], "child_names": ["little blue heron, Egretta caerulea", "bittern", "egret"], "child_labels": [133, 132, 131]}, "198": {"node_name": "bed", "node_id_at_leaf": -1, "parent_names": ["bedroom furniture"], "child_names": ["four-poster"], "child_labels": [564]}, "199": {"node_name": "otter", "node_id_at_leaf": 360, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "200": {"node_name": "chair", "node_id_at_leaf": -1, "parent_names": ["seat"], "child_names": ["barber chair", "folding chair", "rocking chair, rocker", "chair of state"], "child_labels": [559, 765, 423, 857]}, "201": {"node_name": "j<PERSON><PERSON>", "node_id_at_leaf": 95, "parent_names": ["piciform bird"], "child_names": [], "child_labels": []}, "202": {"node_name": "tape player", "node_id_at_leaf": 848, "parent_names": ["electronic equipment"], "child_names": [], "child_labels": []}, "203": {"node_name": "rabbit, coney, cony", "node_id_at_leaf": -1, "parent_names": ["lagomorph, gnawing mammal"], "child_names": ["wood rabbit, cottontail, cottontail rabbit", "Ang<PERSON>, Angora rabbit"], "child_labels": [330, 332]}, "204": {"node_name": "desk", "node_id_at_leaf": 526, "parent_names": ["table"], "child_names": [], "child_labels": []}, "205": {"node_name": "unicycle, monocycle", "node_id_at_leaf": 880, "parent_names": ["cycles"], "child_names": [], "child_labels": []}, "206": {"node_name": "hippopotamus, hippo, river horse, Hippopotamus amphibius", "node_id_at_leaf": 344, "parent_names": ["ungulate, hoofed mammal"], "child_names": [], "child_labels": []}, "207": {"node_name": "hyena, hyaena", "node_id_at_leaf": 276, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "208": {"node_name": "eagle, bird of Jove", "node_id_at_leaf": -1, "parent_names": ["bird of prey, raptor, raptorial bird"], "child_names": ["bald eagle, American eagle, Haliaeetus leucocephalus"], "child_labels": [22]}, "209": {"node_name": "sauce", "node_id_at_leaf": -1, "parent_names": ["condiment"], "child_names": ["chocolate sauce, chocolate syrup", "spaghetti sauce, pasta sauce"], "child_labels": [960, 959]}, "210": {"node_name": "snorkel", "node_id_at_leaf": 801, "parent_names": ["sports equipment"], "child_names": [], "child_labels": []}, "211": {"node_name": "lighter, light, igniter, ignitor", "node_id_at_leaf": 626, "parent_names": ["tool"], "child_names": [], "child_labels": []}, "212": {"node_name": "bannister, banister, balustrade, balusters, handrail", "node_id_at_leaf": 421, "parent_names": ["barrier"], "child_names": [], "child_labels": []}, "213": {"node_name": "mushroom", "node_id_at_leaf": -1, "parent_names": ["basidiomycete, basidiomycetous fungi"], "child_names": ["agaric", "hen-of-the-woods, hen of the woods, Polyporus frondosus, <PERSON><PERSON><PERSON><PERSON> frondosa", "bolete"], "child_labels": [997, 996, 992]}, "214": {"node_name": "face mask", "node_id_at_leaf": -1, "parent_names": ["facial accessories"], "child_names": ["gasmask, respirator, gas helmet", "mask", "ski mask"], "child_labels": [643, 796, 570]}, "215": {"node_name": "kangaroo", "node_id_at_leaf": -1, "parent_names": ["marsupial, pouched mammal"], "child_names": ["wallaby, brush kangaroo"], "child_labels": [104]}, "216": {"node_name": "crocodile", "node_id_at_leaf": -1, "parent_names": ["crocodilian reptile, crocodilian"], "child_names": ["African crocodile, Nile crocodile, Crocodylus niloticus"], "child_labels": [49]}, "217": {"node_name": "pizza, pizza pie", "node_id_at_leaf": 963, "parent_names": ["dish"], "child_names": [], "child_labels": []}, "218": {"node_name": "monkey", "node_id_at_leaf": -1, "parent_names": ["primate"], "child_names": ["guenon, guenon monkey", "patas, hussar monkey, Erythrocebus patas", "baboon", "macaque", "langur", "colobus, colobus monkey", "proboscis monkey, Nasalis larvatus", "marmoset", "capuchin, ringtail, Cebus capucinus", "howler monkey, howler", "titi, titi monkey", "spider monkey, <PERSON><PERSON><PERSON>i", "squirrel monkey, <PERSON><PERSON><PERSON> sciureus"], "child_labels": [376, 371, 375, 379, 377, 373, 372, 374, 378, 380, 381, 382, 370]}, "219": {"node_name": "eraser", "node_id_at_leaf": -1, "parent_names": ["tool"], "child_names": ["rubber eraser, rubber, pencil eraser"], "child_labels": [767]}, "220": {"node_name": "cap", "node_id_at_leaf": -1, "parent_names": ["headdress, headgear"], "child_names": ["bathing cap, swimming cap", "mortarboard", "shower cap"], "child_labels": [433, 793, 667]}, "221": {"node_name": "duck", "node_id_at_leaf": -1, "parent_names": ["aquatic bird"], "child_names": ["drake", "merganser, fish duck, sawbill, sheldrake"], "child_labels": [97, 98]}, "222": {"node_name": "conch", "node_id_at_leaf": 112, "parent_names": ["gastropod, univalve"], "child_names": [], "child_labels": []}, "223": {"node_name": "dinosaur", "node_id_at_leaf": -1, "parent_names": ["archosaur, archosaurian, archosaurian reptile"], "child_names": ["ornithischian, ornithischian dinosaur"], "child_labels": [51]}, "224": {"node_name": "airship, dirigible", "node_id_at_leaf": 405, "parent_names": ["aircraft"], "child_names": [], "child_labels": []}, "225": {"node_name": "missile", "node_id_at_leaf": 657, "parent_names": ["weapon, arm, weapon system"], "child_names": [], "child_labels": []}, "226": {"node_name": "coral", "node_id_at_leaf": -1, "parent_names": ["anthozoan, actinozoan"], "child_names": ["brain coral"], "child_labels": [109]}, "227": {"node_name": "fire screen, fireguard", "node_id_at_leaf": 556, "parent_names": ["screen"], "child_names": [], "child_labels": []}, "228": {"node_name": "tripod", "node_id_at_leaf": 872, "parent_names": ["photographic equipment"], "child_names": [], "child_labels": []}, "229": {"node_name": "gymnastic apparatus, exerciser", "node_id_at_leaf": -1, "parent_names": ["sports equipment"], "child_names": ["balance beam, beam", "horizontal bar, high bar", "parallel bars, bars"], "child_labels": [602, 416, 702]}, "230": {"node_name": "pen", "node_id_at_leaf": -1, "parent_names": ["tool"], "child_names": ["ballpoint, ballpoint pen, ballpen, Biro", "fountain pen", "quill, quill pen"], "child_labels": [418, 749, 563]}, "231": {"node_name": "body armor, body armour, suit of armor, suit of armour, coat of mail, cataphract", "node_id_at_leaf": -1, "parent_names": ["armor"], "child_names": ["breastplate, aegis, egis", "bulletproof vest", "chain mail, ring mail, mail, chain armor, chain armour, ring armor, ring armour", "cuirass"], "child_labels": [461, 524, 490, 465]}, "232": {"node_name": "compass", "node_id_at_leaf": -1, "parent_names": ["measuring instrument, measuring system, measuring device"], "child_names": ["magnetic compass"], "child_labels": [635]}, "233": {"node_name": "window shade", "node_id_at_leaf": 905, "parent_names": ["screen"], "child_names": [], "child_labels": []}, "234": {"node_name": "telephone, phone, telephone set", "node_id_at_leaf": -1, "parent_names": ["electronic equipment"], "child_names": ["cellular telephone, cellular phone, cellphone, cell, mobile phone", "dial telephone, dial phone", "pay-phone, pay-station"], "child_labels": [707, 528, 487]}, "235": {"node_name": "makeup, make-up, war paint", "node_id_at_leaf": -1, "parent_names": ["cosmetic"], "child_names": ["face powder", "lipstick, lip rouge"], "child_labels": [551, 629]}, "236": {"node_name": "coffee maker", "node_id_at_leaf": -1, "parent_names": ["home appliance, household appliance"], "child_names": ["espresso maker"], "child_labels": [550]}, "237": {"node_name": "beaver", "node_id_at_leaf": 337, "parent_names": ["rodent, gnawer"], "child_names": [], "child_labels": []}, "238": {"node_name": "crutch", "node_id_at_leaf": 523, "parent_names": ["dummy51"], "child_names": [], "child_labels": []}, "239": {"node_name": "window screen", "node_id_at_leaf": 904, "parent_names": ["screen"], "child_names": [], "child_labels": []}, "240": {"node_name": "badger", "node_id_at_leaf": 362, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "241": {"node_name": "sweater, jumper", "node_id_at_leaf": -1, "parent_names": ["garment"], "child_names": ["cardigan", "pullover, slipover"], "child_labels": [474, 841]}, "242": {"node_name": "baby bed, baby's bed", "node_id_at_leaf": -1, "parent_names": ["bedroom furniture"], "child_names": ["bassinet", "cradle", "crib, cot"], "child_labels": [520, 431, 516]}, "243": {"node_name": "sea urchin", "node_id_at_leaf": 328, "parent_names": ["dummy13"], "child_names": [], "child_labels": []}, "244": {"node_name": "perfume, essence", "node_id_at_leaf": 711, "parent_names": ["cosmetic"], "child_names": [], "child_labels": []}, "245": {"node_name": "mug", "node_id_at_leaf": -1, "parent_names": ["tableware"], "child_names": ["coffee mug"], "child_labels": [504]}, "246": {"node_name": "loaf of bread, loaf", "node_id_at_leaf": -1, "parent_names": ["baked goods"], "child_names": ["French loaf"], "child_labels": [930]}, "247": {"node_name": "bicycle, bike, wheel, cycle", "node_id_at_leaf": -1, "parent_names": ["cycles"], "child_names": ["bicycle-built-for-two, tandem bicycle, tandem", "mountain bike, all-terrain bike, off-roader"], "child_labels": [671, 444]}, "248": {"node_name": "handkerchief, hankie, hanky, hankey", "node_id_at_leaf": 591, "parent_names": ["dummy50"], "child_names": [], "child_labels": []}, "249": {"node_name": "rotisserie", "node_id_at_leaf": 766, "parent_names": ["home appliance, household appliance"], "child_names": [], "child_labels": []}, "250": {"node_name": "pudding, pud", "node_id_at_leaf": -1, "parent_names": ["dessert, sweet, afters"], "child_names": ["trifle"], "child_labels": [927]}, "251": {"node_name": "ant, emmet, pismire", "node_id_at_leaf": 310, "parent_names": ["insect"], "child_names": [], "child_labels": []}, "252": {"node_name": "column, pillar", "node_id_at_leaf": -1, "parent_names": ["dummy78"], "child_names": ["obelisk", "pedestal, plinth, footstall", "totem pole"], "child_labels": [708, 682, 863]}, "253": {"node_name": "lampshade, lamp shade", "node_id_at_leaf": 619, "parent_names": ["lamp"], "child_names": [], "child_labels": []}, "254": {"node_name": "chambered nautilus, pearly nautilus, nautilus", "node_id_at_leaf": 117, "parent_names": ["cephalopod, cephalopod mollusk"], "child_names": [], "child_labels": []}, "255": {"node_name": "rug, carpet, carpeting", "node_id_at_leaf": -1, "parent_names": ["floor cover, floor covering"], "child_names": ["prayer rug, prayer mat"], "child_labels": [741]}, "256": {"node_name": "pan, cooking pan", "node_id_at_leaf": -1, "parent_names": ["kitchen utensil"], "child_names": ["frying pan, frypan, skillet", "wok"], "child_labels": [909, 567]}, "257": {"node_name": "hummingbird", "node_id_at_leaf": 94, "parent_names": ["apodiform bird"], "child_names": [], "child_labels": []}, "258": {"node_name": "hammer", "node_id_at_leaf": 587, "parent_names": ["tool"], "child_names": [], "child_labels": []}, "259": {"node_name": "pomegranate", "node_id_at_leaf": 957, "parent_names": ["fruit"], "child_names": [], "child_labels": []}, "260": {"node_name": "scorpion", "node_id_at_leaf": 71, "parent_names": ["arachnid, arachnoid"], "child_names": [], "child_labels": []}, "261": {"node_name": "saltshaker, salt shaker", "node_id_at_leaf": 773, "parent_names": ["kitchen utensil"], "child_names": [], "child_labels": []}, "262": {"node_name": "lobster", "node_id_at_leaf": -1, "parent_names": ["crustacean"], "child_names": ["spiny lobster, langouste, rock lobster, crawfish, crayfish, sea crawfish", "true lobster"], "child_labels": [122, 123]}, "263": {"node_name": "frozen dessert", "node_id_at_leaf": -1, "parent_names": ["dessert, sweet, afters"], "child_names": ["ice cream, icecream", "ice lolly, lolly, lollipop, popsicle"], "child_labels": [928, 929]}, "264": {"node_name": "damselfly", "node_id_at_leaf": 320, "parent_names": ["insect"], "child_names": [], "child_labels": []}, "265": {"node_name": "dining table, board", "node_id_at_leaf": 532, "parent_names": ["table"], "child_names": [], "child_labels": []}, "266": {"node_name": "amphibian, amphibious vehicle", "node_id_at_leaf": 408, "parent_names": ["tracked vehicle"], "child_names": [], "child_labels": []}, "267": {"node_name": "pepper", "node_id_at_leaf": -1, "parent_names": ["vegetable, veggie, veg"], "child_names": ["sweet pepper"], "child_labels": [945]}, "268": {"node_name": "weasel", "node_id_at_leaf": 356, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "269": {"node_name": "bowl", "node_id_at_leaf": -1, "parent_names": ["tableware"], "child_names": ["mixing bowl", "soup bowl"], "child_labels": [659, 809]}, "270": {"node_name": "bow", "node_id_at_leaf": 456, "parent_names": ["weapon, arm, weapon system"], "child_names": [], "child_labels": []}, "271": {"node_name": "potato, white potato, Irish potato, murphy, spud, tater", "node_id_at_leaf": -1, "parent_names": ["dish"], "child_names": ["mashed potato"], "child_labels": [935]}, "272": {"node_name": "alligator, gator", "node_id_at_leaf": -1, "parent_names": ["crocodilian reptile, crocodilian"], "child_names": ["American alligator, Alligator mississipiensis"], "child_labels": [50]}, "273": {"node_name": "digital computer", "node_id_at_leaf": -1, "parent_names": ["electronic equipment"], "child_names": ["desktop computer", "hand-held computer, hand-held microcomputer", "laptop, laptop computer", "notebook, notebook computer"], "child_labels": [527, 620, 590, 681]}, "274": {"node_name": "undergarment, unmentionable", "node_id_at_leaf": -1, "parent_names": ["garment"], "child_names": ["brassiere, bra, bandeau", "diaper, nappy, napkin"], "child_labels": [529, 459]}, "275": {"node_name": "sandbar, sand bar", "node_id_at_leaf": 977, "parent_names": ["bar"], "child_names": [], "child_labels": []}, "276": {"node_name": "scuba diver", "node_id_at_leaf": 983, "parent_names": ["person, individual, someone, somebody, mortal, soul"], "child_names": [], "child_labels": []}, "277": {"node_name": "entertainment center", "node_id_at_leaf": 548, "parent_names": ["wall unit"], "child_names": [], "child_labels": []}, "278": {"node_name": "jin<PERSON><PERSON>a, ricksha, rickshaw", "node_id_at_leaf": 612, "parent_names": ["cart"], "child_names": [], "child_labels": []}, "279": {"node_name": "hog, pig, grunter, squealer, Sus scrofa", "node_id_at_leaf": 341, "parent_names": ["ungulate, hoofed mammal"], "child_names": [], "child_labels": []}, "280": {"node_name": "knife", "node_id_at_leaf": -1, "parent_names": ["kitchen utensil"], "child_names": ["cleaver, meat cleaver, chopper"], "child_labels": [499]}, "281": {"node_name": "table lamp", "node_id_at_leaf": 846, "parent_names": ["lamp"], "child_names": [], "child_labels": []}, "282": {"node_name": "joystick", "node_id_at_leaf": 613, "parent_names": ["electronic equipment"], "child_names": [], "child_labels": []}, "283": {"node_name": "sea anemone, anemone", "node_id_at_leaf": 108, "parent_names": ["anthozoan, actinozoan"], "child_names": [], "child_labels": []}, "284": {"node_name": "echidna, spiny anteater, anteater", "node_id_at_leaf": 102, "parent_names": ["monotreme, egg-laying mammal"], "child_names": [], "child_labels": []}, "285": {"node_name": "lemon", "node_id_at_leaf": 951, "parent_names": ["fruit"], "child_names": [], "child_labels": []}, "286": {"node_name": "squash", "node_id_at_leaf": -1, "parent_names": ["vegetable, veggie, veg"], "child_names": ["winter squash", "summer squash"], "child_labels": [939, 940, 941, 942]}, "287": {"node_name": "alp", "node_id_at_leaf": 970, "parent_names": ["mountain, mount"], "child_names": [], "child_labels": []}, "288": {"node_name": "monitor", "node_id_at_leaf": 664, "parent_names": ["electronic equipment"], "child_names": [], "child_labels": []}, "289": {"node_name": "half track", "node_id_at_leaf": 586, "parent_names": ["tracked vehicle"], "child_names": [], "child_labels": []}, "290": {"node_name": "tower", "node_id_at_leaf": -1, "parent_names": ["building, edifice"], "child_names": ["beacon, lighthouse, beacon light, pharos"], "child_labels": [437]}, "291": {"node_name": "suit, suit of clothes", "node_id_at_leaf": 834, "parent_names": ["garment"], "child_names": [], "child_labels": []}, "292": {"node_name": "roof", "node_id_at_leaf": -1, "parent_names": ["area"], "child_names": ["dome", "thatch, thatched roof", "tile roof", "vault"], "child_labels": [858, 538, 853, 884]}, "293": {"node_name": "ball", "node_id_at_leaf": -1, "parent_names": ["sports equipment"], "child_names": ["baseball", "basketball", "croquet ball", "golf ball", "ping-pong ball", "punching bag, punch bag, punching ball, punchball", "rugby ball", "soccer ball", "tennis ball", "volleyball"], "child_labels": [890, 722, 747, 805, 429, 852, 522, 430, 574, 768]}, "294": {"node_name": "toilet seat", "node_id_at_leaf": 861, "parent_names": ["seat"], "child_names": [], "child_labels": []}, "295": {"node_name": "titmouse, tit", "node_id_at_leaf": -1, "parent_names": ["passerine, passeriform bird"], "child_names": ["chickadee"], "child_labels": [19]}, "296": {"node_name": "sliding door", "node_id_at_leaf": 799, "parent_names": ["door"], "child_names": [], "child_labels": []}, "297": {"node_name": "llama", "node_id_at_leaf": 355, "parent_names": ["ungulate, hoofed mammal"], "child_names": [], "child_labels": []}, "298": {"node_name": "audio system, sound system", "node_id_at_leaf": -1, "parent_names": ["electronic equipment"], "child_names": ["iPod", "loudspeaker, speaker, speaker unit, loudspeaker system, speaker system"], "child_labels": [605, 632]}, "299": {"node_name": "tricycle, trike, velocipede", "node_id_at_leaf": 870, "parent_names": ["cycles"], "child_names": [], "child_labels": []}, "300": {"node_name": "punch", "node_id_at_leaf": -1, "parent_names": ["alcohol, alcoholic drink, alcoholic beverage, intoxicant, inebriant"], "child_names": ["cup", "eggnog"], "child_labels": [968, 969]}, "301": {"node_name": "waffle iron", "node_id_at_leaf": 891, "parent_names": ["home appliance, household appliance"], "child_names": [], "child_labels": []}, "302": {"node_name": "home theater, home theatre", "node_id_at_leaf": 598, "parent_names": ["electronic equipment"], "child_names": [], "child_labels": []}, "303": {"node_name": "plate", "node_id_at_leaf": 923, "parent_names": ["tableware"], "child_names": [], "child_labels": []}, "304": {"node_name": "cabbage, chou", "node_id_at_leaf": -1, "parent_names": ["vegetable, veggie, veg"], "child_names": ["head cabbage"], "child_labels": [936]}, "305": {"node_name": "polecat, fitch, foulmart, foumart, <PERSON><PERSON> putorius", "node_id_at_leaf": 358, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "306": {"node_name": "military uniform", "node_id_at_leaf": 652, "parent_names": ["garment"], "child_names": [], "child_labels": []}, "307": {"node_name": "computer keyboard, keypad", "node_id_at_leaf": 508, "parent_names": ["electronic equipment"], "child_names": [], "child_labels": []}, "308": {"node_name": "lesser panda, red panda, panda, bear cat, cat bear, Ailurus fulgens", "node_id_at_leaf": 387, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "309": {"node_name": "goat, caprine animal", "node_id_at_leaf": -1, "parent_names": ["ungulate, hoofed mammal"], "child_names": ["ibex, Capra ibex"], "child_labels": [350]}, "310": {"node_name": "goldfish, Carassius auratus", "node_id_at_leaf": 1, "parent_names": ["bony fish"], "child_names": [], "child_labels": []}, "311": {"node_name": "snowmobile", "node_id_at_leaf": 802, "parent_names": ["tracked vehicle"], "child_names": [], "child_labels": []}, "312": {"node_name": "hat, chapeau, lid", "node_id_at_leaf": -1, "parent_names": ["headdress, headgear"], "child_names": ["bearskin, busby, shako", "bonnet, poke bonnet", "cowboy hat, ten-gallon hat", "sombrero"], "child_labels": [439, 515, 452, 808]}, "313": {"node_name": "cushion", "node_id_at_leaf": -1, "parent_names": ["padding, cushioning"], "child_names": ["pillow"], "child_labels": [721]}, "314": {"node_name": "tench, Tinca tinca", "node_id_at_leaf": 0, "parent_names": ["bony fish"], "child_names": [], "child_labels": []}, "315": {"node_name": "tights, leotards", "node_id_at_leaf": -1, "parent_names": ["footwear, legwear"], "child_names": ["maillot"], "child_labels": [638]}, "316": {"node_name": "streetcar, tram, tramcar, trolley, trolley car", "node_id_at_leaf": 829, "parent_names": ["train, railroad train"], "child_names": [], "child_labels": []}, "317": {"node_name": "bustard", "node_id_at_leaf": 138, "parent_names": ["aquatic bird"], "child_names": [], "child_labels": []}, "318": {"node_name": "ray", "node_id_at_leaf": -1, "parent_names": ["cartilaginous fish, chondrichthian"], "child_names": ["electric ray, crampfish, numbfish, torpedo", "stingray"], "child_labels": [5, 6]}, "319": {"node_name": "butterfly fish", "node_id_at_leaf": -1, "parent_names": ["bony fish"], "child_names": ["rock beauty, Holocanthus tricolor"], "child_labels": [392]}, "320": {"node_name": "butterfly", "node_id_at_leaf": -1, "parent_names": ["insect"], "child_names": ["ringlet, ringlet butterfly", "sulphur butterfly, sulfur butterfly", "lycaenid, lycaenid butterfly", "pierid, pierid butterfly", "nymphalid, nymphalid butterfly, brush-footed butterfly, four-footed butterfly", "danaid, danaid butterfly"], "child_labels": [323, 322, 324, 321, 325, 326]}, "321": {"node_name": "giant panda, panda, panda bear, coon bear, Ailuropoda melanoleuca", "node_id_at_leaf": 388, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "322": {"node_name": "jaguar, panther, Panthera onca, Felis onca", "node_id_at_leaf": 290, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "323": {"node_name": "skunk, polecat, wood pussy", "node_id_at_leaf": 361, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "324": {"node_name": "lakeside, lakeshore", "node_id_at_leaf": 975, "parent_names": ["shore"], "child_names": [], "child_labels": []}, "325": {"node_name": "spectacles, specs, eyeglasses, glasses", "node_id_at_leaf": -1, "parent_names": ["facial accessories"], "child_names": ["sunglasses, dark glasses, shades"], "child_labels": [837]}, "326": {"node_name": "hip, rose hip, rosehip", "node_id_at_leaf": 989, "parent_names": ["fruit"], "child_names": [], "child_labels": []}, "327": {"node_name": "daisy", "node_id_at_leaf": 985, "parent_names": ["flower"], "child_names": [], "child_labels": []}, "328": {"node_name": "jellyfish", "node_id_at_leaf": 107, "parent_names": ["dummy10"], "child_names": [], "child_labels": []}, "329": {"node_name": "magpie", "node_id_at_leaf": 18, "parent_names": ["passerine, passeriform bird"], "child_names": [], "child_labels": []}, "330": {"node_name": "sofa, couch, lounge", "node_id_at_leaf": -1, "parent_names": ["seat"], "child_names": ["bench", "convertible, sofa bed"], "child_labels": [831, 703]}, "331": {"node_name": "jug", "node_id_at_leaf": -1, "parent_names": ["tableware"], "child_names": ["water jug", "whiskey jug"], "child_labels": [901, 899]}, "332": {"node_name": "meerkat, mierkat", "node_id_at_leaf": 299, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "333": {"node_name": "orchid, orchidaceous plant", "node_id_at_leaf": -1, "parent_names": ["flower"], "child_names": ["lady's slipper, lady-slipper, ladies' slipper, slipper orchid"], "child_labels": [986]}, "334": {"node_name": "goldfinch, <PERSON><PERSON><PERSON> card<PERSON>", "node_id_at_leaf": 11, "parent_names": ["passerine, passeriform bird"], "child_names": [], "child_labels": []}, "335": {"node_name": "black-footed ferret, ferret, <PERSON><PERSON> nigripes", "node_id_at_leaf": 359, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "336": {"node_name": "dwelling, home, domicile, abode, habitation, dwelling house", "node_id_at_leaf": -1, "parent_names": ["building, edifice"], "child_names": ["birdhouse", "castle", "cliff dwelling", "mobile home, manufactured home", "monastery", "palace", "yurt"], "child_labels": [483, 500, 448, 915, 663, 698, 660]}, "337": {"node_name": "microwave, microwave oven", "node_id_at_leaf": 651, "parent_names": ["home appliance, household appliance"], "child_names": [], "child_labels": []}, "338": {"node_name": "power drill", "node_id_at_leaf": 740, "parent_names": ["tool"], "child_names": [], "child_labels": []}, "339": {"node_name": "sewing machine", "node_id_at_leaf": 786, "parent_names": ["home appliance, household appliance"], "child_names": [], "child_labels": []}, "340": {"node_name": "scale, weighing machine", "node_id_at_leaf": 778, "parent_names": ["measuring instrument, measuring system, measuring device"], "child_names": [], "child_labels": []}, "341": {"node_name": "dummy47", "node_id_at_leaf": -1, "parent_names": ["building, edifice"], "child_names": ["prison, prison house"], "child_labels": [743]}, "342": {"node_name": "squirrel", "node_id_at_leaf": -1, "parent_names": ["rodent, gnawer"], "child_names": ["fox squirrel, eastern fox squirrel, Sciurus niger"], "child_labels": [335]}, "343": {"node_name": "wild dog", "node_id_at_leaf": -1, "parent_names": ["carnivore"], "child_names": ["dingo, warrigal, warragal, Canis dingo", "dhole, Cuon alpinus", "African hunting dog, hyena dog, Cape hunting dog, Lycaon pictus"], "child_labels": [275, 274, 273]}, "344": {"node_name": "lens cap, lens cover", "node_id_at_leaf": 622, "parent_names": ["photographic equipment"], "child_names": [], "child_labels": []}, "345": {"node_name": "percussion instrument, percussive instrument", "node_id_at_leaf": -1, "parent_names": ["musical instrument, instrument"], "child_names": ["chime, bell, gong", "drum, membranophone, tympan", "gong, tam-tam", "maraca", "marimba, xylophone", "steel drum"], "child_labels": [577, 642, 494, 641, 822, 541]}, "346": {"node_name": "stove", "node_id_at_leaf": 827, "parent_names": ["home appliance, household appliance"], "child_names": [], "child_labels": []}, "347": {"node_name": "platypus, duckbill, duckbilled platypus, duck-billed platypus, Ornithorhynchus anatinus", "node_id_at_leaf": 103, "parent_names": ["monotreme, egg-laying mammal"], "child_names": [], "child_labels": []}, "348": {"node_name": "screwdriver", "node_id_at_leaf": 784, "parent_names": ["tool"], "child_names": [], "child_labels": []}, "349": {"node_name": "ladle", "node_id_at_leaf": 618, "parent_names": ["kitchen utensil"], "child_names": [], "child_labels": []}, "350": {"node_name": "goose", "node_id_at_leaf": 99, "parent_names": ["aquatic bird"], "child_names": [], "child_labels": []}, "351": {"node_name": "vacuum, vacuum cleaner", "node_id_at_leaf": 882, "parent_names": ["home appliance, household appliance"], "child_names": [], "child_labels": []}, "352": {"node_name": "dishrag, dishcloth", "node_id_at_leaf": 533, "parent_names": ["piece of cloth, piece of material"], "child_names": [], "child_labels": []}, "353": {"node_name": "warplane, military plane", "node_id_at_leaf": 895, "parent_names": ["aircraft"], "child_names": [], "child_labels": []}, "354": {"node_name": "lion, king of beasts, <PERSON><PERSON> leo", "node_id_at_leaf": 291, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "355": {"node_name": "camera, photographic camera", "node_id_at_leaf": -1, "parent_names": ["photographic equipment"], "child_names": ["Polaroid camera, Polaroid Land camera", "reflex camera"], "child_labels": [759, 732]}, "356": {"node_name": "torch", "node_id_at_leaf": 862, "parent_names": ["tool"], "child_names": [], "child_labels": []}, "357": {"node_name": "horse cart, horse-cart", "node_id_at_leaf": 603, "parent_names": ["cart"], "child_names": [], "child_labels": []}, "358": {"node_name": "house finch, linnet, Carpodacus mexicanus", "node_id_at_leaf": 12, "parent_names": ["passerine, passeriform bird"], "child_names": [], "child_labels": []}, "359": {"node_name": "airplane, aeroplane, plane", "node_id_at_leaf": -1, "parent_names": ["aircraft"], "child_names": ["airliner"], "child_labels": [404]}, "360": {"node_name": "trouser, pant", "node_id_at_leaf": -1, "parent_names": ["garment"], "child_names": ["jean, blue jean, denim"], "child_labels": [608]}, "361": {"node_name": "boot", "node_id_at_leaf": -1, "parent_names": ["footwear, legwear"], "child_names": ["cowboy boot"], "child_labels": [514]}, "362": {"node_name": "antelope", "node_id_at_leaf": -1, "parent_names": ["ungulate, hoofed mammal"], "child_names": ["hartebeest", "impala, Aepyceros melampus", "gazelle"], "child_labels": [352, 353, 351]}, "363": {"node_name": "wardrobe, closet, press", "node_id_at_leaf": 894, "parent_names": ["wall unit"], "child_names": [], "child_labels": []}, "364": {"node_name": "cocktail shaker", "node_id_at_leaf": 503, "parent_names": ["kitchen utensil"], "child_names": [], "child_labels": []}, "365": {"node_name": "cooker", "node_id_at_leaf": -1, "parent_names": ["home appliance, household appliance"], "child_names": ["Crock Pot"], "child_labels": [521]}, "366": {"node_name": "umbrella", "node_id_at_leaf": 879, "parent_names": ["dummy57"], "child_names": [], "child_labels": []}, "367": {"node_name": "buckle", "node_id_at_leaf": 464, "parent_names": ["dummy53"], "child_names": [], "child_labels": []}, "368": {"node_name": "projectile, missile", "node_id_at_leaf": 744, "parent_names": ["weapon, arm, weapon system"], "child_names": [], "child_labels": []}, "369": {"node_name": "meat loaf, meatloaf", "node_id_at_leaf": 962, "parent_names": ["dish"], "child_names": [], "child_labels": []}, "370": {"node_name": "glass, drinking glass", "node_id_at_leaf": -1, "parent_names": ["tableware"], "child_names": ["beer glass", "goblet"], "child_labels": [572, 441]}, "371": {"node_name": "cannon", "node_id_at_leaf": 471, "parent_names": ["weapon, arm, weapon system"], "child_names": [], "child_labels": []}, "372": {"node_name": "mushroom", "node_id_at_leaf": 947, "parent_names": ["vegetable, veggie, veg"], "child_names": [], "child_labels": []}, "373": {"node_name": "robin, American robin, <PERSON><PERSON><PERSON> migratorius", "node_id_at_leaf": 15, "parent_names": ["passerine, passeriform bird"], "child_names": [], "child_labels": []}, "374": {"node_name": "submarine, pigboat, sub, U-boat", "node_id_at_leaf": 833, "parent_names": ["vessel, watercraft"], "child_names": [], "child_labels": []}, "375": {"node_name": "oxcart", "node_id_at_leaf": 690, "parent_names": ["cart"], "child_names": [], "child_labels": []}, "376": {"node_name": "knee pad", "node_id_at_leaf": 615, "parent_names": ["footwear, legwear"], "child_names": [], "child_labels": []}, "377": {"node_name": "stethoscope", "node_id_at_leaf": 823, "parent_names": ["medical instrument"], "child_names": [], "child_labels": []}, "378": {"node_name": "hare", "node_id_at_leaf": 331, "parent_names": ["lagomorph, gnawing mammal"], "child_names": [], "child_labels": []}, "379": {"node_name": "isopod", "node_id_at_leaf": 126, "parent_names": ["crustacean"], "child_names": [], "child_labels": []}, "380": {"node_name": "oscilloscope, scope, cathode-ray oscilloscope, CRO", "node_id_at_leaf": 688, "parent_names": ["electronic equipment"], "child_names": [], "child_labels": []}, "381": {"node_name": "wild sheep", "node_id_at_leaf": -1, "parent_names": ["ungulate, hoofed mammal"], "child_names": ["mountain sheep"], "child_labels": [349]}, "382": {"node_name": "racket, racquet", "node_id_at_leaf": 752, "parent_names": ["sports equipment"], "child_names": [], "child_labels": []}, "383": {"node_name": "timepiece, timekeeper, horologe", "node_id_at_leaf": -1, "parent_names": ["measuring instrument, measuring system, measuring device"], "child_names": ["sundial", "clock", "sandglass", "watch, ticker", "timer"], "child_labels": [530, 604, 826, 409, 835, 531, 892, 704]}, "384": {"node_name": "dishwasher, dish washer, dishwashing machine", "node_id_at_leaf": 534, "parent_names": ["home appliance, household appliance"], "child_names": [], "child_labels": []}, "385": {"node_name": "armadillo", "node_id_at_leaf": 363, "parent_names": ["edentate"], "child_names": [], "child_labels": []}, "386": {"node_name": "ski", "node_id_at_leaf": 795, "parent_names": ["sports equipment"], "child_names": [], "child_labels": []}, "387": {"node_name": "mink", "node_id_at_leaf": 357, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "388": {"node_name": "paintbrush", "node_id_at_leaf": 696, "parent_names": ["tool"], "child_names": [], "child_labels": []}, "389": {"node_name": "fig", "node_id_at_leaf": 952, "parent_names": ["fruit"], "child_names": [], "child_labels": []}, "390": {"node_name": "fox", "node_id_at_leaf": -1, "parent_names": ["carnivore"], "child_names": ["red fox, Vulpes vulpes", "kit fox, Vulpes macrotis", "Arctic fox, white fox, Alopex lagopus", "grey fox, gray fox, Urocyon cinereoargenteus"], "child_labels": [277, 280, 278, 279]}, "391": {"node_name": "dummy46", "node_id_at_leaf": -1, "parent_names": ["building, edifice"], "child_names": ["planetarium"], "child_labels": [727]}, "392": {"node_name": "elephant", "node_id_at_leaf": -1, "parent_names": ["proboscidean, proboscidian"], "child_names": ["Indian elephant, <PERSON>ephas maximus", "African elephant, Loxodonta africana"], "child_labels": [385, 386]}, "393": {"node_name": "skirt", "node_id_at_leaf": -1, "parent_names": ["garment"], "child_names": ["hoopskirt, crinoline", "miniskirt, mini", "overskirt", "sarong"], "child_labels": [689, 601, 655, 775]}, "394": {"node_name": "cuckoo", "node_id_at_leaf": -1, "parent_names": ["cuculiform bird"], "child_names": ["coucal"], "child_labels": [91]}, "395": {"node_name": "barometer", "node_id_at_leaf": 426, "parent_names": ["measuring instrument, measuring system, measuring device"], "child_names": [], "child_labels": []}, "396": {"node_name": "puffer, pufferfish, blowfish, globefish", "node_id_at_leaf": 397, "parent_names": ["bony fish"], "child_names": [], "child_labels": []}, "397": {"node_name": "lynx, catamount", "node_id_at_leaf": 287, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "398": {"node_name": "pool table, billiard table, snooker table", "node_id_at_leaf": 736, "parent_names": ["table"], "child_names": [], "child_labels": []}, "399": {"node_name": "dummy45", "node_id_at_leaf": -1, "parent_names": ["building, edifice"], "child_names": ["library"], "child_labels": [624]}, "400": {"node_name": "patio, terrace", "node_id_at_leaf": 706, "parent_names": ["area"], "child_names": [], "child_labels": []}, "401": {"node_name": "shoji", "node_id_at_leaf": 789, "parent_names": ["screen"], "child_names": [], "child_labels": []}, "402": {"node_name": "jay", "node_id_at_leaf": 17, "parent_names": ["passerine, passeriform bird"], "child_names": [], "child_labels": []}, "403": {"node_name": "shark", "node_id_at_leaf": -1, "parent_names": ["cartilaginous fish, chondrichthian"], "child_names": ["great white shark, white shark, man-eater, man-eating shark, Carcharodon carcharias", "tiger shark, <PERSON><PERSON><PERSON><PERSON> cu<PERSON>i", "hammerhead, hammerhead shark"], "child_labels": [2, 4, 3]}, "404": {"node_name": "clog, geta, patten, sabot", "node_id_at_leaf": 502, "parent_names": ["footwear, legwear"], "child_names": [], "child_labels": []}, "405": {"node_name": "oystercatcher, oyster catcher", "node_id_at_leaf": 143, "parent_names": ["aquatic bird"], "child_names": [], "child_labels": []}, "406": {"node_name": "dogsled, dog sled, dog sleigh", "node_id_at_leaf": 537, "parent_names": ["sled, sledge, sleigh"], "child_names": [], "child_labels": []}, "407": {"node_name": "plow, plough", "node_id_at_leaf": 730, "parent_names": ["tool"], "child_names": [], "child_labels": []}, "408": {"node_name": "cicada, cicala", "node_id_at_leaf": 316, "parent_names": ["insect"], "child_names": [], "child_labels": []}, "409": {"node_name": "sturgeon", "node_id_at_leaf": 394, "parent_names": ["bony fish"], "child_names": [], "child_labels": []}, "410": {"node_name": "chest of drawers, chest, bureau, dresser", "node_id_at_leaf": -1, "parent_names": ["wall unit"], "child_names": ["chiffonier, commode"], "child_labels": [493]}, "411": {"node_name": "cucumber, cuke", "node_id_at_leaf": 943, "parent_names": ["vegetable, veggie, veg"], "child_names": [], "child_labels": []}, "412": {"node_name": "passenger train", "node_id_at_leaf": -1, "parent_names": ["train, railroad train"], "child_names": ["bullet train, bullet"], "child_labels": [466]}, "413": {"node_name": "bear", "node_id_at_leaf": -1, "parent_names": ["carnivore"], "child_names": ["brown bear, bruin, Ursus arctos", "American black bear, black bear, Ursus americanus, Euarctos americanus", "ice bear, polar bear, Ursus Maritimus, T<PERSON><PERSON><PERSON> maritimus", "sloth bear, <PERSON><PERSON><PERSON> ursinus, Ursus ursinus"], "child_labels": [294, 295, 296, 297]}, "414": {"node_name": "bell cote, bell cot", "node_id_at_leaf": 442, "parent_names": ["area"], "child_names": [], "child_labels": []}, "415": {"node_name": "vulture", "node_id_at_leaf": 23, "parent_names": ["bird of prey, raptor, raptorial bird"], "child_names": [], "child_labels": []}, "416": {"node_name": "cattle, cows, kine, oxen, Bos taurus", "node_id_at_leaf": -1, "parent_names": ["ungulate, hoofed mammal"], "child_names": ["ox"], "child_labels": [345]}, "417": {"node_name": "dam, dike, dyke", "node_id_at_leaf": 525, "parent_names": ["barrier"], "child_names": [], "child_labels": []}, "418": {"node_name": "sheep", "node_id_at_leaf": -1, "parent_names": ["ungulate, hoofed mammal"], "child_names": ["ram, tup"], "child_labels": [348]}, "419": {"node_name": "porcupine, hedgehog", "node_id_at_leaf": 334, "parent_names": ["rodent, gnawer"], "child_names": [], "child_labels": []}, "420": {"node_name": "turtle", "node_id_at_leaf": -1, "parent_names": ["chelonian, chelonian reptile"], "child_names": ["mud turtle", "terrapin", "box turtle, box tortoise", "sea turtle, marine turtle"], "child_labels": [33, 36, 37, 34, 35]}, "421": {"node_name": "dip", "node_id_at_leaf": -1, "parent_names": ["condiment"], "child_names": ["guacamole"], "child_labels": [924]}, "422": {"node_name": "Old World buffalo, buffalo", "node_id_at_leaf": -1, "parent_names": ["ungulate, hoofed mammal"], "child_names": ["water buffalo, water ox, Asiatic buffalo, Bubalus bubalis"], "child_labels": [346]}, "423": {"node_name": "penguin", "node_id_at_leaf": -1, "parent_names": ["aquatic bird"], "child_names": ["king penguin, Aptenodytes patagonica"], "child_labels": [145]}, "424": {"node_name": "bag", "node_id_at_leaf": -1, "parent_names": ["bag"], "child_names": ["backpack, back pack, knapsack, packsack, rucksack, haversack", "mailbag, postbag", "plastic bag", "purse"], "child_labels": [636, 414, 748, 728]}, "425": {"node_name": "leopard, Panthera pardus", "node_id_at_leaf": 288, "parent_names": ["carnivore"], "child_names": [], "child_labels": []}, "426": {"node_name": "volcano", "node_id_at_leaf": 980, "parent_names": ["mountain, mount"], "child_names": [], "child_labels": []}, "427": {"node_name": "clip", "node_id_at_leaf": -1, "parent_names": ["headdress, headgear"], "child_names": ["hair slide"], "child_labels": [584]}, "428": {"node_name": "telescope, scope", "node_id_at_leaf": -1, "parent_names": ["scientific instrument"], "child_names": ["radio telescope, radio reflector"], "child_labels": [755]}, "429": {"node_name": "adhesive bandage", "node_id_at_leaf": -1, "parent_names": ["bandage, patch"], "child_names": ["Band Aid"], "child_labels": [419]}, "430": {"node_name": "maze, labyrinth", "node_id_at_leaf": 646, "parent_names": ["dummy74"], "child_names": [], "child_labels": []}, "431": {"node_name": "hornbill", "node_id_at_leaf": 93, "parent_names": ["coraciiform bird"], "child_names": [], "child_labels": []}, "432": {"node_name": "dog, domestic dog, Canis familiaris", "node_id_at_leaf": -1, "parent_names": ["carnivore"], "child_names": ["Chihuahua", "Japanese spaniel", "Maltese dog, Maltese terrier, Maltese", "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "Shih-<PERSON><PERSON>", "toy terrier", "Rhodesian ridgeback", "Afghan hound, Afghan", "basset, basset hound", "beagle", "bloodhound, sleuthhound", "bluetick", "black-and-tan coonhound", "<PERSON> hound, Walker foxhound", "English foxhound", "redbone", "b<PERSON><PERSON><PERSON>, Russian wolfhound", "Irish wolfhound", "Italian greyhound", "whippet", "Ibizan hound, Ibizan Podenco", "Norwegian elkhound, elkhound", "otterhound, otter hound", "Saluki, gazelle hound", "Scottish deerhound, deerhound", "Weimaraner", "Bedlington terrier", "Border terrier", "Kerry blue terrier", "Irish terrier", "Norfolk terrier", "Norwich terrier", "Yorkshire terrier", "Airedale, Airedale terrier", "cairn, cairn terrier", "Australian terrier", "<PERSON><PERSON>, <PERSON><PERSON> terrier", "Boston bull, Boston terrier", "Scotch terrier, Scottish terrier, <PERSON>ie", "Tibetan terrier, chrysanthemum dog", "silky terrier, Sydney silky", "soft-coated wheaten terrier", "West Highland white terrier", "Lhasa, Lhasa apso", "flat-coated retriever", "curly-coated retriever", "golden retriever", "Labrador retriever", "Chesapeake Bay retriever", "German short-haired pointer", "<PERSON><PERSON><PERSON>, Hungarian pointer", "English setter", "Irish setter, red setter", "<PERSON> setter", "Brittany spaniel", "clumber, clumber spaniel", "cocker spaniel, English cocker spaniel, cocker", "Sussex spaniel", "kuvasz", "s<PERSON><PERSON><PERSON>", "briard", "kelpie", "komondor", "Old English sheepdog, bobtail", "Shetland sheepdog, Shetland sheep dog, Shetland", "collie", "Border collie", "Bouvier des Flandres, Bouviers des Flandres", "Rottweiler", "German shepherd, German shepherd dog, German police dog, alsatian", "<PERSON><PERSON>, <PERSON><PERSON> pinscher", "miniature pinscher", "Greater Swiss Mountain dog", "Bernese mountain dog", "<PERSON><PERSON><PERSON><PERSON>", "EntleBucher", "boxer", "bull mastiff", "Great Dane", "Saint Bernard, St Bernard", "Eskimo dog, husky", "malamute, malemute, Alaskan malamute", "Siberian husky", "da<PERSON><PERSON>, coach dog, carriage dog", "<PERSON><PERSON><PERSON><PERSON><PERSON>, monkey pinscher, monkey dog", "basenji", "pug, pug-dog", "<PERSON><PERSON>", "Newfoundland, Newfoundland dog", "Great Pyrenees", "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "Pomeranian", "chow, chow chow", "keeshond", "Mexican hairless", "schna<PERSON><PERSON>", "bulldog, English bulldog", "springer spaniel, springer", "griffon, Brussels griffon, Belgian griffon", "fox terrier", "wirehair, wirehaired terrier, wire-haired terrier", "Belgian sheepdog, Belgian shepherd", "toy spaniel", "corgi, Welsh corgi", "water spaniel", "poodle, poodle dog", "bullterrier, bull terrier", "mastiff"], "child_labels": [179, 234, 173, 160, 210, 243, 248, 252, 177, 235, 159, 188, 221, 153, 222, 257, 201, 156, 225, 167, 249, 264, 172, 192, 220, 236, 260, 165, 244, 251, 208, 203, 231, 239, 164, 186, 245, 215, 256, 200, 190, 228, 163, 216, 194, 214, 229, 162, 259, 247, 152, 233, 204, 183, 182, 218, 255, 237, 263, 242, 158, 219, 224, 178, 181, 189, 191, 240, 238, 174, 155, 253, 187, 161, 154, 250, 184, 198, 209, 193, 261, 199, 185, 254, 169, 196, 176, 180, 211, 241, 226, 151, 166, 267, 217, 262, 195, 212, 227, 170, 265, 202, 232, 230, 157, 205, 266, 246, 223, 206, 268, 171, 175, 197, 168, 207, 213, 258]}, "433": {"node_name": "apple", "node_id_at_leaf": -1, "parent_names": ["fruit"], "child_names": ["eating apple, dessert apple"], "child_labels": [948]}, "434": {"node_name": "eel", "node_id_at_leaf": 390, "parent_names": ["bony fish"], "child_names": [], "child_labels": []}, "435": {"node_name": "truck, motortruck", "node_id_at_leaf": -1, "parent_names": ["motor vehicle, automotive vehicle"], "child_names": ["fire engine, fire truck", "forklift", "garbage truck, dustcart", "moving van", "pickup, pickup truck", "snowplow, snowplough", "tow truck, tow car, wrecker", "tractor", "trailer truck, tractor trailer, trucking rig, rig, articulated lorry, semi"], "child_labels": [717, 867, 561, 569, 866, 864, 555, 803, 675]}, "436": {"node_name": "snail", "node_id_at_leaf": 113, "parent_names": ["gastropod, univalve"], "child_names": [], "child_labels": []}, "437": {"node_name": "starfish, sea star", "node_id_at_leaf": 327, "parent_names": ["dummy11"], "child_names": [], "child_labels": []}, "438": {"node_name": "gallinule, marsh hen, water hen, swamphen", "node_id_at_leaf": -1, "parent_names": ["aquatic bird"], "child_names": ["purple gallinule"], "child_labels": [136]}, "439": {"node_name": "chest", "node_id_at_leaf": 492, "parent_names": ["dummy79"], "child_names": [], "child_labels": []}, "440": {"node_name": "shoe", "node_id_at_leaf": -1, "parent_names": ["footwear, legwear"], "child_names": ["Loafer", "running shoe", "sandal"], "child_labels": [630, 774, 770]}, "441": {"node_name": "<PERSON><PERSON>, Aramus pictus", "node_id_at_leaf": 135, "parent_names": ["aquatic bird"], "child_names": [], "child_labels": []}, "442": {"node_name": "locomotive, engine, locomotive engine, railway locomotive", "node_id_at_leaf": -1, "parent_names": ["train, railroad train"], "child_names": ["electric locomotive", "steam locomotive"], "child_labels": [820, 547]}, "443": {"node_name": "bookcase", "node_id_at_leaf": 453, "parent_names": ["wall unit"], "child_names": [], "child_labels": []}, "444": {"node_name": "handcart, pushcart, cart, go-cart", "node_id_at_leaf": -1, "parent_names": ["cart"], "child_names": ["barrow, garden cart, lawn cart, wheelbarrow", "shopping cart"], "child_labels": [791, 428]}, "445": {"node_name": "corn", "node_id_at_leaf": 987, "parent_names": ["fruit"], "child_names": [], "child_labels": []}, "446": {"node_name": "scorpaenid, scorpaenid fish", "node_id_at_leaf": -1, "parent_names": ["bony fish"], "child_names": ["lionfish"], "child_labels": [396]}, "447": {"node_name": "gate", "node_id_at_leaf": -1, "parent_names": ["barrier"], "child_names": ["turnstile"], "child_labels": [877]}, "448": {"node_name": "sea slug, nudibranch", "node_id_at_leaf": 115, "parent_names": ["gastropod, univalve"], "child_names": [], "child_labels": []}, "449": {"node_name": "coat", "node_id_at_leaf": -1, "parent_names": ["garment"], "child_names": ["a<PERSON>a", "academic gown, academic robe, judge's robe", "cloak", "fur coat", "kimono", "lab coat, laboratory coat", "poncho", "vestment", "raincoat, waterproof"], "child_labels": [617, 399, 501, 400, 568, 614, 735, 887, 869]}, "450": {"node_name": "measuring cup", "node_id_at_leaf": 647, "parent_names": ["kitchen utensil"], "child_names": [], "child_labels": []}, "451": {"node_name": "buckeye, horse chestnut, conker", "node_id_at_leaf": 990, "parent_names": ["fruit"], "child_names": [], "child_labels": []}, "452": {"node_name": "crayfish, crawfish, crawdad, crawdaddy", "node_id_at_leaf": 124, "parent_names": ["crustacean"], "child_names": [], "child_labels": []}, "453": {"node_name": "stinkhorn, carrion fungus", "node_id_at_leaf": 994, "parent_names": ["basidiomycete, basidiomycetous fungi"], "child_names": [], "child_labels": []}, "454": {"node_name": "acorn", "node_id_at_leaf": 988, "parent_names": ["fruit"], "child_names": [], "child_labels": []}, "455": {"node_name": "bee", "node_id_at_leaf": 309, "parent_names": ["insect"], "child_names": [], "child_labels": []}, "456": {"node_name": "tank, army tank, armored combat vehicle, armoured combat vehicle", "node_id_at_leaf": 847, "parent_names": ["tracked vehicle"], "child_names": [], "child_labels": []}, "457": {"node_name": "stork", "node_id_at_leaf": -1, "parent_names": ["aquatic bird"], "child_names": ["white stork, Ciconia ciconia", "black stork, Ciconia nigra"], "child_labels": [127, 128]}, "458": {"node_name": "apron", "node_id_at_leaf": 411, "parent_names": ["garment"], "child_names": [], "child_labels": []}, "459": {"node_name": "plover", "node_id_at_leaf": -1, "parent_names": ["aquatic bird"], "child_names": ["ruddy turnstone, Arenaria interpres"], "child_labels": [139]}, "460": {"node_name": "ear, spike, capitulum", "node_id_at_leaf": 998, "parent_names": ["fruit"], "child_names": [], "child_labels": []}, "461": {"node_name": "nightingale, Luscinia megarhynchos", "node_id_at_leaf": -1, "parent_names": ["passerine, passeriform bird"], "child_names": ["bulbul"], "child_labels": [16]}, "462": {"node_name": "coral fungus", "node_id_at_leaf": 991, "parent_names": ["basidiomycete, basidiomycetous fungi"], "child_names": [], "child_labels": []}, "463": {"node_name": "television, television system", "node_id_at_leaf": 851, "parent_names": ["electronic equipment"], "child_names": [], "child_labels": []}, "464": {"node_name": "cream, ointment, emollient", "node_id_at_leaf": -1, "parent_names": ["cosmetic"], "child_names": ["lotion", "sunscreen, sunblock, sun blocker"], "child_labels": [631, 838]}, "465": {"node_name": "toaster", "node_id_at_leaf": 859, "parent_names": ["home appliance, household appliance"], "child_names": [], "child_labels": []}}