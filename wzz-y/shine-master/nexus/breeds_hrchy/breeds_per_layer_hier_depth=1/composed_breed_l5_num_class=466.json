{"0": {"node_name": "whale", "parent_names": ["aquatic mammal"], "child_names": ["grey whale", "gray whale", "devilfish", "<PERSON><PERSON><PERSON><PERSON> gibbosus", "<PERSON><PERSON><PERSON><PERSON> robustus", "killer whale", "killer", "orca", "grampus", "sea wolf", "Orcinus orca"], "candidate_sentences": ["a grey whale, which is a whale, which is a aquatic mammal", "a gray whale, which is a whale, which is a aquatic mammal", "a devilfish, which is a whale, which is a aquatic mammal", "a Eschrichtius gibbosus, which is a whale, which is a aquatic mammal", "a Eschrichtius robustus, which is a whale, which is a aquatic mammal", "a killer whale, which is a whale, which is a aquatic mammal", "a killer, which is a whale, which is a aquatic mammal", "a orca, which is a whale, which is a aquatic mammal", "a grampus, which is a whale, which is a aquatic mammal", "a sea wolf, which is a whale, which is a aquatic mammal", "a Orcinus orca, which is a whale, which is a aquatic mammal"]}, "1": {"node_name": "rapeseed", "parent_names": ["fruit"], "child_names": [], "candidate_sentences": ["a rapeseed, which is a fruit"]}, "2": {"node_name": "ballplayer, baseball player", "parent_names": ["person", "individual", "someone", "somebody", "mortal", "soul"], "child_names": [], "candidate_sentences": ["a ballplayer, baseball player, which is a person", "a ballplayer, baseball player, which is a individual", "a ballplayer, baseball player, which is a someone", "a ballplayer, baseball player, which is a somebody", "a ballplayer, baseball player, which is a mortal", "a ballplayer, baseball player, which is a soul"]}, "3": {"node_name": "ship", "parent_names": ["vessel", "watercraft"], "child_names": ["pirate", "pirate ship", "schooner", "wreck", "passenger ship", "cargo ship", "cargo vessel", "warship", "war vessel", "combat ship"], "candidate_sentences": ["a pirate, which is a ship, which is a vessel", "a pirate ship, which is a ship, which is a vessel", "a schooner, which is a ship, which is a vessel", "a wreck, which is a ship, which is a vessel", "a passenger ship, which is a ship, which is a vessel", "a cargo ship, which is a ship, which is a vessel", "a cargo vessel, which is a ship, which is a vessel", "a warship, which is a ship, which is a vessel", "a war vessel, which is a ship, which is a vessel", "a combat ship, which is a ship, which is a vessel", "a pirate, which is a ship, which is a watercraft", "a pirate ship, which is a ship, which is a watercraft", "a schooner, which is a ship, which is a watercraft", "a wreck, which is a ship, which is a watercraft", "a passenger ship, which is a ship, which is a watercraft", "a cargo ship, which is a ship, which is a watercraft", "a cargo vessel, which is a ship, which is a watercraft", "a warship, which is a ship, which is a watercraft", "a war vessel, which is a ship, which is a watercraft", "a combat ship, which is a ship, which is a watercraft"]}, "4": {"node_name": "CD player", "parent_names": ["electronic equipment"], "child_names": [], "candidate_sentences": ["a CD player, which is a electronic equipment"]}, "5": {"node_name": "iron, smoothing iron", "parent_names": ["home appliance", "household appliance"], "child_names": [], "candidate_sentences": ["a iron, smoothing iron, which is a home appliance", "a iron, smoothing iron, which is a household appliance"]}, "6": {"node_name": "sloth, tree sloth", "parent_names": ["edentate"], "child_names": ["three-toed sloth", "ai", "<PERSON><PERSON> tridactylus"], "candidate_sentences": ["a three-toed sloth, which is a sloth, tree sloth, which is a edentate", "a ai, which is a sloth, tree sloth, which is a edentate", "a Bradypus tridactylus, which is a sloth, tree sloth, which is a edentate"]}, "7": {"node_name": "lizard", "parent_names": ["saurian"], "child_names": ["chameleon", "chamaeleon", "venomous lizard", "teiid lizard", "teiid", "iguanid", "iguanid lizard", "gecko", "agamid", "agamid lizard", "anguid lizard", "lacertid lizard", "lacertid", "monitor", "monitor lizard", "varan"], "candidate_sentences": ["a chameleon, which is a lizard, which is a saurian", "a chamaeleon, which is a lizard, which is a saurian", "a venomous lizard, which is a lizard, which is a saurian", "a teiid lizard, which is a lizard, which is a saurian", "a teiid, which is a lizard, which is a saurian", "a iguanid, which is a lizard, which is a saurian", "a iguanid lizard, which is a lizard, which is a saurian", "a gecko, which is a lizard, which is a saurian", "a agamid, which is a lizard, which is a saurian", "a agamid lizard, which is a lizard, which is a saurian", "a anguid lizard, which is a lizard, which is a saurian", "a lacertid lizard, which is a lizard, which is a saurian", "a lacertid, which is a lizard, which is a saurian", "a monitor, which is a lizard, which is a saurian", "a monitor lizard, which is a lizard, which is a saurian", "a varan, which is a lizard, which is a saurian"]}, "8": {"node_name": "albatross, mollymawk", "parent_names": ["aquatic bird"], "child_names": [], "candidate_sentences": ["a albatross, mollymawk, which is a aquatic bird"]}, "9": {"node_name": "crab", "parent_names": ["crustacean"], "child_names": ["Dungeness crab", "Cancer magister", "rock crab", "Cancer irroratus", "fiddler crab", "king crab", "Alaska crab", "Alaskan king crab", "Alaska king crab", "Paralithodes camtschatica"], "candidate_sentences": ["a Dungeness crab, which is a crab, which is a crustacean", "a Cancer magister, which is a crab, which is a crustacean", "a rock crab, which is a crab, which is a crustacean", "a Cancer irroratus, which is a crab, which is a crustacean", "a fiddler crab, which is a crab, which is a crustacean", "a king crab, which is a crab, which is a crustacean", "a Alaska crab, which is a crab, which is a crustacean", "a Alaskan king crab, which is a crab, which is a crustacean", "a Alaska king crab, which is a crab, which is a crustacean", "a Paralithodes camtschatica, which is a crab, which is a crustacean"]}, "10": {"node_name": "tiger, Panthera tigris", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a tiger, Panthera tigris, which is a carnivore"]}, "11": {"node_name": "salamander", "parent_names": ["dummy8"], "child_names": ["European fire salamander", "<PERSON><PERSON><PERSON>", "spotted salamander", "Ambysto<PERSON> maculatum", "axolotl", "mud puppy", "Ambystoma mexicanum", "newt", "triton"], "candidate_sentences": ["a European fire salamander, which is a salamander, which is a dummy8", "a Salamandra salamandra, which is a salamander, which is a dummy8", "a spotted salamander, which is a salamander, which is a dummy8", "a Ambystoma maculatum, which is a salamander, which is a dummy8", "a axolotl, which is a salamander, which is a dummy8", "a mud puppy, which is a salamander, which is a dummy8", "a Ambystoma mexicanum, which is a salamander, which is a dummy8", "a newt, which is a salamander, which is a dummy8", "a triton, which is a salamander, which is a dummy8"]}, "12": {"node_name": "reel", "parent_names": ["tool"], "child_names": [], "candidate_sentences": ["a reel, which is a tool"]}, "13": {"node_name": "plate rack", "parent_names": ["kitchen utensil"], "child_names": [], "candidate_sentences": ["a plate rack, which is a kitchen utensil"]}, "14": {"node_name": "marmot", "parent_names": ["rodent", "gnawer"], "child_names": [], "candidate_sentences": ["a marmot, which is a rodent", "a marmot, which is a gnawer"]}, "15": {"node_name": "bee eater", "parent_names": ["coraciiform bird"], "child_names": [], "candidate_sentences": ["a bee eater, which is a coraciiform bird"]}, "16": {"node_name": "stew", "parent_names": ["dish"], "child_names": ["hot pot", "hotpot"], "candidate_sentences": ["a hot pot, which is a stew, which is a dish", "a hotpot, which is a stew, which is a dish"]}, "17": {"node_name": "dummy49", "parent_names": ["building", "edifice"], "child_names": ["restaurant", "eating house", "eating place", "eatery"], "candidate_sentences": ["a restaurant, which is a dummy49, which is a building", "a eating house, which is a dummy49, which is a building", "a eating place, which is a dummy49, which is a building", "a eatery, which is a dummy49, which is a building", "a restaurant, which is a dummy49, which is a edifice", "a eating house, which is a dummy49, which is a edifice", "a eating place, which is a dummy49, which is a edifice", "a eatery, which is a dummy49, which is a edifice"]}, "18": {"node_name": "warthog", "parent_names": ["ungulate", "hoofed mammal"], "child_names": [], "candidate_sentences": ["a warthog, which is a ungulate", "a warthog, which is a hoofed mammal"]}, "19": {"node_name": "towel", "parent_names": ["piece of cloth", "piece of material"], "child_names": ["bath towel", "paper towel"], "candidate_sentences": ["a bath towel, which is a towel, which is a piece of cloth", "a paper towel, which is a towel, which is a piece of cloth", "a bath towel, which is a towel, which is a piece of material", "a paper towel, which is a towel, which is a piece of material"]}, "20": {"node_name": "washer, automatic washer, washing machine", "parent_names": ["home appliance", "household appliance"], "child_names": [], "candidate_sentences": ["a washer, automatic washer, washing machine, which is a home appliance", "a washer, automatic washer, washing machine, which is a household appliance"]}, "21": {"node_name": "hair spray", "parent_names": ["cosmetic"], "child_names": [], "candidate_sentences": ["a hair spray, which is a cosmetic"]}, "22": {"node_name": "lemur", "parent_names": ["primate"], "child_names": ["Madagascar cat", "ring-tailed lemur", "Lemu<PERSON> catta", "indri", "indris", "<PERSON><PERSON><PERSON> indri", "<PERSON><PERSON><PERSON> brevicaudatus"], "candidate_sentences": ["a Madagascar cat, which is a lemur, which is a primate", "a ring-tailed lemur, which is a lemur, which is a primate", "a Lemur catta, which is a lemur, which is a primate", "a indri, which is a lemur, which is a primate", "a indris, which is a lemur, which is a primate", "a Indri indri, which is a lemur, which is a primate", "a Indri brevicaudatus, which is a lemur, which is a primate"]}, "23": {"node_name": "crane", "parent_names": ["aquatic bird"], "child_names": [], "candidate_sentences": ["a crane, which is a aquatic bird"]}, "24": {"node_name": "burrito", "parent_names": ["dish"], "child_names": [], "candidate_sentences": ["a burrito, which is a dish"]}, "25": {"node_name": "coral reef", "parent_names": ["reef"], "child_names": [], "candidate_sentences": ["a coral reef, which is a reef"]}, "26": {"node_name": "cracker", "parent_names": ["baked goods"], "child_names": ["pretzel"], "candidate_sentences": ["a pretzel, which is a cracker, which is a baked goods"]}, "27": {"node_name": "flamingo", "parent_names": ["aquatic bird"], "child_names": [], "candidate_sentences": ["a flamingo, which is a aquatic bird"]}, "28": {"node_name": "salmon", "parent_names": ["bony fish"], "child_names": ["coho", "cohoe", "coho salmon", "blue jack", "silver salmon", "Oncorhynch<PERSON> kisutch"], "candidate_sentences": ["a coho, which is a salmon, which is a bony fish", "a cohoe, which is a salmon, which is a bony fish", "a coho salmon, which is a salmon, which is a bony fish", "a blue jack, which is a salmon, which is a bony fish", "a silver salmon, which is a salmon, which is a bony fish", "a Oncorhynchus kisutch, which is a salmon, which is a bony fish"]}, "29": {"node_name": "horse, Equus caballus", "parent_names": ["ungulate", "hoofed mammal"], "child_names": ["sorrel"], "candidate_sentences": ["a sorrel, which is a horse, Equus caballus, which is a ungulate", "a sorrel, which is a horse, Equus caballus, which is a hoofed mammal"]}, "30": {"node_name": "beetle", "parent_names": ["insect"], "child_names": ["tiger beetle", "ladybug", "lady<PERSON><PERSON>", "lady beetle", "ladybird", "ladybird beetle", "ground beetle", "carabid beetle", "long-horned beetle", "longicorn", "longicorn beetle", "leaf beetle", "chrysomelid", "weevil", "scarabaeid beetle", "<PERSON><PERSON><PERSON><PERSON>", "scarabaean"], "candidate_sentences": ["a tiger beetle, which is a beetle, which is a insect", "a ladybug, which is a beetle, which is a insect", "a ladybeetle, which is a beetle, which is a insect", "a lady beetle, which is a beetle, which is a insect", "a ladybird, which is a beetle, which is a insect", "a ladybird beetle, which is a beetle, which is a insect", "a ground beetle, which is a beetle, which is a insect", "a carabid beetle, which is a beetle, which is a insect", "a long-horned beetle, which is a beetle, which is a insect", "a longicorn, which is a beetle, which is a insect", "a longicorn beetle, which is a beetle, which is a insect", "a leaf beetle, which is a beetle, which is a insect", "a chrysomelid, which is a beetle, which is a insect", "a weevil, which is a beetle, which is a insect", "a scarabaeid beetle, which is a beetle, which is a insect", "a scarabaeid, which is a beetle, which is a insect", "a scarabaean, which is a beetle, which is a insect"]}, "31": {"node_name": "zebra", "parent_names": ["ungulate", "hoofed mammal"], "child_names": [], "candidate_sentences": ["a zebra, which is a ungulate", "a zebra, which is a hoofed mammal"]}, "32": {"node_name": "quilt, comforter, comfort, puff", "parent_names": ["bedclothes", "bed clothing", "bedding"], "child_names": [], "candidate_sentences": ["a quilt, comforter, comfort, puff, which is a bedclothes", "a quilt, comforter, comfort, puff, which is a bed clothing", "a quilt, comforter, comfort, puff, which is a bedding"]}, "33": {"node_name": "artichoke, globe artichoke", "parent_names": ["vegetable", "veggie", "veg"], "child_names": [], "candidate_sentences": ["a artichoke, globe artichoke, which is a vegetable", "a artichoke, globe artichoke, which is a veggie", "a artichoke, globe artichoke, which is a veg"]}, "34": {"node_name": "boat", "parent_names": ["vessel", "watercraft"], "child_names": ["canoe", "fireboat", "gondola", "lifeboat", "speedboat", "yawl", "sailboat", "sailing boat"], "candidate_sentences": ["a canoe, which is a boat, which is a vessel", "a fireboat, which is a boat, which is a vessel", "a gondola, which is a boat, which is a vessel", "a lifeboat, which is a boat, which is a vessel", "a speedboat, which is a boat, which is a vessel", "a yawl, which is a boat, which is a vessel", "a sailboat, which is a boat, which is a vessel", "a sailing boat, which is a boat, which is a vessel", "a canoe, which is a boat, which is a watercraft", "a fireboat, which is a boat, which is a watercraft", "a gondola, which is a boat, which is a watercraft", "a lifeboat, which is a boat, which is a watercraft", "a speedboat, which is a boat, which is a watercraft", "a yawl, which is a boat, which is a watercraft", "a sailboat, which is a boat, which is a watercraft", "a sailing boat, which is a boat, which is a watercraft"]}, "35": {"node_name": "sock", "parent_names": ["footwear", "legwear"], "child_names": [], "candidate_sentences": ["a sock, which is a footwear", "a sock, which is a legwear"]}, "36": {"node_name": "sheath", "parent_names": ["dummy39"], "child_names": ["holster", "scabbard"], "candidate_sentences": ["a holster, which is a sheath, which is a dummy39", "a scabbard, which is a sheath, which is a dummy39"]}, "37": {"node_name": "stage", "parent_names": ["area"], "child_names": [], "candidate_sentences": ["a stage, which is a area"]}, "38": {"node_name": "domestic cat, house cat, Felis domesticus, Felis catus", "parent_names": ["carnivore"], "child_names": ["tabby", "tabby cat", "tiger cat", "Persian cat", "Siamese cat", "Siamese", "Egyptian cat"], "candidate_sentences": ["a tabby, which is a domestic cat, house cat, Felis domesticus, Felis catus, which is a carnivore", "a tabby cat, which is a domestic cat, house cat, Felis domesticus, Felis catus, which is a carnivore", "a tiger cat, which is a domestic cat, house cat, Felis domesticus, Felis catus, which is a carnivore", "a Persian cat, which is a domestic cat, house cat, Felis domesticus, Felis catus, which is a carnivore", "a Siamese cat, which is a domestic cat, house cat, Felis domesticus, Felis catus, which is a carnivore", "a Siamese, which is a domestic cat, house cat, Felis domesticus, Felis catus, which is a carnivore", "a Egyptian cat, which is a domestic cat, house cat, Felis domesticus, Felis catus, which is a carnivore"]}, "39": {"node_name": "orange", "parent_names": ["fruit"], "child_names": [], "candidate_sentences": ["a orange, which is a fruit"]}, "40": {"node_name": "microscope", "parent_names": ["scientific instrument"], "child_names": ["hand glass", "simple microscope", "magnifying glass"], "candidate_sentences": ["a hand glass, which is a microscope, which is a scientific instrument", "a simple microscope, which is a microscope, which is a scientific instrument", "a magnifying glass, which is a microscope, which is a scientific instrument"]}, "41": {"node_name": "stringed instrument", "parent_names": ["musical instrument", "instrument"], "child_names": ["banjo", "cello", "violoncello", "harp", "violin", "fiddle", "guitar"], "candidate_sentences": ["a banjo, which is a stringed instrument, which is a musical instrument", "a cello, which is a stringed instrument, which is a musical instrument", "a violoncello, which is a stringed instrument, which is a musical instrument", "a harp, which is a stringed instrument, which is a musical instrument", "a violin, which is a stringed instrument, which is a musical instrument", "a fiddle, which is a stringed instrument, which is a musical instrument", "a guitar, which is a stringed instrument, which is a musical instrument", "a banjo, which is a stringed instrument, which is a instrument", "a cello, which is a stringed instrument, which is a instrument", "a violoncello, which is a stringed instrument, which is a instrument", "a harp, which is a stringed instrument, which is a instrument", "a violin, which is a stringed instrument, which is a instrument", "a fiddle, which is a stringed instrument, which is a instrument", "a guitar, which is a stringed instrument, which is a instrument"]}, "42": {"node_name": "flatworm, platyhelminth", "parent_names": ["dummy14"], "child_names": [], "candidate_sentences": ["a flatworm, platyhelminth, which is a dummy14"]}, "43": {"node_name": "glove", "parent_names": ["handwear", "hand wear"], "child_names": ["mitten"], "candidate_sentences": ["a mitten, which is a glove, which is a handwear", "a mitten, which is a glove, which is a hand wear"]}, "44": {"node_name": "indigo bunting, indigo finch, indigo bird, Passerina cyanea", "parent_names": ["passerine", "passeriform bird"], "child_names": [], "candidate_sentences": ["a indigo bunting, indigo finch, indigo bird, Passerina cyanea, which is a passerine", "a indigo bunting, indigo finch, indigo bird, Passerina cyanea, which is a passeriform bird"]}, "45": {"node_name": "bridge, span", "parent_names": ["dummy73"], "child_names": ["steel arch bridge", "suspension bridge", "viaduct"], "candidate_sentences": ["a steel arch bridge, which is a bridge, span, which is a dummy73", "a suspension bridge, which is a bridge, span, which is a dummy73", "a viaduct, which is a bridge, span, which is a dummy73"]}, "46": {"node_name": "sea cow, sirenian mammal, sirenian", "parent_names": ["aquatic mammal"], "child_names": ["dugong", "<PERSON><PERSON> dugon"], "candidate_sentences": ["a dugong, which is a sea cow, sirenian mammal, sirenian, which is a aquatic mammal", "a Dugong dugon, which is a sea cow, sirenian mammal, sirenian, which is a aquatic mammal"]}, "47": {"node_name": "place of worship, house of prayer, house of God, house of worship", "parent_names": ["building", "edifice"], "child_names": ["church", "church building", "mosque", "shrine"], "candidate_sentences": ["a church, which is a place of worship, house of prayer, house of God, house of worship, which is a building", "a church building, which is a place of worship, house of prayer, house of God, house of worship, which is a building", "a mosque, which is a place of worship, house of prayer, house of God, house of worship, which is a building", "a shrine, which is a place of worship, house of prayer, house of God, house of worship, which is a building", "a church, which is a place of worship, house of prayer, house of God, house of worship, which is a edifice", "a church building, which is a place of worship, house of prayer, house of God, house of worship, which is a edifice", "a mosque, which is a place of worship, house of prayer, house of God, house of worship, which is a edifice", "a shrine, which is a place of worship, house of prayer, house of God, house of worship, which is a edifice"]}, "48": {"node_name": "broccoli", "parent_names": ["vegetable", "veggie", "veg"], "child_names": [], "candidate_sentences": ["a broccoli, which is a vegetable", "a broccoli, which is a veggie", "a broccoli, which is a veg"]}, "49": {"node_name": "snow leopard, ounce, Panthera uncia", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a snow leopard, ounce, Panthera uncia, which is a carnivore"]}, "50": {"node_name": "balloon", "parent_names": ["aircraft"], "child_names": [], "candidate_sentences": ["a balloon, which is a aircraft"]}, "51": {"node_name": "acarine", "parent_names": ["arachnid", "arachnoid"], "child_names": ["tick"], "candidate_sentences": ["a tick, which is a acarine, which is a arachnid", "a tick, which is a acarine, which is a arachnoid"]}, "52": {"node_name": "scarf", "parent_names": ["neckwear"], "child_names": ["feather boa", "boa", "stole"], "candidate_sentences": ["a feather boa, which is a scarf, which is a neckwear", "a boa, which is a scarf, which is a neckwear", "a stole, which is a scarf, which is a neckwear"]}, "53": {"node_name": "bun, roll", "parent_names": ["baked goods"], "child_names": ["bagel", "beigel"], "candidate_sentences": ["a bagel, which is a bun, roll, which is a baked goods", "a beigel, which is a bun, roll, which is a baked goods"]}, "54": {"node_name": "chain saw, chainsaw", "parent_names": ["tool"], "child_names": [], "candidate_sentences": ["a chain saw, chainsaw, which is a tool"]}, "55": {"node_name": "grasshopper, hopper", "parent_names": ["insect"], "child_names": [], "candidate_sentences": ["a grasshopper, hopper, which is a insect"]}, "56": {"node_name": "brambling, Fringilla montifringilla", "parent_names": ["passerine", "passeriform bird"], "child_names": [], "candidate_sentences": ["a brambling, Fringilla montifringilla, which is a passerine", "a brambling, Fring<PERSON> montifringilla, which is a passeriform bird"]}, "57": {"node_name": "breakwater, groin, groyne, mole, bulwark, seawall, jetty", "parent_names": ["barrier"], "child_names": [], "candidate_sentences": ["a breakwater, groin, groyne, mole, bulwark, seawall, jetty, which is a barrier"]}, "58": {"node_name": "guinea pig, Cavia co<PERSON>a", "parent_names": ["rodent", "gnawer"], "child_names": [], "candidate_sentences": ["a guinea pig, <PERSON><PERSON> co<PERSON>a, which is a rodent", "a guinea pig, <PERSON><PERSON> co<PERSON>a, which is a gnawer"]}, "59": {"node_name": "espresso", "parent_names": ["coffee", "java"], "child_names": [], "candidate_sentences": ["a espresso, which is a coffee", "a espresso, which is a java"]}, "60": {"node_name": "bus, autobus, coach, charabanc, double-decker, jitney, motorbus, motorcoach, omnibus, passenger vehicle", "parent_names": ["motor vehicle", "automotive vehicle"], "child_names": ["minibus", "recreational vehicle", "RV", "R.V.", "school bus", "trolleybus", "trolley coach", "trackless trolley"], "candidate_sentences": ["a minibus, which is a bus, autobus, coach, charabanc, double-decker, jitney, motorbus, motorcoach, omnibus, passenger vehicle, which is a motor vehicle", "a recreational vehicle, which is a bus, autobus, coach, charabanc, double-decker, jitney, motorbus, motorcoach, omnibus, passenger vehicle, which is a motor vehicle", "a RV, which is a bus, autobus, coach, charabanc, double-decker, jitney, motorbus, motorcoach, omnibus, passenger vehicle, which is a motor vehicle", "a R.V., which is a bus, autobus, coach, charabanc, double-decker, jitney, motorbus, motorcoach, omnibus, passenger vehicle, which is a motor vehicle", "a school bus, which is a bus, autobus, coach, charabanc, double-decker, jitney, motorbus, motorcoach, omnibus, passenger vehicle, which is a motor vehicle", "a trolleybus, which is a bus, autobus, coach, charabanc, double-decker, jitney, motorbus, motorcoach, omnibus, passenger vehicle, which is a motor vehicle", "a trolley coach, which is a bus, autobus, coach, charabanc, double-decker, jitney, motorbus, motorcoach, omnibus, passenger vehicle, which is a motor vehicle", "a trackless trolley, which is a bus, autobus, coach, charabanc, double-decker, jitney, motorbus, motorcoach, omnibus, passenger vehicle, which is a motor vehicle", "a minibus, which is a bus, autobus, coach, charabanc, double-decker, jitney, motorbus, motorcoach, omnibus, passenger vehicle, which is a automotive vehicle", "a recreational vehicle, which is a bus, autobus, coach, charabanc, double-decker, jitney, motorbus, motorcoach, omnibus, passenger vehicle, which is a automotive vehicle", "a RV, which is a bus, autobus, coach, charabanc, double-decker, jitney, motorbus, motorcoach, omnibus, passenger vehicle, which is a automotive vehicle", "a R.V., which is a bus, autobus, coach, charabanc, double-decker, jitney, motorbus, motorcoach, omnibus, passenger vehicle, which is a automotive vehicle", "a school bus, which is a bus, autobus, coach, charabanc, double-decker, jitney, motorbus, motorcoach, omnibus, passenger vehicle, which is a automotive vehicle", "a trolleybus, which is a bus, autobus, coach, charabanc, double-decker, jitney, motorbus, motorcoach, omnibus, passenger vehicle, which is a automotive vehicle", "a trolley coach, which is a bus, autobus, coach, charabanc, double-decker, jitney, motorbus, motorcoach, omnibus, passenger vehicle, which is a automotive vehicle", "a trackless trolley, which is a bus, autobus, coach, charabanc, double-decker, jitney, motorbus, motorcoach, omnibus, passenger vehicle, which is a automotive vehicle"]}, "61": {"node_name": "cock", "parent_names": ["dummy0"], "child_names": [], "candidate_sentences": ["a cock, which is a dummy0"]}, "62": {"node_name": "wombat", "parent_names": ["marsupial", "pouched mammal"], "child_names": [], "candidate_sentences": ["a wombat, which is a marsupial", "a wombat, which is a pouched mammal"]}, "63": {"node_name": "hermit crab", "parent_names": ["crustacean"], "child_names": [], "candidate_sentences": ["a hermit crab, which is a crustacean"]}, "64": {"node_name": "banana", "parent_names": ["fruit"], "child_names": [], "candidate_sentences": ["a banana, which is a fruit"]}, "65": {"node_name": "damselfish, demoiselle", "parent_names": ["bony fish"], "child_names": ["anemone fish"], "candidate_sentences": ["a anemone fish, which is a damselfish, demoiselle, which is a bony fish"]}, "66": {"node_name": "hen", "parent_names": ["dummy1"], "child_names": [], "candidate_sentences": ["a hen, which is a dummy1"]}, "67": {"node_name": "pineapple, ananas", "parent_names": ["fruit"], "child_names": [], "candidate_sentences": ["a pineapple, ananas, which is a fruit"]}, "68": {"node_name": "outbuilding", "parent_names": ["building", "edifice"], "child_names": ["barn", "greenhouse", "nursery", "glasshouse", "shed"], "candidate_sentences": ["a barn, which is a outbuilding, which is a building", "a greenhouse, which is a outbuilding, which is a building", "a nursery, which is a outbuilding, which is a building", "a glasshouse, which is a outbuilding, which is a building", "a shed, which is a outbuilding, which is a building", "a barn, which is a outbuilding, which is a edifice", "a greenhouse, which is a outbuilding, which is a edifice", "a nursery, which is a outbuilding, which is a edifice", "a glasshouse, which is a outbuilding, which is a edifice", "a shed, which is a outbuilding, which is a edifice"]}, "69": {"node_name": "tray", "parent_names": ["kitchen utensil"], "child_names": [], "candidate_sentences": ["a tray, which is a kitchen utensil"]}, "70": {"node_name": "dock, dockage, docking facility", "parent_names": ["landing", "landing place"], "child_names": [], "candidate_sentences": ["a dock, dockage, docking facility, which is a landing", "a dock, dockage, docking facility, which is a landing place"]}, "71": {"node_name": "wild boar, boar, Sus scrofa", "parent_names": ["ungulate", "hoofed mammal"], "child_names": [], "candidate_sentences": ["a wild boar, boar, Sus scrofa, which is a ungulate", "a wild boar, boar, Sus scrofa, which is a hoofed mammal"]}, "72": {"node_name": "necktie, tie", "parent_names": ["neckwear"], "child_names": ["bolo tie", "bolo", "bola tie", "bola", "bow tie", "bow-tie", "bowtie", "Windsor tie"], "candidate_sentences": ["a bolo tie, which is a necktie, tie, which is a neckwear", "a bolo, which is a necktie, tie, which is a neckwear", "a bola tie, which is a necktie, tie, which is a neckwear", "a bola, which is a necktie, tie, which is a neckwear", "a bow tie, which is a necktie, tie, which is a neckwear", "a bow-tie, which is a necktie, tie, which is a neckwear", "a bowtie, which is a necktie, tie, which is a neckwear", "a Windsor tie, which is a necktie, tie, which is a neckwear"]}, "73": {"node_name": "walking stick, walkingstick, stick insect", "parent_names": ["insect"], "child_names": [], "candidate_sentences": ["a walking stick, walkingstick, stick insect, which is a insect"]}, "74": {"node_name": "toucan", "parent_names": ["piciform bird"], "child_names": [], "candidate_sentences": ["a toucan, which is a piciform bird"]}, "75": {"node_name": "frog, toad, toad frog, anuran, batrachian, salientian", "parent_names": ["dummy7"], "child_names": ["tree frog", "tree-frog", "tailed frog", "bell toad", "ribbed toad", "tailed toad", "Ascap<PERSON> trui", "true frog", "ranid"], "candidate_sentences": ["a tree frog, which is a frog, toad, toad frog, anuran, batrachian, salientian, which is a dummy7", "a tree-frog, which is a frog, toad, toad frog, anuran, batrachian, salientian, which is a dummy7", "a tailed frog, which is a frog, toad, toad frog, anuran, batrachian, salientian, which is a dummy7", "a bell toad, which is a frog, toad, toad frog, anuran, batrachian, salientian, which is a dummy7", "a ribbed toad, which is a frog, toad, toad frog, anuran, batrachian, salientian, which is a dummy7", "a tailed toad, which is a frog, toad, toad frog, anuran, batrachian, salientian, which is a dummy7", "a Ascaphus trui, which is a frog, toad, toad frog, anuran, batrachian, salientian, which is a dummy7", "a true frog, which is a frog, toad, toad frog, anuran, batrachian, salientian, which is a dummy7", "a ranid, which is a frog, toad, toad frog, anuran, batrachian, salientian, which is a dummy7"]}, "76": {"node_name": "harvestman, daddy longlegs, Phalangium opilio", "parent_names": ["arachnid", "arachnoid"], "child_names": [], "candidate_sentences": ["a harvestman, daddy longlegs, Phalangium opilio, which is a arachnid", "a harvestman, daddy longlegs, Phalangium opilio, which is a arachnoid"]}, "77": {"node_name": "screw", "parent_names": ["tool"], "child_names": [], "candidate_sentences": ["a screw, which is a tool"]}, "78": {"node_name": "spatula", "parent_names": ["kitchen utensil"], "child_names": [], "candidate_sentences": ["a spatula, which is a kitchen utensil"]}, "79": {"node_name": "reservoir", "parent_names": ["tank", "storage tank"], "child_names": ["water tower"], "candidate_sentences": ["a water tower, which is a reservoir, which is a tank", "a water tower, which is a reservoir, which is a storage tank"]}, "80": {"node_name": "lacewing, lacewing fly", "parent_names": ["insect"], "child_names": [], "candidate_sentences": ["a lacewing, lacewing fly, which is a insect"]}, "81": {"node_name": "parrot", "parent_names": ["dummy6"], "child_names": ["African grey", "African gray", "Psitta<PERSON> erithacus", "macaw", "cockatoo", "lory"], "candidate_sentences": ["a African grey, which is a parrot, which is a dummy6", "a African gray, which is a parrot, which is a dummy6", "a Psittacus erithacus, which is a parrot, which is a dummy6", "a macaw, which is a parrot, which is a dummy6", "a cockatoo, which is a parrot, which is a dummy6", "a lory, which is a parrot, which is a dummy6"]}, "82": {"node_name": "opener", "parent_names": ["kitchen utensil"], "child_names": ["can opener", "tin opener", "bottle opener"], "candidate_sentences": ["a can opener, which is a opener, which is a kitchen utensil", "a tin opener, which is a opener, which is a kitchen utensil", "a bottle opener, which is a opener, which is a kitchen utensil"]}, "83": {"node_name": "gar, garfish, garpike, billfish, Lepisosteus osseus", "parent_names": ["bony fish"], "child_names": [], "candidate_sentences": ["a gar, garfish, garpike, billfish, Lepisosteus osseus, which is a bony fish"]}, "84": {"node_name": "lawn mower, mower", "parent_names": ["home appliance", "household appliance"], "child_names": [], "candidate_sentences": ["a lawn mower, mower, which is a home appliance", "a lawn mower, mower, which is a household appliance"]}, "85": {"node_name": "theater, theatre, house", "parent_names": ["building", "edifice"], "child_names": ["cinema", "movie theater", "movie theatre", "movie house", "picture palace"], "candidate_sentences": ["a cinema, which is a theater, theatre, house, which is a building", "a movie theater, which is a theater, theatre, house, which is a building", "a movie theatre, which is a theater, theatre, house, which is a building", "a movie house, which is a theater, theatre, house, which is a building", "a picture palace, which is a theater, theatre, house, which is a building", "a cinema, which is a theater, theatre, house, which is a edifice", "a movie theater, which is a theater, theatre, house, which is a edifice", "a movie theatre, which is a theater, theatre, house, which is a edifice", "a movie house, which is a theater, theatre, house, which is a edifice", "a picture palace, which is a theater, theatre, house, which is a edifice"]}, "86": {"node_name": "firearm, piece, small-arm", "parent_names": ["weapon", "arm", "weapon system"], "child_names": ["rifle", "pistol", "handgun", "side arm", "shooting iron", "automatic firearm", "automatic gun", "automatic weapon"], "candidate_sentences": ["a rifle, which is a firearm, piece, small-arm, which is a weapon", "a pistol, which is a firearm, piece, small-arm, which is a weapon", "a handgun, which is a firearm, piece, small-arm, which is a weapon", "a side arm, which is a firearm, piece, small-arm, which is a weapon", "a shooting iron, which is a firearm, piece, small-arm, which is a weapon", "a automatic firearm, which is a firearm, piece, small-arm, which is a weapon", "a automatic gun, which is a firearm, piece, small-arm, which is a weapon", "a automatic weapon, which is a firearm, piece, small-arm, which is a weapon", "a rifle, which is a firearm, piece, small-arm, which is a arm", "a pistol, which is a firearm, piece, small-arm, which is a arm", "a handgun, which is a firearm, piece, small-arm, which is a arm", "a side arm, which is a firearm, piece, small-arm, which is a arm", "a shooting iron, which is a firearm, piece, small-arm, which is a arm", "a automatic firearm, which is a firearm, piece, small-arm, which is a arm", "a automatic gun, which is a firearm, piece, small-arm, which is a arm", "a automatic weapon, which is a firearm, piece, small-arm, which is a arm", "a rifle, which is a firearm, piece, small-arm, which is a weapon system", "a pistol, which is a firearm, piece, small-arm, which is a weapon system", "a handgun, which is a firearm, piece, small-arm, which is a weapon system", "a side arm, which is a firearm, piece, small-arm, which is a weapon system", "a shooting iron, which is a firearm, piece, small-arm, which is a weapon system", "a automatic firearm, which is a firearm, piece, small-arm, which is a weapon system", "a automatic gun, which is a firearm, piece, small-arm, which is a weapon system", "a automatic weapon, which is a firearm, piece, small-arm, which is a weapon system"]}, "87": {"node_name": "microphone, mike", "parent_names": ["electronic equipment"], "child_names": [], "candidate_sentences": ["a microphone, mike, which is a electronic equipment"]}, "88": {"node_name": "promontory, headland, head, foreland", "parent_names": ["dummy69"], "child_names": [], "candidate_sentences": ["a promontory, headland, head, foreland, which is a dummy69"]}, "89": {"node_name": "<PERSON><PERSON><PERSON><PERSON>", "parent_names": ["gallinaceous bird", "gallinacean"], "child_names": ["peacock", "quail", "partridge"], "candidate_sentences": ["a peacock, which is a phasianid, which is a gallinaceous bird", "a quail, which is a phasianid, which is a gallinaceous bird", "a partridge, which is a phasianid, which is a gallinaceous bird", "a peacock, which is a phasianid, which is a gallinacean", "a quail, which is a phasianid, which is a gallinacean", "a partridge, which is a phasianid, which is a gallinacean"]}, "90": {"node_name": "curtain, drape, drapery, mantle, pall", "parent_names": ["dummy56"], "child_names": ["shower curtain", "theater curtain", "theatre curtain"], "candidate_sentences": ["a shower curtain, which is a curtain, drape, drapery, mantle, pall, which is a dummy56", "a theater curtain, which is a curtain, drape, drapery, mantle, pall, which is a dummy56", "a theatre curtain, which is a curtain, drape, drapery, mantle, pall, which is a dummy56"]}, "91": {"node_name": "seashore, coast, seacoast, sea-coast", "parent_names": ["shore"], "child_names": [], "candidate_sentences": ["a seashore, coast, seacoast, sea-coast, which is a shore"]}, "92": {"node_name": "motorcycle, bike", "parent_names": ["motor vehicle", "automotive vehicle"], "child_names": ["motor scooter", "scooter", "minibike", "motorbike"], "candidate_sentences": ["a motor scooter, which is a motorcycle, bike, which is a motor vehicle", "a scooter, which is a motorcycle, bike, which is a motor vehicle", "a minibike, which is a motorcycle, bike, which is a motor vehicle", "a motorbike, which is a motorcycle, bike, which is a motor vehicle", "a motor scooter, which is a motorcycle, bike, which is a automotive vehicle", "a scooter, which is a motorcycle, bike, which is a automotive vehicle", "a minibike, which is a motorcycle, bike, which is a automotive vehicle", "a motorbike, which is a motorcycle, bike, which is a automotive vehicle"]}, "93": {"node_name": "jewelry, jewellery", "parent_names": ["neckwear"], "child_names": ["necklace"], "candidate_sentences": ["a necklace, which is a jewelry, jewellery, which is a neckwear"]}, "94": {"node_name": "wine, vino", "parent_names": ["alcohol", "alcoholic drink", "alcoholic beverage", "intoxicant", "inebriant"], "child_names": ["red wine"], "candidate_sentences": ["a red wine, which is a wine, vino, which is a alcohol", "a red wine, which is a wine, vino, which is a alcoholic drink", "a red wine, which is a wine, vino, which is a alcoholic beverage", "a red wine, which is a wine, vino, which is a intoxicant", "a red wine, which is a wine, vino, which is a inebriant"]}, "95": {"node_name": "mercantile establishment, retail store, sales outlet, outlet", "parent_names": ["building", "edifice"], "child_names": ["bakery", "bakeshop", "bakehouse", "barbershop", "bookshop", "bookstore", "bookstall", "butcher shop", "meat market", "confectionery", "confectionary", "candy store", "grocery store", "grocery", "food market", "market", "shoe shop", "shoe-shop", "shoe store", "tobacco shop", "tobacconist shop", "tobacconist", "toyshop"], "candidate_sentences": ["a bakery, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a bakeshop, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a bakehouse, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a barbershop, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a bookshop, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a bookstore, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a bookstall, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a butcher shop, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a meat market, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a confectionery, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a confectionary, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a candy store, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a grocery store, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a grocery, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a food market, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a market, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a shoe shop, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a shoe-shop, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a shoe store, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a tobacco shop, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a tobacconist shop, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a tobacconist, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a toyshop, which is a mercantile establishment, retail store, sales outlet, outlet, which is a building", "a bakery, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a bakeshop, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a bakehouse, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a barbershop, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a bookshop, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a bookstore, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a bookstall, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a butcher shop, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a meat market, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a confectionery, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a confectionary, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a candy store, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a grocery store, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a grocery, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a food market, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a market, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a shoe shop, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a shoe-shop, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a shoe store, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a tobacco shop, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a tobacconist shop, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a tobacconist, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice", "a toyshop, which is a mercantile establishment, retail store, sales outlet, outlet, which is a edifice"]}, "96": {"node_name": "mantis, mantid", "parent_names": ["insect"], "child_names": [], "candidate_sentences": ["a mantis, mantid, which is a insect"]}, "97": {"node_name": "junco, snowbird", "parent_names": ["passerine", "passeriform bird"], "child_names": [], "candidate_sentences": ["a junco, snowbird, which is a passerine", "a junco, snowbird, which is a passeriform bird"]}, "98": {"node_name": "bobsled, bobsleigh, bob", "parent_names": ["sled", "sledge", "sleigh"], "child_names": [], "candidate_sentences": ["a bobsled, bobsleigh, bob, which is a sled", "a bobsled, bobsleigh, bob, which is a sledge", "a bobsled, bobsleigh, bob, which is a sleigh"]}, "99": {"node_name": "potpie", "parent_names": ["dish"], "child_names": [], "candidate_sentences": ["a potpie, which is a dish"]}, "100": {"node_name": "hamster", "parent_names": ["rodent", "gnawer"], "child_names": [], "candidate_sentences": ["a hamster, which is a rodent", "a hamster, which is a gnawer"]}, "101": {"node_name": "geyser", "parent_names": ["spring", "fountain", "outflow", "outpouring", "natural spring"], "child_names": [], "candidate_sentences": ["a geyser, which is a spring", "a geyser, which is a fountain", "a geyser, which is a outflow", "a geyser, which is a outpouring", "a geyser, which is a natural spring"]}, "102": {"node_name": "snipe", "parent_names": ["aquatic bird"], "child_names": ["dowitcher"], "candidate_sentences": ["a dowitcher, which is a snipe, which is a aquatic bird"]}, "103": {"node_name": "ostrich, <PERSON><PERSON><PERSON>o camelus", "parent_names": ["ratite", "ratite bird", "flightless bird"], "child_names": [], "candidate_sentences": ["a ostrich, <PERSON><PERSON>thio camelus, which is a ratite", "a ostrich, <PERSON><PERSON><PERSON><PERSON> camelus, which is a ratite bird", "a ostrich, <PERSON><PERSON><PERSON><PERSON> camelus, which is a flightless bird"]}, "104": {"node_name": "slug", "parent_names": ["gastropod", "univalve"], "child_names": [], "candidate_sentences": ["a slug, which is a gastropod", "a slug, which is a univalve"]}, "105": {"node_name": "cougar, puma, catamount, mountain lion, painter, panther, Felis concolor", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a cougar, puma, catamount, mountain lion, painter, panther, <PERSON>lis concolor, which is a carnivore"]}, "106": {"node_name": "brace", "parent_names": ["neckwear"], "child_names": ["neck brace"], "candidate_sentences": ["a neck brace, which is a brace, which is a neckwear"]}, "107": {"node_name": "hand blower, blow dryer, blow drier, hair dryer, hair drier", "parent_names": ["dryer", "drier"], "child_names": [], "candidate_sentences": ["a hand blower, blow dryer, blow drier, hair dryer, hair drier, which is a dryer", "a hand blower, blow dryer, blow drier, hair dryer, hair drier, which is a drier"]}, "108": {"node_name": "dragonfly, darning needle, devil's darning needle, sewing needle, snake feeder, snake doctor, mosquito hawk, skeeter hawk", "parent_names": ["insect"], "child_names": [], "candidate_sentences": ["a dragonfly, darning needle, devil's darning needle, sewing needle, snake feeder, snake doctor, mosquito hawk, skeeter hawk, which is a insect"]}, "109": {"node_name": "hairpiece, false hair, postiche", "parent_names": ["headdress", "headgear"], "child_names": ["wig"], "candidate_sentences": ["a wig, which is a hairpiece, false hair, postiche, which is a headdress", "a wig, which is a hairpiece, false hair, postiche, which is a headgear"]}, "110": {"node_name": "shirt", "parent_names": ["garment"], "child_names": ["jersey", "T-shirt", "tee shirt"], "candidate_sentences": ["a jersey, which is a shirt, which is a garment", "a T-shirt, which is a shirt, which is a garment", "a tee shirt, which is a shirt, which is a garment"]}, "111": {"node_name": "spoon", "parent_names": ["tableware"], "child_names": ["wooden spoon"], "candidate_sentences": ["a wooden spoon, which is a spoon, which is a tableware"]}, "112": {"node_name": "soup", "parent_names": ["dish"], "child_names": ["consomme"], "candidate_sentences": ["a consomme, which is a soup, which is a dish"]}, "113": {"node_name": "fountain", "parent_names": ["dummy67"], "child_names": [], "candidate_sentences": ["a fountain, which is a dummy67"]}, "114": {"node_name": "strawberry", "parent_names": ["fruit"], "child_names": [], "candidate_sentences": ["a strawberry, which is a fruit"]}, "115": {"node_name": "earthstar", "parent_names": ["basidiomycete", "basidiomycetous fungi"], "child_names": [], "candidate_sentences": ["a earthstar, which is a basidiomycete", "a earthstar, which is a basidiomycetous fungi"]}, "116": {"node_name": "sea cucumber, holothurian", "parent_names": ["dummy12"], "child_names": [], "candidate_sentences": ["a sea cucumber, holothurian, which is a dummy12"]}, "117": {"node_name": "seal", "parent_names": ["aquatic mammal"], "child_names": ["sea lion"], "candidate_sentences": ["a sea lion, which is a seal, which is a aquatic mammal"]}, "118": {"node_name": "modem", "parent_names": ["electronic equipment"], "child_names": [], "candidate_sentences": ["a modem, which is a electronic equipment"]}, "119": {"node_name": "gyromitra", "parent_names": ["ascomycete"], "child_names": [], "candidate_sentences": ["a gyromitra, which is a ascomycete"]}, "120": {"node_name": "wind instrument, wind", "parent_names": ["musical instrument", "instrument"], "child_names": ["bassoon", "cornet", "horn", "trumpet", "trump", "flute", "transverse flute", "French horn", "horn", "harmonica", "mouth organ", "harp", "mouth harp", "oboe", "hautboy", "<PERSON><PERSON><PERSON><PERSON>", "ocarina", "sweet potato", "sax", "saxophone", "trombone", "pipe"], "candidate_sentences": ["a bassoon, which is a wind instrument, wind, which is a musical instrument", "a cornet, which is a wind instrument, wind, which is a musical instrument", "a horn, which is a wind instrument, wind, which is a musical instrument", "a trumpet, which is a wind instrument, wind, which is a musical instrument", "a trump, which is a wind instrument, wind, which is a musical instrument", "a flute, which is a wind instrument, wind, which is a musical instrument", "a transverse flute, which is a wind instrument, wind, which is a musical instrument", "a French horn, which is a wind instrument, wind, which is a musical instrument", "a horn, which is a wind instrument, wind, which is a musical instrument", "a harmonica, which is a wind instrument, wind, which is a musical instrument", "a mouth organ, which is a wind instrument, wind, which is a musical instrument", "a harp, which is a wind instrument, wind, which is a musical instrument", "a mouth harp, which is a wind instrument, wind, which is a musical instrument", "a oboe, which is a wind instrument, wind, which is a musical instrument", "a hautboy, which is a wind instrument, wind, which is a musical instrument", "a hautbois, which is a wind instrument, wind, which is a musical instrument", "a ocarina, which is a wind instrument, wind, which is a musical instrument", "a sweet potato, which is a wind instrument, wind, which is a musical instrument", "a sax, which is a wind instrument, wind, which is a musical instrument", "a saxophone, which is a wind instrument, wind, which is a musical instrument", "a trombone, which is a wind instrument, wind, which is a musical instrument", "a pipe, which is a wind instrument, wind, which is a musical instrument", "a bassoon, which is a wind instrument, wind, which is a instrument", "a cornet, which is a wind instrument, wind, which is a instrument", "a horn, which is a wind instrument, wind, which is a instrument", "a trumpet, which is a wind instrument, wind, which is a instrument", "a trump, which is a wind instrument, wind, which is a instrument", "a flute, which is a wind instrument, wind, which is a instrument", "a transverse flute, which is a wind instrument, wind, which is a instrument", "a French horn, which is a wind instrument, wind, which is a instrument", "a horn, which is a wind instrument, wind, which is a instrument", "a harmonica, which is a wind instrument, wind, which is a instrument", "a mouth organ, which is a wind instrument, wind, which is a instrument", "a harp, which is a wind instrument, wind, which is a instrument", "a mouth harp, which is a wind instrument, wind, which is a instrument", "a oboe, which is a wind instrument, wind, which is a instrument", "a hautboy, which is a wind instrument, wind, which is a instrument", "a hautbois, which is a wind instrument, wind, which is a instrument", "a ocarina, which is a wind instrument, wind, which is a instrument", "a sweet potato, which is a wind instrument, wind, which is a instrument", "a sax, which is a wind instrument, wind, which is a instrument", "a saxophone, which is a wind instrument, wind, which is a instrument", "a trombone, which is a wind instrument, wind, which is a instrument", "a pipe, which is a wind instrument, wind, which is a instrument"]}, "121": {"node_name": "mongoose", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a mongoose, which is a carnivore"]}, "122": {"node_name": "memorial, monument", "parent_names": ["dummy77"], "child_names": ["brass", "memorial tablet", "plaque", "megalith", "megalithic structure", "triumphal arch"], "candidate_sentences": ["a brass, which is a memorial, monument, which is a dummy77", "a memorial tablet, which is a memorial, monument, which is a dummy77", "a plaque, which is a memorial, monument, which is a dummy77", "a megalith, which is a memorial, monument, which is a dummy77", "a megalithic structure, which is a memorial, monument, which is a dummy77", "a triumphal arch, which is a memorial, monument, which is a dummy77"]}, "123": {"node_name": "car, auto, automobile, machine, motorcar", "parent_names": ["motor vehicle", "automotive vehicle"], "child_names": ["ambulance", "beach wagon", "station wagon", "wagon", "estate car", "beach waggon", "station waggon", "waggon", "cab", "hack", "taxi", "taxicab", "convertible", "go-kart", "golfcart", "golf cart", "jeep", "landrover", "limousine", "limo", "minivan", "Model T", "police van", "police wagon", "paddy wagon", "patrol wagon", "wagon", "black Maria", "racer", "race car", "racing car", "sports car", "sport car"], "candidate_sentences": ["a ambulance, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a beach wagon, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a station wagon, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a wagon, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a estate car, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a beach waggon, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a station waggon, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a waggon, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a cab, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a hack, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a taxi, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a taxicab, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a convertible, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a go-kart, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a golfcart, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a golf cart, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a jeep, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a landrover, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a limousine, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a limo, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a minivan, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a Model T, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a police van, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a police wagon, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a paddy wagon, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a patrol wagon, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a wagon, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a black Maria, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a racer, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a race car, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a racing car, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a sports car, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a sport car, which is a car, auto, automobile, machine, motorcar, which is a motor vehicle", "a ambulance, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a beach wagon, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a station wagon, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a wagon, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a estate car, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a beach waggon, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a station waggon, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a waggon, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a cab, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a hack, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a taxi, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a taxicab, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a convertible, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a go-kart, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a golfcart, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a golf cart, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a jeep, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a landrover, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a limousine, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a limo, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a minivan, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a Model T, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a police van, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a police wagon, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a paddy wagon, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a patrol wagon, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a wagon, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a black Maria, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a racer, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a race car, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a racing car, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a sports car, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle", "a sport car, which is a car, auto, automobile, machine, motorcar, which is a automotive vehicle"]}, "124": {"node_name": "projector", "parent_names": ["electronic equipment"], "child_names": [], "candidate_sentences": ["a projector, which is a electronic equipment"]}, "125": {"node_name": "sandwich", "parent_names": ["dish"], "child_names": ["hotdog", "hot dog", "red hot", "hamburger", "beefburger", "burger"], "candidate_sentences": ["a hotdog, which is a sandwich, which is a dish", "a hot dog, which is a sandwich, which is a dish", "a red hot, which is a sandwich, which is a dish", "a hamburger, which is a sandwich, which is a dish", "a beefburger, which is a sandwich, which is a dish", "a burger, which is a sandwich, which is a dish"]}, "126": {"node_name": "fan", "parent_names": ["home appliance", "household appliance"], "child_names": ["electric fan", "blower"], "candidate_sentences": ["a electric fan, which is a fan, which is a home appliance", "a blower, which is a fan, which is a home appliance", "a electric fan, which is a fan, which is a household appliance", "a blower, which is a fan, which is a household appliance"]}, "127": {"node_name": "grouse", "parent_names": ["gallinaceous bird", "gallinacean"], "child_names": ["black grouse", "ptarmigan", "ruffed grouse", "partridge", "Bonasa umbellus", "prairie chicken", "prairie grouse", "prairie fowl"], "candidate_sentences": ["a black grouse, which is a grouse, which is a gallinaceous bird", "a ptarmigan, which is a grouse, which is a gallinaceous bird", "a ruffed grouse, which is a grouse, which is a gallinaceous bird", "a partridge, which is a grouse, which is a gallinaceous bird", "a Bonasa umbellus, which is a grouse, which is a gallinaceous bird", "a prairie chicken, which is a grouse, which is a gallinaceous bird", "a prairie grouse, which is a grouse, which is a gallinaceous bird", "a prairie fowl, which is a grouse, which is a gallinaceous bird", "a black grouse, which is a grouse, which is a gallinacean", "a ptarmigan, which is a grouse, which is a gallinacean", "a ruffed grouse, which is a grouse, which is a gallinacean", "a partridge, which is a grouse, which is a gallinacean", "a Bonasa umbellus, which is a grouse, which is a gallinacean", "a prairie chicken, which is a grouse, which is a gallinacean", "a prairie grouse, which is a grouse, which is a gallinacean", "a prairie fowl, which is a grouse, which is a gallinacean"]}, "128": {"node_name": "hawk", "parent_names": ["bird of prey", "raptor", "raptorial bird"], "child_names": ["kite"], "candidate_sentences": ["a kite, which is a hawk, which is a bird of prey", "a kite, which is a hawk, which is a raptor", "a kite, which is a hawk, which is a raptorial bird"]}, "129": {"node_name": "owl, bird of <PERSON><PERSON>, bird of night, hooter", "parent_names": ["bird of prey", "raptor", "raptorial bird"], "child_names": ["great grey owl", "great gray owl", "<PERSON><PERSON> nebulosa"], "candidate_sentences": ["a great grey owl, which is a owl, bird of <PERSON><PERSON>, bird of night, hooter, which is a bird of prey", "a great gray owl, which is a owl, bird of <PERSON><PERSON>, bird of night, hooter, which is a bird of prey", "a Strix nebulosa, which is a owl, bird of Minerva, bird of night, hooter, which is a bird of prey", "a great grey owl, which is a owl, bird of <PERSON><PERSON>, bird of night, hooter, which is a raptor", "a great gray owl, which is a owl, bird of <PERSON><PERSON>, bird of night, hooter, which is a raptor", "a Strix nebulosa, which is a owl, bird of <PERSON>rva, bird of night, hooter, which is a raptor", "a great grey owl, which is a owl, bird of <PERSON><PERSON>, bird of night, hooter, which is a raptorial bird", "a great gray owl, which is a owl, bird of <PERSON><PERSON>, bird of night, hooter, which is a raptorial bird", "a Strix nebulosa, which is a owl, bird of <PERSON>rva, bird of night, hooter, which is a raptorial bird"]}, "130": {"node_name": "fly", "parent_names": ["insect"], "child_names": [], "candidate_sentences": ["a fly, which is a insect"]}, "131": {"node_name": "camel", "parent_names": ["ungulate", "hoofed mammal"], "child_names": ["Arabian camel", "dromedary", "<PERSON><PERSON> d<PERSON>ius"], "candidate_sentences": ["a Arabian camel, which is a camel, which is a ungulate", "a dromedary, which is a camel, which is a ungulate", "a Camelus dromedarius, which is a camel, which is a ungulate", "a Arabian camel, which is a camel, which is a hoofed mammal", "a dromedary, which is a camel, which is a hoofed mammal", "a Camelus dromedarius, which is a camel, which is a hoofed mammal"]}, "132": {"node_name": "sandpiper", "parent_names": ["aquatic bird"], "child_names": ["red-backed sandpiper", "dunlin", "<PERSON><PERSON><PERSON> alpina", "<PERSON>shank", "<PERSON>nga totanus"], "candidate_sentences": ["a red-backed sandpiper, which is a sandpiper, which is a aquatic bird", "a dunlin, which is a sandpiper, which is a aquatic bird", "a Erolia alpina, which is a sandpiper, which is a aquatic bird", "a redshank, which is a sandpiper, which is a aquatic bird", "a Tringa totanus, which is a sandpiper, which is a aquatic bird"]}, "133": {"node_name": "phalanger, opossum, possum", "parent_names": ["marsupial", "pouched mammal"], "child_names": ["koala", "koala bear", "kangaroo bear", "native bear", "Phascolarct<PERSON> cinereus"], "candidate_sentences": ["a koala, which is a phalanger, opossum, possum, which is a marsupial", "a koala bear, which is a phalanger, opossum, possum, which is a marsupial", "a kangaroo bear, which is a phalanger, opossum, possum, which is a marsupial", "a native bear, which is a phalanger, opossum, possum, which is a marsupial", "a Phascolarctos cinereus, which is a phalanger, opossum, possum, which is a marsupial", "a koala, which is a phalanger, opossum, possum, which is a pouched mammal", "a koala bear, which is a phalanger, opossum, possum, which is a pouched mammal", "a kangaroo bear, which is a phalanger, opossum, possum, which is a pouched mammal", "a native bear, which is a phalanger, opossum, possum, which is a pouched mammal", "a Phascolarctos cinereus, which is a phalanger, opossum, possum, which is a pouched mammal"]}, "134": {"node_name": "dress, frock", "parent_names": ["garment"], "child_names": ["gown"], "candidate_sentences": ["a gown, which is a dress, frock, which is a garment"]}, "135": {"node_name": "abacus", "parent_names": ["scientific instrument"], "child_names": [], "candidate_sentences": ["a abacus, which is a scientific instrument"]}, "136": {"node_name": "bib", "parent_names": ["neckwear"], "child_names": [], "candidate_sentences": ["a bib, which is a neckwear"]}, "137": {"node_name": "carpenter's kit, tool kit", "parent_names": ["tool"], "child_names": [], "candidate_sentences": ["a carpenter's kit, tool kit, which is a tool"]}, "138": {"node_name": "swimsuit, swimwear, bathing suit, swimming costume, bathing costume", "parent_names": ["garment"], "child_names": ["bikini", "two-piece", "maillot", "tank suit", "swimming trunks", "bathing trunks"], "candidate_sentences": ["a bikini, which is a swimsuit, swimwear, bathing suit, swimming costume, bathing costume, which is a garment", "a two-piece, which is a swimsuit, swimwear, bathing suit, swimming costume, bathing costume, which is a garment", "a maillot, which is a swimsuit, swimwear, bathing suit, swimming costume, bathing costume, which is a garment", "a tank suit, which is a swimsuit, swimwear, bathing suit, swimming costume, bathing costume, which is a garment", "a swimming trunks, which is a swimsuit, swimwear, bathing suit, swimming costume, bathing costume, which is a garment", "a bathing trunks, which is a swimsuit, swimwear, bathing suit, swimming costume, bathing costume, which is a garment"]}, "139": {"node_name": "spoonbill", "parent_names": ["aquatic bird"], "child_names": [], "candidate_sentences": ["a spoonbill, which is a aquatic bird"]}, "140": {"node_name": "cockroach, roach", "parent_names": ["insect"], "child_names": [], "candidate_sentences": ["a cockroach, roach, which is a insect"]}, "141": {"node_name": "water ouzel, dipper", "parent_names": ["passerine", "passeriform bird"], "child_names": [], "candidate_sentences": ["a water ouzel, dipper, which is a passerine", "a water ouzel, dipper, which is a passeriform bird"]}, "142": {"node_name": "printer", "parent_names": ["electronic equipment"], "child_names": [], "candidate_sentences": ["a printer, which is a electronic equipment"]}, "143": {"node_name": "valley, vale", "parent_names": ["dummy71"], "child_names": [], "candidate_sentences": ["a valley, vale, which is a dummy71"]}, "144": {"node_name": "bison", "parent_names": ["ungulate", "hoofed mammal"], "child_names": [], "candidate_sentences": ["a bison, which is a ungulate", "a bison, which is a hoofed mammal"]}, "145": {"node_name": "wolf", "parent_names": ["carnivore"], "child_names": ["timber wolf", "grey wolf", "gray wolf", "Canis lupus", "white wolf", "Arctic wolf", "Canis lupus tundrarum", "red wolf", "maned wolf", "Canis rufus", "<PERSON>is niger", "coyote", "prairie wolf", "brush wolf", "<PERSON><PERSON> latrans"], "candidate_sentences": ["a timber wolf, which is a wolf, which is a carnivore", "a grey wolf, which is a wolf, which is a carnivore", "a gray wolf, which is a wolf, which is a carnivore", "a Canis lupus, which is a wolf, which is a carnivore", "a white wolf, which is a wolf, which is a carnivore", "a Arctic wolf, which is a wolf, which is a carnivore", "a Canis lupus tundrarum, which is a wolf, which is a carnivore", "a red wolf, which is a wolf, which is a carnivore", "a maned wolf, which is a wolf, which is a carnivore", "a Canis rufus, which is a wolf, which is a carnivore", "a Canis niger, which is a wolf, which is a carnivore", "a coyote, which is a wolf, which is a carnivore", "a prairie wolf, which is a wolf, which is a carnivore", "a brush wolf, which is a wolf, which is a carnivore", "a Canis latrans, which is a wolf, which is a carnivore"]}, "146": {"node_name": "refrigerator, icebox", "parent_names": ["home appliance", "household appliance"], "child_names": [], "candidate_sentences": ["a refrigerator, icebox, which is a home appliance", "a refrigerator, icebox, which is a household appliance"]}, "147": {"node_name": "spider", "parent_names": ["arachnid", "arachnoid"], "child_names": ["black and gold garden spider", "<PERSON><PERSON><PERSON><PERSON> aurantia", "barn spider", "Araneus cavaticus", "garden spider", "<PERSON><PERSON> diademata", "black widow", "Latrode<PERSON> mactans", "tarantula", "wolf spider", "hunting spider"], "candidate_sentences": ["a black and gold garden spider, which is a spider, which is a arachnid", "a Argiope aurantia, which is a spider, which is a arachnid", "a barn spider, which is a spider, which is a arachnid", "a Araneus cavaticus, which is a spider, which is a arachnid", "a garden spider, which is a spider, which is a arachnid", "a Aranea diademata, which is a spider, which is a arachnid", "a black widow, which is a spider, which is a arachnid", "a Latrodectus mactans, which is a spider, which is a arachnid", "a tarantula, which is a spider, which is a arachnid", "a wolf spider, which is a spider, which is a arachnid", "a hunting spider, which is a spider, which is a arachnid", "a black and gold garden spider, which is a spider, which is a arachnoid", "a Argiope aurantia, which is a spider, which is a arachnoid", "a barn spider, which is a spider, which is a arachnoid", "a Araneus cavaticus, which is a spider, which is a arachnoid", "a garden spider, which is a spider, which is a arachnoid", "a Aranea diademata, which is a spider, which is a arachnoid", "a black widow, which is a spider, which is a arachnoid", "a Latrodectus mactans, which is a spider, which is a arachnoid", "a tarantula, which is a spider, which is a arachnoid", "a wolf spider, which is a spider, which is a arachnoid", "a hunting spider, which is a spider, which is a arachnoid"]}, "148": {"node_name": "binoculars, field glasses, opera glasses", "parent_names": ["scientific instrument"], "child_names": [], "candidate_sentences": ["a binoculars, field glasses, opera glasses, which is a scientific instrument"]}, "149": {"node_name": "shovel", "parent_names": ["tool"], "child_names": [], "candidate_sentences": ["a shovel, which is a tool"]}, "150": {"node_name": "keyboard instrument", "parent_names": ["musical instrument", "instrument"], "child_names": ["accordion", "piano accordion", "squeeze box", "organ", "pipe organ", "piano", "pianoforte", "forte-piano"], "candidate_sentences": ["a accordion, which is a keyboard instrument, which is a musical instrument", "a piano accordion, which is a keyboard instrument, which is a musical instrument", "a squeeze box, which is a keyboard instrument, which is a musical instrument", "a organ, which is a keyboard instrument, which is a musical instrument", "a pipe organ, which is a keyboard instrument, which is a musical instrument", "a piano, which is a keyboard instrument, which is a musical instrument", "a pianoforte, which is a keyboard instrument, which is a musical instrument", "a forte-piano, which is a keyboard instrument, which is a musical instrument", "a accordion, which is a keyboard instrument, which is a instrument", "a piano accordion, which is a keyboard instrument, which is a instrument", "a squeeze box, which is a keyboard instrument, which is a instrument", "a organ, which is a keyboard instrument, which is a instrument", "a pipe organ, which is a keyboard instrument, which is a instrument", "a piano, which is a keyboard instrument, which is a instrument", "a pianoforte, which is a keyboard instrument, which is a instrument", "a forte-piano, which is a keyboard instrument, which is a instrument"]}, "151": {"node_name": "pin", "parent_names": ["tool"], "child_names": ["safety pin"], "candidate_sentences": ["a safety pin, which is a pin, which is a tool"]}, "152": {"node_name": "meter", "parent_names": ["measuring instrument", "measuring system", "measuring device"], "child_names": ["odometer", "hodometer", "mileometer", "milometer"], "candidate_sentences": ["a odometer, which is a meter, which is a measuring instrument", "a hodometer, which is a meter, which is a measuring instrument", "a mileometer, which is a meter, which is a measuring instrument", "a milometer, which is a meter, which is a measuring instrument", "a odometer, which is a meter, which is a measuring system", "a hodometer, which is a meter, which is a measuring system", "a mileometer, which is a meter, which is a measuring system", "a milometer, which is a meter, which is a measuring system", "a odometer, which is a meter, which is a measuring device", "a hodometer, which is a meter, which is a measuring device", "a mileometer, which is a meter, which is a measuring device", "a milometer, which is a meter, which is a measuring device"]}, "153": {"node_name": "mouse, computer mouse", "parent_names": ["electronic equipment"], "child_names": [], "candidate_sentences": ["a mouse, computer mouse, which is a electronic equipment"]}, "154": {"node_name": "swan", "parent_names": ["aquatic bird"], "child_names": ["black swan", "<PERSON><PERSON><PERSON> atratus"], "candidate_sentences": ["a black swan, which is a swan, which is a aquatic bird", "a Cygnus atratus, which is a swan, which is a aquatic bird"]}, "155": {"node_name": "remote control, remote", "parent_names": ["electronic equipment"], "child_names": [], "candidate_sentences": ["a remote control, remote, which is a electronic equipment"]}, "156": {"node_name": "stocking", "parent_names": ["footwear", "legwear"], "child_names": ["Christmas stocking"], "candidate_sentences": ["a Christmas stocking, which is a stocking, which is a footwear", "a Christmas stocking, which is a stocking, which is a legwear"]}, "157": {"node_name": "pot", "parent_names": ["kitchen utensil"], "child_names": ["caldron", "cauldron", "coffeepot", "Dutch oven", "teapot"], "candidate_sentences": ["a caldron, which is a pot, which is a kitchen utensil", "a cauldron, which is a pot, which is a kitchen utensil", "a coffeepot, which is a pot, which is a kitchen utensil", "a Dutch oven, which is a pot, which is a kitchen utensil", "a teapot, which is a pot, which is a kitchen utensil"]}, "158": {"node_name": "drilling platform, offshore rig", "parent_names": ["rig"], "child_names": [], "candidate_sentences": ["a drilling platform, offshore rig, which is a rig"]}, "159": {"node_name": "mosquito net", "parent_names": ["screen"], "child_names": [], "candidate_sentences": ["a mosquito net, which is a screen"]}, "160": {"node_name": "leafhopper", "parent_names": ["insect"], "child_names": [], "candidate_sentences": ["a leafhopper, which is a insect"]}, "161": {"node_name": "radio, wireless", "parent_names": ["electronic equipment"], "child_names": [], "candidate_sentences": ["a radio, wireless, which is a electronic equipment"]}, "162": {"node_name": "cabinet", "parent_names": ["wall unit"], "child_names": ["china cabinet", "china closet", "file", "file cabinet", "filing cabinet", "medicine chest", "medicine cabinet"], "candidate_sentences": ["a china cabinet, which is a cabinet, which is a wall unit", "a china closet, which is a cabinet, which is a wall unit", "a file, which is a cabinet, which is a wall unit", "a file cabinet, which is a cabinet, which is a wall unit", "a filing cabinet, which is a cabinet, which is a wall unit", "a medicine chest, which is a cabinet, which is a wall unit", "a medicine cabinet, which is a cabinet, which is a wall unit"]}, "163": {"node_name": "jackfruit, jak, jack", "parent_names": ["fruit"], "child_names": [], "candidate_sentences": ["a jackfruit, jak, jack, which is a fruit"]}, "164": {"node_name": "car, railcar, railway car, railroad car", "parent_names": ["train", "railroad train"], "child_names": ["freight car", "passenger car", "coach", "carriage"], "candidate_sentences": ["a freight car, which is a car, railcar, railway car, railroad car, which is a train", "a passenger car, which is a car, railcar, railway car, railroad car, which is a train", "a coach, which is a car, railcar, railway car, railroad car, which is a train", "a carriage, which is a car, railcar, railway car, railroad car, which is a train", "a freight car, which is a car, railcar, railway car, railroad car, which is a railroad train", "a passenger car, which is a car, railcar, railway car, railroad car, which is a railroad train", "a coach, which is a car, railcar, railway car, railroad car, which is a railroad train", "a carriage, which is a car, railcar, railway car, railroad car, which is a railroad train"]}, "165": {"node_name": "cardoon", "parent_names": ["vegetable", "veggie", "veg"], "child_names": [], "candidate_sentences": ["a cardoon, which is a vegetable", "a cardoon, which is a veggie", "a cardoon, which is a veg"]}, "166": {"node_name": "photocopier", "parent_names": ["electronic equipment"], "child_names": [], "candidate_sentences": ["a photocopier, which is a electronic equipment"]}, "167": {"node_name": "nematode, nematode worm, roundworm", "parent_names": ["dummy15"], "child_names": [], "candidate_sentences": ["a nematode, nematode worm, roundworm, which is a dummy15"]}, "168": {"node_name": "bottle", "parent_names": ["tableware"], "child_names": ["beer bottle", "pop bottle", "soda bottle", "water bottle", "wine bottle"], "candidate_sentences": ["a beer bottle, which is a bottle, which is a tableware", "a pop bottle, which is a bottle, which is a tableware", "a soda bottle, which is a bottle, which is a tableware", "a water bottle, which is a bottle, which is a tableware", "a wine bottle, which is a bottle, which is a tableware"]}, "169": {"node_name": "syringe", "parent_names": ["medical instrument"], "child_names": [], "candidate_sentences": ["a syringe, which is a medical instrument"]}, "170": {"node_name": "factory, mill, manufacturing plant, manufactory", "parent_names": ["building", "edifice"], "child_names": ["lumbermill", "sawmill"], "candidate_sentences": ["a lumbermill, which is a factory, mill, manufacturing plant, manufactory, which is a building", "a sawmill, which is a factory, mill, manufacturing plant, manufactory, which is a building", "a lumbermill, which is a factory, mill, manufacturing plant, manufactory, which is a edifice", "a sawmill, which is a factory, mill, manufacturing plant, manufactory, which is a edifice"]}, "171": {"node_name": "ape", "parent_names": ["primate"], "child_names": ["orangutan", "orang", "orangutang", "<PERSON><PERSON> pygmaeus", "gorilla", "Gorilla gorilla", "chimpanzee", "chimp", "Pan troglodytes", "gibbon", "Hylobates lar", "siamang", "Hylob<PERSON> syndactylus", "Symphalangus syndactylus"], "candidate_sentences": ["a orangutan, which is a ape, which is a primate", "a orang, which is a ape, which is a primate", "a orangutang, which is a ape, which is a primate", "a Pongo pygmaeus, which is a ape, which is a primate", "a gorilla, which is a ape, which is a primate", "a Gorilla gorilla, which is a ape, which is a primate", "a chimpanzee, which is a ape, which is a primate", "a chimp, which is a ape, which is a primate", "a Pan troglodytes, which is a ape, which is a primate", "a gibbon, which is a ape, which is a primate", "a Hylobates lar, which is a ape, which is a primate", "a siamang, which is a ape, which is a primate", "a Hylobates syndactylus, which is a ape, which is a primate", "a Symphalangus syndactylus, which is a ape, which is a primate"]}, "172": {"node_name": "helmet", "parent_names": ["headdress", "headgear"], "child_names": ["crash helmet", "football helmet", "pickelhaube"], "candidate_sentences": ["a crash helmet, which is a helmet, which is a headdress", "a football helmet, which is a helmet, which is a headdress", "a pickelhaube, which is a helmet, which is a headdress", "a crash helmet, which is a helmet, which is a headgear", "a football helmet, which is a helmet, which is a headgear", "a pickelhaube, which is a helmet, which is a headgear"]}, "173": {"node_name": "space heater", "parent_names": ["home appliance", "household appliance"], "child_names": [], "candidate_sentences": ["a space heater, which is a home appliance", "a space heater, which is a household appliance"]}, "174": {"node_name": "plunger, plumber's helper", "parent_names": ["tool"], "child_names": [], "candidate_sentences": ["a plunger, plumber's helper, which is a tool"]}, "175": {"node_name": "sharpener", "parent_names": ["tool"], "child_names": ["pencil sharpener"], "candidate_sentences": ["a pencil sharpener, which is a sharpener, which is a tool"]}, "176": {"node_name": "rail", "parent_names": ["aquatic bird"], "child_names": ["coot"], "candidate_sentences": ["a coot, which is a rail, which is a aquatic bird"]}, "177": {"node_name": "signboard, sign", "parent_names": ["dummy72"], "child_names": ["scoreboard", "street sign", "traffic light", "traffic signal", "stoplight"], "candidate_sentences": ["a scoreboard, which is a signboard, sign, which is a dummy72", "a street sign, which is a signboard, sign, which is a dummy72", "a traffic light, which is a signboard, sign, which is a dummy72", "a traffic signal, which is a signboard, sign, which is a dummy72", "a stoplight, which is a signboard, sign, which is a dummy72"]}, "178": {"node_name": "fence, fencing", "parent_names": ["barrier"], "child_names": ["chainlink fence", "picket fence", "paling", "stone wall", "rail fence"], "candidate_sentences": ["a chainlink fence, which is a fence, fencing, which is a barrier", "a picket fence, which is a fence, fencing, which is a barrier", "a paling, which is a fence, fencing, which is a barrier", "a stone wall, which is a fence, fencing, which is a barrier", "a rail fence, which is a fence, fencing, which is a barrier"]}, "179": {"node_name": "custard apple", "parent_names": ["fruit"], "child_names": [], "candidate_sentences": ["a custard apple, which is a fruit"]}, "180": {"node_name": "groom, bridegroom", "parent_names": ["person", "individual", "someone", "somebody", "mortal", "soul"], "child_names": [], "candidate_sentences": ["a groom, bridegroom, which is a person", "a groom, bridegroom, which is a individual", "a groom, bridegroom, which is a someone", "a groom, bridegroom, which is a somebody", "a groom, bridegroom, which is a mortal", "a groom, bridegroom, which is a soul"]}, "181": {"node_name": "pelican", "parent_names": ["aquatic bird"], "child_names": [], "candidate_sentences": ["a pelican, which is a aquatic bird"]}, "182": {"node_name": "mat", "parent_names": ["floor cover", "floor covering"], "child_names": ["doormat", "welcome mat"], "candidate_sentences": ["a doormat, which is a mat, which is a floor cover", "a welcome mat, which is a mat, which is a floor cover", "a doormat, which is a mat, which is a floor covering", "a welcome mat, which is a mat, which is a floor covering"]}, "183": {"node_name": "bar<PERSON><PERSON><PERSON>, snoek", "parent_names": ["bony fish"], "child_names": [], "candidate_sentences": ["a barracouta, snoek, which is a bony fish"]}, "184": {"node_name": "cauliflower", "parent_names": ["vegetable", "veggie", "veg"], "child_names": [], "candidate_sentences": ["a cauliflower, which is a vegetable", "a cauliflower, which is a veggie", "a cauliflower, which is a veg"]}, "185": {"node_name": "space shuttle", "parent_names": ["spacecraft", "ballistic capsule", "space vehicle"], "child_names": [], "candidate_sentences": ["a space shuttle, which is a spacecraft", "a space shuttle, which is a ballistic capsule", "a space shuttle, which is a space vehicle"]}, "186": {"node_name": "cheetah, chetah, Acinony<PERSON> jubatus", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a cheetah, chetah, Acinonyx jubatus, which is a carnivore"]}, "187": {"node_name": "nail", "parent_names": ["tool"], "child_names": [], "candidate_sentences": ["a nail, which is a tool"]}, "188": {"node_name": "cricket", "parent_names": ["insect"], "child_names": [], "candidate_sentences": ["a cricket, which is a insect"]}, "189": {"node_name": "measuring stick, measure, measuring rod", "parent_names": ["measuring instrument", "measuring system", "measuring device"], "child_names": ["rule", "ruler"], "candidate_sentences": ["a rule, which is a measuring stick, measure, measuring rod, which is a measuring instrument", "a ruler, which is a measuring stick, measure, measuring rod, which is a measuring instrument", "a rule, which is a measuring stick, measure, measuring rod, which is a measuring system", "a ruler, which is a measuring stick, measure, measuring rod, which is a measuring system", "a rule, which is a measuring stick, measure, measuring rod, which is a measuring device", "a ruler, which is a measuring stick, measure, measuring rod, which is a measuring device"]}, "190": {"node_name": "snake, serpent, ophidian", "parent_names": ["serpentes"], "child_names": ["thunder snake", "worm snake", "Carphop<PERSON> amoenus", "ringneck snake", "ring-necked snake", "ring snake", "hognose snake", "puff adder", "sand viper", "green snake", "grass snake", "king snake", "kingsnake", "garter snake", "grass snake", "water snake", "vine snake", "night snake", "<PERSON><PERSON>ps<PERSON><PERSON> torquata", "boa constrictor", "Constrictor constrictor", "sea snake", "horned viper", "cerastes", "sand viper", "horned asp", "<PERSON><PERSON><PERSON> cornutus", "cobra", "python", "rattlesnake", "rattler", "mamba"], "candidate_sentences": ["a thunder snake, which is a snake, serpent, ophidian, which is a serpentes", "a worm snake, which is a snake, serpent, ophidian, which is a serpentes", "a Carphophis amoenus, which is a snake, serpent, ophidian, which is a serpentes", "a ringneck snake, which is a snake, serpent, ophidian, which is a serpentes", "a ring-necked snake, which is a snake, serpent, ophidian, which is a serpentes", "a ring snake, which is a snake, serpent, ophidian, which is a serpentes", "a hognose snake, which is a snake, serpent, ophidian, which is a serpentes", "a puff adder, which is a snake, serpent, ophidian, which is a serpentes", "a sand viper, which is a snake, serpent, ophidian, which is a serpentes", "a green snake, which is a snake, serpent, ophidian, which is a serpentes", "a grass snake, which is a snake, serpent, ophidian, which is a serpentes", "a king snake, which is a snake, serpent, ophidian, which is a serpentes", "a kingsnake, which is a snake, serpent, ophidian, which is a serpentes", "a garter snake, which is a snake, serpent, ophidian, which is a serpentes", "a grass snake, which is a snake, serpent, ophidian, which is a serpentes", "a water snake, which is a snake, serpent, ophidian, which is a serpentes", "a vine snake, which is a snake, serpent, ophidian, which is a serpentes", "a night snake, which is a snake, serpent, ophidian, which is a serpentes", "a Hypsiglena torquata, which is a snake, serpent, ophidian, which is a serpentes", "a boa constrictor, which is a snake, serpent, ophidian, which is a serpentes", "a Constrictor constrictor, which is a snake, serpent, ophidian, which is a serpentes", "a sea snake, which is a snake, serpent, ophidian, which is a serpentes", "a horned viper, which is a snake, serpent, ophidian, which is a serpentes", "a cerastes, which is a snake, serpent, ophidian, which is a serpentes", "a sand viper, which is a snake, serpent, ophidian, which is a serpentes", "a horned asp, which is a snake, serpent, ophidian, which is a serpentes", "a Cerastes cornutus, which is a snake, serpent, ophidian, which is a serpentes", "a cobra, which is a snake, serpent, ophidian, which is a serpentes", "a python, which is a snake, serpent, ophidian, which is a serpentes", "a rattlesnake, which is a snake, serpent, ophidian, which is a serpentes", "a rattler, which is a snake, serpent, ophidian, which is a serpentes", "a mamba, which is a snake, serpent, ophidian, which is a serpentes"]}, "191": {"node_name": "display, video display", "parent_names": ["electronic equipment"], "child_names": ["screen", "CRT screen"], "candidate_sentences": ["a screen, which is a display, video display, which is a electronic equipment", "a CRT screen, which is a display, video display, which is a electronic equipment"]}, "192": {"node_name": "weight, free weight, exercising weight", "parent_names": ["sports equipment"], "child_names": ["barbell", "dumbbell"], "candidate_sentences": ["a barbell, which is a weight, free weight, exercising weight, which is a sports equipment", "a dumbbell, which is a weight, free weight, exercising weight, which is a sports equipment"]}, "193": {"node_name": "nightwear, sleepwear, nightclothes", "parent_names": ["garment"], "child_names": ["pajama", "pyjama", "pj's", "jammies"], "candidate_sentences": ["a pajama, which is a nightwear, sleepwear, nightclothes, which is a garment", "a pyjama, which is a nightwear, sleepwear, nightclothes, which is a garment", "a pj's, which is a nightwear, sleepwear, nightclothes, which is a garment", "a jammies, which is a nightwear, sleepwear, nightclothes, which is a garment"]}, "194": {"node_name": "cliff, drop, drop-off", "parent_names": ["dummy68"], "child_names": [], "candidate_sentences": ["a cliff, drop, drop-off, which is a dummy68"]}, "195": {"node_name": "lock", "parent_names": ["tool"], "child_names": ["combination lock", "padlock"], "candidate_sentences": ["a combination lock, which is a lock, which is a tool", "a padlock, which is a lock, which is a tool"]}, "196": {"node_name": "cassette player", "parent_names": ["electronic equipment"], "child_names": [], "candidate_sentences": ["a cassette player, which is a electronic equipment"]}, "197": {"node_name": "heron", "parent_names": ["aquatic bird"], "child_names": ["little blue heron", "Egretta caerulea", "bittern", "egret"], "candidate_sentences": ["a little blue heron, which is a heron, which is a aquatic bird", "a Egretta caerulea, which is a heron, which is a aquatic bird", "a bittern, which is a heron, which is a aquatic bird", "a egret, which is a heron, which is a aquatic bird"]}, "198": {"node_name": "bed", "parent_names": ["bedroom furniture"], "child_names": ["four-poster"], "candidate_sentences": ["a four-poster, which is a bed, which is a bedroom furniture"]}, "199": {"node_name": "otter", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a otter, which is a carnivore"]}, "200": {"node_name": "chair", "parent_names": ["seat"], "child_names": ["barber chair", "folding chair", "rocking chair", "rocker", "chair of state"], "candidate_sentences": ["a barber chair, which is a chair, which is a seat", "a folding chair, which is a chair, which is a seat", "a rocking chair, which is a chair, which is a seat", "a rocker, which is a chair, which is a seat", "a chair of state, which is a chair, which is a seat"]}, "201": {"node_name": "j<PERSON><PERSON>", "parent_names": ["piciform bird"], "child_names": [], "candidate_sentences": ["a jacamar, which is a piciform bird"]}, "202": {"node_name": "tape player", "parent_names": ["electronic equipment"], "child_names": [], "candidate_sentences": ["a tape player, which is a electronic equipment"]}, "203": {"node_name": "rabbit, coney, cony", "parent_names": ["lagomorph", "gnawing mammal"], "child_names": ["wood rabbit", "cottontail", "cottontail rabbit", "<PERSON><PERSON>", "Angora rabbit"], "candidate_sentences": ["a wood rabbit, which is a rabbit, coney, cony, which is a lagomorph", "a cottontail, which is a rabbit, coney, cony, which is a lagomorph", "a cottontail rabbit, which is a rabbit, coney, cony, which is a lagomorph", "a Angora, which is a rabbit, coney, cony, which is a lagomorph", "a Angora rabbit, which is a rabbit, coney, cony, which is a lagomorph", "a wood rabbit, which is a rabbit, coney, cony, which is a gnawing mammal", "a cottontail, which is a rabbit, coney, cony, which is a gnawing mammal", "a cottontail rabbit, which is a rabbit, coney, cony, which is a gnawing mammal", "a Angora, which is a rabbit, coney, cony, which is a gnawing mammal", "a Angora rabbit, which is a rabbit, coney, cony, which is a gnawing mammal"]}, "204": {"node_name": "desk", "parent_names": ["table"], "child_names": [], "candidate_sentences": ["a desk, which is a table"]}, "205": {"node_name": "unicycle, monocycle", "parent_names": ["cycles"], "child_names": [], "candidate_sentences": ["a unicycle, monocycle, which is a cycles"]}, "206": {"node_name": "hippopotamus, hippo, river horse, Hippopotamus amphibius", "parent_names": ["ungulate", "hoofed mammal"], "child_names": [], "candidate_sentences": ["a hippopotamus, hippo, river horse, Hippopotamus amphibius, which is a ungulate", "a hippopotamus, hippo, river horse, Hippopotamus amphibius, which is a hoofed mammal"]}, "207": {"node_name": "hyena, hyaena", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a hyena, hyaena, which is a carnivore"]}, "208": {"node_name": "eagle, bird of Jove", "parent_names": ["bird of prey", "raptor", "raptorial bird"], "child_names": ["bald eagle", "American eagle", "Hal<PERSON><PERSON> leucocephalus"], "candidate_sentences": ["a bald eagle, which is a eagle, bird of Jove, which is a bird of prey", "a American eagle, which is a eagle, bird of Jove, which is a bird of prey", "a Haliaeetus leucocephalus, which is a eagle, bird of Jove, which is a bird of prey", "a bald eagle, which is a eagle, bird of Jove, which is a raptor", "a American eagle, which is a eagle, bird of Jove, which is a raptor", "a Haliaeetus leucocephalus, which is a eagle, bird of Jove, which is a raptor", "a bald eagle, which is a eagle, bird of Jove, which is a raptorial bird", "a American eagle, which is a eagle, bird of Jove, which is a raptorial bird", "a Haliaeetus leucocephalus, which is a eagle, bird of Jove, which is a raptorial bird"]}, "209": {"node_name": "sauce", "parent_names": ["condiment"], "child_names": ["chocolate sauce", "chocolate syrup", "spaghetti sauce", "pasta sauce"], "candidate_sentences": ["a chocolate sauce, which is a sauce, which is a condiment", "a chocolate syrup, which is a sauce, which is a condiment", "a spaghetti sauce, which is a sauce, which is a condiment", "a pasta sauce, which is a sauce, which is a condiment"]}, "210": {"node_name": "snorkel", "parent_names": ["sports equipment"], "child_names": [], "candidate_sentences": ["a snorkel, which is a sports equipment"]}, "211": {"node_name": "lighter, light, igniter, ignitor", "parent_names": ["tool"], "child_names": [], "candidate_sentences": ["a lighter, light, igniter, ignitor, which is a tool"]}, "212": {"node_name": "bannister, banister, balustrade, balusters, handrail", "parent_names": ["barrier"], "child_names": [], "candidate_sentences": ["a bannister, banister, balustrade, balusters, handrail, which is a barrier"]}, "213": {"node_name": "mushroom", "parent_names": ["basidiomycete", "basidiomycetous fungi"], "child_names": ["agaric", "hen-of-the-woods", "hen of the woods", "Polyporus frondosus", "G<PERSON><PERSON><PERSON> frondosa", "bolete"], "candidate_sentences": ["a agaric, which is a mushroom, which is a basidiomycete", "a hen-of-the-woods, which is a mushroom, which is a basidiomycete", "a hen of the woods, which is a mushroom, which is a basidiomycete", "a Polyporus frondosus, which is a mushroom, which is a basidiomycete", "a Grifola frondosa, which is a mushroom, which is a basidiomycete", "a bolete, which is a mushroom, which is a basidiomycete", "a agaric, which is a mushroom, which is a basidiomycetous fungi", "a hen-of-the-woods, which is a mushroom, which is a basidiomycetous fungi", "a hen of the woods, which is a mushroom, which is a basidiomycetous fungi", "a Polyporus frondosus, which is a mushroom, which is a basidiomycetous fungi", "a Grifola frondosa, which is a mushroom, which is a basidiomycetous fungi", "a bolete, which is a mushroom, which is a basidiomycetous fungi"]}, "214": {"node_name": "face mask", "parent_names": ["facial accessories"], "child_names": ["gasmask", "respirator", "gas helmet", "mask", "ski mask"], "candidate_sentences": ["a gasmask, which is a face mask, which is a facial accessories", "a respirator, which is a face mask, which is a facial accessories", "a gas helmet, which is a face mask, which is a facial accessories", "a mask, which is a face mask, which is a facial accessories", "a ski mask, which is a face mask, which is a facial accessories"]}, "215": {"node_name": "kangaroo", "parent_names": ["marsupial", "pouched mammal"], "child_names": ["wallaby", "brush kangaroo"], "candidate_sentences": ["a wallaby, which is a kangaroo, which is a marsupial", "a brush kangaroo, which is a kangaroo, which is a marsupial", "a wallaby, which is a kangaroo, which is a pouched mammal", "a brush kangaroo, which is a kangaroo, which is a pouched mammal"]}, "216": {"node_name": "crocodile", "parent_names": ["crocodilian reptile", "crocodilian"], "child_names": ["African crocodile", "Nile crocodile", "Crocody<PERSON> niloticus"], "candidate_sentences": ["a African crocodile, which is a crocodile, which is a crocodilian reptile", "a Nile crocodile, which is a crocodile, which is a crocodilian reptile", "a Crocodylus niloticus, which is a crocodile, which is a crocodilian reptile", "a African crocodile, which is a crocodile, which is a crocodilian", "a Nile crocodile, which is a crocodile, which is a crocodilian", "a Crocodylus niloticus, which is a crocodile, which is a crocodilian"]}, "217": {"node_name": "pizza, pizza pie", "parent_names": ["dish"], "child_names": [], "candidate_sentences": ["a pizza, pizza pie, which is a dish"]}, "218": {"node_name": "monkey", "parent_names": ["primate"], "child_names": ["guenon", "guenon monkey", "patas", "hussar monkey", "Erythrocebus patas", "baboon", "macaque", "langur", "colobus", "colobus monkey", "proboscis monkey", "Na<PERSON>is larvatus", "marmoset", "capuchin", "ringtail", "Cebus capucinus", "howler monkey", "howler", "titi", "titi monkey", "spider monkey", "<PERSON><PERSON><PERSON>i", "squirrel monkey", "<PERSON><PERSON><PERSON> sciureus"], "candidate_sentences": ["a guenon, which is a monkey, which is a primate", "a guenon monkey, which is a monkey, which is a primate", "a patas, which is a monkey, which is a primate", "a hussar monkey, which is a monkey, which is a primate", "a Erythrocebus patas, which is a monkey, which is a primate", "a baboon, which is a monkey, which is a primate", "a macaque, which is a monkey, which is a primate", "a langur, which is a monkey, which is a primate", "a colobus, which is a monkey, which is a primate", "a colobus monkey, which is a monkey, which is a primate", "a proboscis monkey, which is a monkey, which is a primate", "a Nasalis larvatus, which is a monkey, which is a primate", "a marmoset, which is a monkey, which is a primate", "a capuchin, which is a monkey, which is a primate", "a ringtail, which is a monkey, which is a primate", "a Cebus capucinus, which is a monkey, which is a primate", "a howler monkey, which is a monkey, which is a primate", "a howler, which is a monkey, which is a primate", "a titi, which is a monkey, which is a primate", "a titi monkey, which is a monkey, which is a primate", "a spider monkey, which is a monkey, which is a primate", "a Ateles geo<PERSON>i, which is a monkey, which is a primate", "a squirrel monkey, which is a monkey, which is a primate", "a Saimiri sciureus, which is a monkey, which is a primate"]}, "219": {"node_name": "eraser", "parent_names": ["tool"], "child_names": ["rubber eraser", "rubber", "pencil eraser"], "candidate_sentences": ["a rubber eraser, which is a eraser, which is a tool", "a rubber, which is a eraser, which is a tool", "a pencil eraser, which is a eraser, which is a tool"]}, "220": {"node_name": "cap", "parent_names": ["headdress", "headgear"], "child_names": ["bathing cap", "swimming cap", "mortarboard", "shower cap"], "candidate_sentences": ["a bathing cap, which is a cap, which is a headdress", "a swimming cap, which is a cap, which is a headdress", "a mortarboard, which is a cap, which is a headdress", "a shower cap, which is a cap, which is a headdress", "a bathing cap, which is a cap, which is a headgear", "a swimming cap, which is a cap, which is a headgear", "a mortarboard, which is a cap, which is a headgear", "a shower cap, which is a cap, which is a headgear"]}, "221": {"node_name": "duck", "parent_names": ["aquatic bird"], "child_names": ["drake", "merganser", "fish duck", "sawbill", "<PERSON><PERSON><PERSON><PERSON>"], "candidate_sentences": ["a drake, which is a duck, which is a aquatic bird", "a merganser, which is a duck, which is a aquatic bird", "a fish duck, which is a duck, which is a aquatic bird", "a sawbill, which is a duck, which is a aquatic bird", "a sheldrake, which is a duck, which is a aquatic bird"]}, "222": {"node_name": "conch", "parent_names": ["gastropod", "univalve"], "child_names": [], "candidate_sentences": ["a conch, which is a gastropod", "a conch, which is a univalve"]}, "223": {"node_name": "dinosaur", "parent_names": ["archosaur", "archosaurian", "archosaurian reptile"], "child_names": ["ornithischian", "ornithischian dinosaur"], "candidate_sentences": ["a ornithischian, which is a dinosaur, which is a archosaur", "a ornithischian dinosaur, which is a dinosaur, which is a archosaur", "a ornithischian, which is a dinosaur, which is a archosaurian", "a ornithischian dinosaur, which is a dinosaur, which is a archosaurian", "a ornithischian, which is a dinosaur, which is a archosaurian reptile", "a ornithischian dinosaur, which is a dinosaur, which is a archosaurian reptile"]}, "224": {"node_name": "airship, dirigible", "parent_names": ["aircraft"], "child_names": [], "candidate_sentences": ["a airship, dirigible, which is a aircraft"]}, "225": {"node_name": "missile", "parent_names": ["weapon", "arm", "weapon system"], "child_names": [], "candidate_sentences": ["a missile, which is a weapon", "a missile, which is a arm", "a missile, which is a weapon system"]}, "226": {"node_name": "coral", "parent_names": ["anthozoan", "actinozoan"], "child_names": ["brain coral"], "candidate_sentences": ["a brain coral, which is a coral, which is a anthozoan", "a brain coral, which is a coral, which is a actinozoan"]}, "227": {"node_name": "fire screen, fireguard", "parent_names": ["screen"], "child_names": [], "candidate_sentences": ["a fire screen, fireguard, which is a screen"]}, "228": {"node_name": "tripod", "parent_names": ["photographic equipment"], "child_names": [], "candidate_sentences": ["a tripod, which is a photographic equipment"]}, "229": {"node_name": "gymnastic apparatus, exerciser", "parent_names": ["sports equipment"], "child_names": ["balance beam", "beam", "horizontal bar", "high bar", "parallel bars", "bars"], "candidate_sentences": ["a balance beam, which is a gymnastic apparatus, exerciser, which is a sports equipment", "a beam, which is a gymnastic apparatus, exerciser, which is a sports equipment", "a horizontal bar, which is a gymnastic apparatus, exerciser, which is a sports equipment", "a high bar, which is a gymnastic apparatus, exerciser, which is a sports equipment", "a parallel bars, which is a gymnastic apparatus, exerciser, which is a sports equipment", "a bars, which is a gymnastic apparatus, exerciser, which is a sports equipment"]}, "230": {"node_name": "pen", "parent_names": ["tool"], "child_names": ["ballpoint", "ballpoint pen", "ballpen", "Biro", "fountain pen", "quill", "quill pen"], "candidate_sentences": ["a ballpoint, which is a pen, which is a tool", "a ballpoint pen, which is a pen, which is a tool", "a ballpen, which is a pen, which is a tool", "a Biro, which is a pen, which is a tool", "a fountain pen, which is a pen, which is a tool", "a quill, which is a pen, which is a tool", "a quill pen, which is a pen, which is a tool"]}, "231": {"node_name": "body armor, body armour, suit of armor, suit of armour, coat of mail, cataphract", "parent_names": ["armor"], "child_names": ["breastplate", "aegis", "egis", "bulletproof vest", "chain mail", "ring mail", "mail", "chain armor", "chain armour", "ring armor", "ring armour", "cuirass"], "candidate_sentences": ["a breastplate, which is a body armor, body armour, suit of armor, suit of armour, coat of mail, cataphract, which is a armor", "a aegis, which is a body armor, body armour, suit of armor, suit of armour, coat of mail, cataphract, which is a armor", "a egis, which is a body armor, body armour, suit of armor, suit of armour, coat of mail, cataphract, which is a armor", "a bulletproof vest, which is a body armor, body armour, suit of armor, suit of armour, coat of mail, cataphract, which is a armor", "a chain mail, which is a body armor, body armour, suit of armor, suit of armour, coat of mail, cataphract, which is a armor", "a ring mail, which is a body armor, body armour, suit of armor, suit of armour, coat of mail, cataphract, which is a armor", "a mail, which is a body armor, body armour, suit of armor, suit of armour, coat of mail, cataphract, which is a armor", "a chain armor, which is a body armor, body armour, suit of armor, suit of armour, coat of mail, cataphract, which is a armor", "a chain armour, which is a body armor, body armour, suit of armor, suit of armour, coat of mail, cataphract, which is a armor", "a ring armor, which is a body armor, body armour, suit of armor, suit of armour, coat of mail, cataphract, which is a armor", "a ring armour, which is a body armor, body armour, suit of armor, suit of armour, coat of mail, cataphract, which is a armor", "a cuirass, which is a body armor, body armour, suit of armor, suit of armour, coat of mail, cataphract, which is a armor"]}, "232": {"node_name": "compass", "parent_names": ["measuring instrument", "measuring system", "measuring device"], "child_names": ["magnetic compass"], "candidate_sentences": ["a magnetic compass, which is a compass, which is a measuring instrument", "a magnetic compass, which is a compass, which is a measuring system", "a magnetic compass, which is a compass, which is a measuring device"]}, "233": {"node_name": "window shade", "parent_names": ["screen"], "child_names": [], "candidate_sentences": ["a window shade, which is a screen"]}, "234": {"node_name": "telephone, phone, telephone set", "parent_names": ["electronic equipment"], "child_names": ["cellular telephone", "cellular phone", "cellphone", "cell", "mobile phone", "dial telephone", "dial phone", "pay-phone", "pay-station"], "candidate_sentences": ["a cellular telephone, which is a telephone, phone, telephone set, which is a electronic equipment", "a cellular phone, which is a telephone, phone, telephone set, which is a electronic equipment", "a cellphone, which is a telephone, phone, telephone set, which is a electronic equipment", "a cell, which is a telephone, phone, telephone set, which is a electronic equipment", "a mobile phone, which is a telephone, phone, telephone set, which is a electronic equipment", "a dial telephone, which is a telephone, phone, telephone set, which is a electronic equipment", "a dial phone, which is a telephone, phone, telephone set, which is a electronic equipment", "a pay-phone, which is a telephone, phone, telephone set, which is a electronic equipment", "a pay-station, which is a telephone, phone, telephone set, which is a electronic equipment"]}, "235": {"node_name": "makeup, make-up, war paint", "parent_names": ["cosmetic"], "child_names": ["face powder", "lipstick", "lip rouge"], "candidate_sentences": ["a face powder, which is a makeup, make-up, war paint, which is a cosmetic", "a lipstick, which is a makeup, make-up, war paint, which is a cosmetic", "a lip rouge, which is a makeup, make-up, war paint, which is a cosmetic"]}, "236": {"node_name": "coffee maker", "parent_names": ["home appliance", "household appliance"], "child_names": ["espresso maker"], "candidate_sentences": ["a espresso maker, which is a coffee maker, which is a home appliance", "a espresso maker, which is a coffee maker, which is a household appliance"]}, "237": {"node_name": "beaver", "parent_names": ["rodent", "gnawer"], "child_names": [], "candidate_sentences": ["a beaver, which is a rodent", "a beaver, which is a gnawer"]}, "238": {"node_name": "crutch", "parent_names": ["dummy51"], "child_names": [], "candidate_sentences": ["a crutch, which is a dummy51"]}, "239": {"node_name": "window screen", "parent_names": ["screen"], "child_names": [], "candidate_sentences": ["a window screen, which is a screen"]}, "240": {"node_name": "badger", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a badger, which is a carnivore"]}, "241": {"node_name": "sweater, jumper", "parent_names": ["garment"], "child_names": ["cardigan", "pullover", "slipover"], "candidate_sentences": ["a cardigan, which is a sweater, jumper, which is a garment", "a pullover, which is a sweater, jumper, which is a garment", "a slipover, which is a sweater, jumper, which is a garment"]}, "242": {"node_name": "baby bed, baby's bed", "parent_names": ["bedroom furniture"], "child_names": ["bassinet", "cradle", "crib", "cot"], "candidate_sentences": ["a bassinet, which is a baby bed, baby's bed, which is a bedroom furniture", "a cradle, which is a baby bed, baby's bed, which is a bedroom furniture", "a crib, which is a baby bed, baby's bed, which is a bedroom furniture", "a cot, which is a baby bed, baby's bed, which is a bedroom furniture"]}, "243": {"node_name": "sea urchin", "parent_names": ["dummy13"], "child_names": [], "candidate_sentences": ["a sea urchin, which is a dummy13"]}, "244": {"node_name": "perfume, essence", "parent_names": ["cosmetic"], "child_names": [], "candidate_sentences": ["a perfume, essence, which is a cosmetic"]}, "245": {"node_name": "mug", "parent_names": ["tableware"], "child_names": ["coffee mug"], "candidate_sentences": ["a coffee mug, which is a mug, which is a tableware"]}, "246": {"node_name": "loaf of bread, loaf", "parent_names": ["baked goods"], "child_names": ["French loaf"], "candidate_sentences": ["a French loaf, which is a loaf of bread, loaf, which is a baked goods"]}, "247": {"node_name": "bicycle, bike, wheel, cycle", "parent_names": ["cycles"], "child_names": ["bicycle-built-for-two", "tandem bicycle", "tandem", "mountain bike", "all-terrain bike", "off-roader"], "candidate_sentences": ["a bicycle-built-for-two, which is a bicycle, bike, wheel, cycle, which is a cycles", "a tandem bicycle, which is a bicycle, bike, wheel, cycle, which is a cycles", "a tandem, which is a bicycle, bike, wheel, cycle, which is a cycles", "a mountain bike, which is a bicycle, bike, wheel, cycle, which is a cycles", "a all-terrain bike, which is a bicycle, bike, wheel, cycle, which is a cycles", "a off-roader, which is a bicycle, bike, wheel, cycle, which is a cycles"]}, "248": {"node_name": "handkerchief, hankie, hanky, hankey", "parent_names": ["dummy50"], "child_names": [], "candidate_sentences": ["a handkerchief, hankie, hanky, hankey, which is a dummy50"]}, "249": {"node_name": "rotisserie", "parent_names": ["home appliance", "household appliance"], "child_names": [], "candidate_sentences": ["a rotisserie, which is a home appliance", "a rotisserie, which is a household appliance"]}, "250": {"node_name": "pudding, pud", "parent_names": ["dessert", "sweet", "afters"], "child_names": ["trifle"], "candidate_sentences": ["a trifle, which is a pudding, pud, which is a dessert", "a trifle, which is a pudding, pud, which is a sweet", "a trifle, which is a pudding, pud, which is a afters"]}, "251": {"node_name": "ant, emmet, pismire", "parent_names": ["insect"], "child_names": [], "candidate_sentences": ["a ant, emmet, pismire, which is a insect"]}, "252": {"node_name": "column, pillar", "parent_names": ["dummy78"], "child_names": ["obelisk", "pedestal", "plinth", "footstall", "totem pole"], "candidate_sentences": ["a obelisk, which is a column, pillar, which is a dummy78", "a pedestal, which is a column, pillar, which is a dummy78", "a plinth, which is a column, pillar, which is a dummy78", "a footstall, which is a column, pillar, which is a dummy78", "a totem pole, which is a column, pillar, which is a dummy78"]}, "253": {"node_name": "lampshade, lamp shade", "parent_names": ["lamp"], "child_names": [], "candidate_sentences": ["a lampshade, lamp shade, which is a lamp"]}, "254": {"node_name": "chambered nautilus, pearly nautilus, nautilus", "parent_names": ["cephalopod", "cephalopod mollusk"], "child_names": [], "candidate_sentences": ["a chambered nautilus, pearly nautilus, nautilus, which is a cephalopod", "a chambered nautilus, pearly nautilus, nautilus, which is a cephalopod mollusk"]}, "255": {"node_name": "rug, carpet, carpeting", "parent_names": ["floor cover", "floor covering"], "child_names": ["prayer rug", "prayer mat"], "candidate_sentences": ["a prayer rug, which is a rug, carpet, carpeting, which is a floor cover", "a prayer mat, which is a rug, carpet, carpeting, which is a floor cover", "a prayer rug, which is a rug, carpet, carpeting, which is a floor covering", "a prayer mat, which is a rug, carpet, carpeting, which is a floor covering"]}, "256": {"node_name": "pan, cooking pan", "parent_names": ["kitchen utensil"], "child_names": ["frying pan", "frypan", "skillet", "wok"], "candidate_sentences": ["a frying pan, which is a pan, cooking pan, which is a kitchen utensil", "a frypan, which is a pan, cooking pan, which is a kitchen utensil", "a skillet, which is a pan, cooking pan, which is a kitchen utensil", "a wok, which is a pan, cooking pan, which is a kitchen utensil"]}, "257": {"node_name": "hummingbird", "parent_names": ["apodiform bird"], "child_names": [], "candidate_sentences": ["a hummingbird, which is a apodiform bird"]}, "258": {"node_name": "hammer", "parent_names": ["tool"], "child_names": [], "candidate_sentences": ["a hammer, which is a tool"]}, "259": {"node_name": "pomegranate", "parent_names": ["fruit"], "child_names": [], "candidate_sentences": ["a pomegranate, which is a fruit"]}, "260": {"node_name": "scorpion", "parent_names": ["arachnid", "arachnoid"], "child_names": [], "candidate_sentences": ["a scorpion, which is a arachnid", "a scorpion, which is a arachnoid"]}, "261": {"node_name": "saltshaker, salt shaker", "parent_names": ["kitchen utensil"], "child_names": [], "candidate_sentences": ["a saltshaker, salt shaker, which is a kitchen utensil"]}, "262": {"node_name": "lobster", "parent_names": ["crustacean"], "child_names": ["spiny lobster", "langouste", "rock lobster", "crawfish", "crayfish", "sea crawfish", "true lobster"], "candidate_sentences": ["a spiny lobster, which is a lobster, which is a crustacean", "a langouste, which is a lobster, which is a crustacean", "a rock lobster, which is a lobster, which is a crustacean", "a crawfish, which is a lobster, which is a crustacean", "a crayfish, which is a lobster, which is a crustacean", "a sea crawfish, which is a lobster, which is a crustacean", "a true lobster, which is a lobster, which is a crustacean"]}, "263": {"node_name": "frozen dessert", "parent_names": ["dessert", "sweet", "afters"], "child_names": ["ice cream", "icecream", "ice lolly", "lolly", "lollipop", "popsicle"], "candidate_sentences": ["a ice cream, which is a frozen dessert, which is a dessert", "a icecream, which is a frozen dessert, which is a dessert", "a ice lolly, which is a frozen dessert, which is a dessert", "a lolly, which is a frozen dessert, which is a dessert", "a lollipop, which is a frozen dessert, which is a dessert", "a popsicle, which is a frozen dessert, which is a dessert", "a ice cream, which is a frozen dessert, which is a sweet", "a icecream, which is a frozen dessert, which is a sweet", "a ice lolly, which is a frozen dessert, which is a sweet", "a lolly, which is a frozen dessert, which is a sweet", "a lollipop, which is a frozen dessert, which is a sweet", "a popsicle, which is a frozen dessert, which is a sweet", "a ice cream, which is a frozen dessert, which is a afters", "a icecream, which is a frozen dessert, which is a afters", "a ice lolly, which is a frozen dessert, which is a afters", "a lolly, which is a frozen dessert, which is a afters", "a lollipop, which is a frozen dessert, which is a afters", "a popsicle, which is a frozen dessert, which is a afters"]}, "264": {"node_name": "damselfly", "parent_names": ["insect"], "child_names": [], "candidate_sentences": ["a damselfly, which is a insect"]}, "265": {"node_name": "dining table, board", "parent_names": ["table"], "child_names": [], "candidate_sentences": ["a dining table, board, which is a table"]}, "266": {"node_name": "amphibian, amphibious vehicle", "parent_names": ["tracked vehicle"], "child_names": [], "candidate_sentences": ["a amphibian, amphibious vehicle, which is a tracked vehicle"]}, "267": {"node_name": "pepper", "parent_names": ["vegetable", "veggie", "veg"], "child_names": ["sweet pepper"], "candidate_sentences": ["a sweet pepper, which is a pepper, which is a vegetable", "a sweet pepper, which is a pepper, which is a veggie", "a sweet pepper, which is a pepper, which is a veg"]}, "268": {"node_name": "weasel", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a weasel, which is a carnivore"]}, "269": {"node_name": "bowl", "parent_names": ["tableware"], "child_names": ["mixing bowl", "soup bowl"], "candidate_sentences": ["a mixing bowl, which is a bowl, which is a tableware", "a soup bowl, which is a bowl, which is a tableware"]}, "270": {"node_name": "bow", "parent_names": ["weapon", "arm", "weapon system"], "child_names": [], "candidate_sentences": ["a bow, which is a weapon", "a bow, which is a arm", "a bow, which is a weapon system"]}, "271": {"node_name": "potato, white potato, Irish potato, murphy, spud, tater", "parent_names": ["dish"], "child_names": ["mashed potato"], "candidate_sentences": ["a mashed potato, which is a potato, white potato, Irish potato, murphy, spud, tater, which is a dish"]}, "272": {"node_name": "alligator, gator", "parent_names": ["crocodilian reptile", "crocodilian"], "child_names": ["American alligator", "Alligator mississipiensis"], "candidate_sentences": ["a American alligator, which is a alligator, gator, which is a crocodilian reptile", "a Alligator mississipiensis, which is a alligator, gator, which is a crocodilian reptile", "a American alligator, which is a alligator, gator, which is a crocodilian", "a Alligator mississipiensis, which is a alligator, gator, which is a crocodilian"]}, "273": {"node_name": "digital computer", "parent_names": ["electronic equipment"], "child_names": ["desktop computer", "hand-held computer", "hand-held microcomputer", "laptop", "laptop computer", "notebook", "notebook computer"], "candidate_sentences": ["a desktop computer, which is a digital computer, which is a electronic equipment", "a hand-held computer, which is a digital computer, which is a electronic equipment", "a hand-held microcomputer, which is a digital computer, which is a electronic equipment", "a laptop, which is a digital computer, which is a electronic equipment", "a laptop computer, which is a digital computer, which is a electronic equipment", "a notebook, which is a digital computer, which is a electronic equipment", "a notebook computer, which is a digital computer, which is a electronic equipment"]}, "274": {"node_name": "undergarment, unmentionable", "parent_names": ["garment"], "child_names": ["brassiere", "bra", "bandeau", "diaper", "nappy", "napkin"], "candidate_sentences": ["a brassiere, which is a undergarment, unmentionable, which is a garment", "a bra, which is a undergarment, unmentionable, which is a garment", "a bandeau, which is a undergarment, unmentionable, which is a garment", "a diaper, which is a undergarment, unmentionable, which is a garment", "a nappy, which is a undergarment, unmentionable, which is a garment", "a napkin, which is a undergarment, unmentionable, which is a garment"]}, "275": {"node_name": "sandbar, sand bar", "parent_names": ["bar"], "child_names": [], "candidate_sentences": ["a sandbar, sand bar, which is a bar"]}, "276": {"node_name": "scuba diver", "parent_names": ["person", "individual", "someone", "somebody", "mortal", "soul"], "child_names": [], "candidate_sentences": ["a scuba diver, which is a person", "a scuba diver, which is a individual", "a scuba diver, which is a someone", "a scuba diver, which is a somebody", "a scuba diver, which is a mortal", "a scuba diver, which is a soul"]}, "277": {"node_name": "entertainment center", "parent_names": ["wall unit"], "child_names": [], "candidate_sentences": ["a entertainment center, which is a wall unit"]}, "278": {"node_name": "jin<PERSON><PERSON>a, ricksha, rickshaw", "parent_names": ["cart"], "child_names": [], "candidate_sentences": ["a jinrikisha, ricksha, rickshaw, which is a cart"]}, "279": {"node_name": "hog, pig, grunter, squealer, Sus scrofa", "parent_names": ["ungulate", "hoofed mammal"], "child_names": [], "candidate_sentences": ["a hog, pig, grunter, squealer, Sus scrofa, which is a ungulate", "a hog, pig, grunter, squealer, Sus scrofa, which is a hoofed mammal"]}, "280": {"node_name": "knife", "parent_names": ["kitchen utensil"], "child_names": ["cleaver", "meat cleaver", "chopper"], "candidate_sentences": ["a cleaver, which is a knife, which is a kitchen utensil", "a meat cleaver, which is a knife, which is a kitchen utensil", "a chopper, which is a knife, which is a kitchen utensil"]}, "281": {"node_name": "table lamp", "parent_names": ["lamp"], "child_names": [], "candidate_sentences": ["a table lamp, which is a lamp"]}, "282": {"node_name": "joystick", "parent_names": ["electronic equipment"], "child_names": [], "candidate_sentences": ["a joystick, which is a electronic equipment"]}, "283": {"node_name": "sea anemone, anemone", "parent_names": ["anthozoan", "actinozoan"], "child_names": [], "candidate_sentences": ["a sea anemone, anemone, which is a anthozoan", "a sea anemone, anemone, which is a actinozoan"]}, "284": {"node_name": "echidna, spiny anteater, anteater", "parent_names": ["monotreme", "egg-laying mammal"], "child_names": [], "candidate_sentences": ["a echidna, spiny anteater, anteater, which is a monotreme", "a echidna, spiny anteater, anteater, which is a egg-laying mammal"]}, "285": {"node_name": "lemon", "parent_names": ["fruit"], "child_names": [], "candidate_sentences": ["a lemon, which is a fruit"]}, "286": {"node_name": "squash", "parent_names": ["vegetable", "veggie", "veg"], "child_names": ["winter squash", "summer squash"], "candidate_sentences": ["a winter squash, which is a squash, which is a vegetable", "a summer squash, which is a squash, which is a vegetable", "a winter squash, which is a squash, which is a veggie", "a summer squash, which is a squash, which is a veggie", "a winter squash, which is a squash, which is a veg", "a summer squash, which is a squash, which is a veg"]}, "287": {"node_name": "alp", "parent_names": ["mountain", "mount"], "child_names": [], "candidate_sentences": ["a alp, which is a mountain", "a alp, which is a mount"]}, "288": {"node_name": "monitor", "parent_names": ["electronic equipment"], "child_names": [], "candidate_sentences": ["a monitor, which is a electronic equipment"]}, "289": {"node_name": "half track", "parent_names": ["tracked vehicle"], "child_names": [], "candidate_sentences": ["a half track, which is a tracked vehicle"]}, "290": {"node_name": "tower", "parent_names": ["building", "edifice"], "child_names": ["beacon", "lighthouse", "beacon light", "pharos"], "candidate_sentences": ["a beacon, which is a tower, which is a building", "a lighthouse, which is a tower, which is a building", "a beacon light, which is a tower, which is a building", "a pharos, which is a tower, which is a building", "a beacon, which is a tower, which is a edifice", "a lighthouse, which is a tower, which is a edifice", "a beacon light, which is a tower, which is a edifice", "a pharos, which is a tower, which is a edifice"]}, "291": {"node_name": "suit, suit of clothes", "parent_names": ["garment"], "child_names": [], "candidate_sentences": ["a suit, suit of clothes, which is a garment"]}, "292": {"node_name": "roof", "parent_names": ["area"], "child_names": ["dome", "thatch", "thatched roof", "tile roof", "vault"], "candidate_sentences": ["a dome, which is a roof, which is a area", "a thatch, which is a roof, which is a area", "a thatched roof, which is a roof, which is a area", "a tile roof, which is a roof, which is a area", "a vault, which is a roof, which is a area"]}, "293": {"node_name": "ball", "parent_names": ["sports equipment"], "child_names": ["baseball", "basketball", "croquet ball", "golf ball", "ping-pong ball", "punching bag", "punch bag", "punching ball", "punchball", "rugby ball", "soccer ball", "tennis ball", "volleyball"], "candidate_sentences": ["a baseball, which is a ball, which is a sports equipment", "a basketball, which is a ball, which is a sports equipment", "a croquet ball, which is a ball, which is a sports equipment", "a golf ball, which is a ball, which is a sports equipment", "a ping-pong ball, which is a ball, which is a sports equipment", "a punching bag, which is a ball, which is a sports equipment", "a punch bag, which is a ball, which is a sports equipment", "a punching ball, which is a ball, which is a sports equipment", "a punchball, which is a ball, which is a sports equipment", "a rugby ball, which is a ball, which is a sports equipment", "a soccer ball, which is a ball, which is a sports equipment", "a tennis ball, which is a ball, which is a sports equipment", "a volleyball, which is a ball, which is a sports equipment"]}, "294": {"node_name": "toilet seat", "parent_names": ["seat"], "child_names": [], "candidate_sentences": ["a toilet seat, which is a seat"]}, "295": {"node_name": "titmouse, tit", "parent_names": ["passerine", "passeriform bird"], "child_names": ["chickadee"], "candidate_sentences": ["a chickadee, which is a titmouse, tit, which is a passerine", "a chickadee, which is a titmouse, tit, which is a passeriform bird"]}, "296": {"node_name": "sliding door", "parent_names": ["door"], "child_names": [], "candidate_sentences": ["a sliding door, which is a door"]}, "297": {"node_name": "llama", "parent_names": ["ungulate", "hoofed mammal"], "child_names": [], "candidate_sentences": ["a llama, which is a ungulate", "a llama, which is a hoofed mammal"]}, "298": {"node_name": "audio system, sound system", "parent_names": ["electronic equipment"], "child_names": ["iPod", "loudspeaker", "speaker", "speaker unit", "loudspeaker system", "speaker system"], "candidate_sentences": ["a iPod, which is a audio system, sound system, which is a electronic equipment", "a loudspeaker, which is a audio system, sound system, which is a electronic equipment", "a speaker, which is a audio system, sound system, which is a electronic equipment", "a speaker unit, which is a audio system, sound system, which is a electronic equipment", "a loudspeaker system, which is a audio system, sound system, which is a electronic equipment", "a speaker system, which is a audio system, sound system, which is a electronic equipment"]}, "299": {"node_name": "tricycle, trike, velocipede", "parent_names": ["cycles"], "child_names": [], "candidate_sentences": ["a tricycle, trike, velocipede, which is a cycles"]}, "300": {"node_name": "punch", "parent_names": ["alcohol", "alcoholic drink", "alcoholic beverage", "intoxicant", "inebriant"], "child_names": ["cup", "eggnog"], "candidate_sentences": ["a cup, which is a punch, which is a alcohol", "a eggnog, which is a punch, which is a alcohol", "a cup, which is a punch, which is a alcoholic drink", "a eggnog, which is a punch, which is a alcoholic drink", "a cup, which is a punch, which is a alcoholic beverage", "a eggnog, which is a punch, which is a alcoholic beverage", "a cup, which is a punch, which is a intoxicant", "a eggnog, which is a punch, which is a intoxicant", "a cup, which is a punch, which is a inebriant", "a eggnog, which is a punch, which is a inebriant"]}, "301": {"node_name": "waffle iron", "parent_names": ["home appliance", "household appliance"], "child_names": [], "candidate_sentences": ["a waffle iron, which is a home appliance", "a waffle iron, which is a household appliance"]}, "302": {"node_name": "home theater, home theatre", "parent_names": ["electronic equipment"], "child_names": [], "candidate_sentences": ["a home theater, home theatre, which is a electronic equipment"]}, "303": {"node_name": "plate", "parent_names": ["tableware"], "child_names": [], "candidate_sentences": ["a plate, which is a tableware"]}, "304": {"node_name": "cabbage, chou", "parent_names": ["vegetable", "veggie", "veg"], "child_names": ["head cabbage"], "candidate_sentences": ["a head cabbage, which is a cabbage, chou, which is a vegetable", "a head cabbage, which is a cabbage, chou, which is a veggie", "a head cabbage, which is a cabbage, chou, which is a veg"]}, "305": {"node_name": "polecat, fitch, foulmart, foumart, <PERSON><PERSON> putorius", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a polecat, fitch, foulmart, foumart, Mustela putorius, which is a carnivore"]}, "306": {"node_name": "military uniform", "parent_names": ["garment"], "child_names": [], "candidate_sentences": ["a military uniform, which is a garment"]}, "307": {"node_name": "computer keyboard, keypad", "parent_names": ["electronic equipment"], "child_names": [], "candidate_sentences": ["a computer keyboard, keypad, which is a electronic equipment"]}, "308": {"node_name": "lesser panda, red panda, panda, bear cat, cat bear, Ailurus fulgens", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a lesser panda, red panda, panda, bear cat, cat bear, Ailurus fulgens, which is a carnivore"]}, "309": {"node_name": "goat, caprine animal", "parent_names": ["ungulate", "hoofed mammal"], "child_names": ["ibex", "<PERSON><PERSON> ibex"], "candidate_sentences": ["a ibex, which is a goat, caprine animal, which is a ungulate", "a Capra ibex, which is a goat, caprine animal, which is a ungulate", "a ibex, which is a goat, caprine animal, which is a hoofed mammal", "a Capra ibex, which is a goat, caprine animal, which is a hoofed mammal"]}, "310": {"node_name": "goldfish, Carassius auratus", "parent_names": ["bony fish"], "child_names": [], "candidate_sentences": ["a goldfish, Carassius auratus, which is a bony fish"]}, "311": {"node_name": "snowmobile", "parent_names": ["tracked vehicle"], "child_names": [], "candidate_sentences": ["a snowmobile, which is a tracked vehicle"]}, "312": {"node_name": "hat, chapeau, lid", "parent_names": ["headdress", "headgear"], "child_names": ["bearskin", "busby", "shako", "bonnet", "poke bonnet", "cowboy hat", "ten-gallon hat", "sombrero"], "candidate_sentences": ["a bearskin, which is a hat, chapeau, lid, which is a headdress", "a busby, which is a hat, chapeau, lid, which is a headdress", "a shako, which is a hat, chapeau, lid, which is a headdress", "a bonnet, which is a hat, chapeau, lid, which is a headdress", "a poke bonnet, which is a hat, chapeau, lid, which is a headdress", "a cowboy hat, which is a hat, chapeau, lid, which is a headdress", "a ten-gallon hat, which is a hat, chapeau, lid, which is a headdress", "a sombrero, which is a hat, chapeau, lid, which is a headdress", "a bearskin, which is a hat, chapeau, lid, which is a headgear", "a busby, which is a hat, chapeau, lid, which is a headgear", "a shako, which is a hat, chapeau, lid, which is a headgear", "a bonnet, which is a hat, chapeau, lid, which is a headgear", "a poke bonnet, which is a hat, chapeau, lid, which is a headgear", "a cowboy hat, which is a hat, chapeau, lid, which is a headgear", "a ten-gallon hat, which is a hat, chapeau, lid, which is a headgear", "a sombrero, which is a hat, chapeau, lid, which is a headgear"]}, "313": {"node_name": "cushion", "parent_names": ["padding", "cushioning"], "child_names": ["pillow"], "candidate_sentences": ["a pillow, which is a cushion, which is a padding", "a pillow, which is a cushion, which is a cushioning"]}, "314": {"node_name": "tench, Tinca tinca", "parent_names": ["bony fish"], "child_names": [], "candidate_sentences": ["a tench, Tinca tinca, which is a bony fish"]}, "315": {"node_name": "tights, leotards", "parent_names": ["footwear", "legwear"], "child_names": ["maillot"], "candidate_sentences": ["a maillot, which is a tights, leotards, which is a footwear", "a maillot, which is a tights, leotards, which is a legwear"]}, "316": {"node_name": "streetcar, tram, tramcar, trolley, trolley car", "parent_names": ["train", "railroad train"], "child_names": [], "candidate_sentences": ["a streetcar, tram, tramcar, trolley, trolley car, which is a train", "a streetcar, tram, tramcar, trolley, trolley car, which is a railroad train"]}, "317": {"node_name": "bustard", "parent_names": ["aquatic bird"], "child_names": [], "candidate_sentences": ["a bustard, which is a aquatic bird"]}, "318": {"node_name": "ray", "parent_names": ["cartilaginous fish", "chondrichthian"], "child_names": ["electric ray", "crampfish", "numbfish", "torpedo", "stingray"], "candidate_sentences": ["a electric ray, which is a ray, which is a cartilaginous fish", "a crampfish, which is a ray, which is a cartilaginous fish", "a numbfish, which is a ray, which is a cartilaginous fish", "a torpedo, which is a ray, which is a cartilaginous fish", "a stingray, which is a ray, which is a cartilaginous fish", "a electric ray, which is a ray, which is a chondrichthian", "a crampfish, which is a ray, which is a chondrichthian", "a numbfish, which is a ray, which is a chondrichthian", "a torpedo, which is a ray, which is a chondrichthian", "a stingray, which is a ray, which is a chondrichthian"]}, "319": {"node_name": "butterfly fish", "parent_names": ["bony fish"], "child_names": ["rock beauty", "Holocanthus tricolor"], "candidate_sentences": ["a rock beauty, which is a butterfly fish, which is a bony fish", "a Holocanthus tricolor, which is a butterfly fish, which is a bony fish"]}, "320": {"node_name": "butterfly", "parent_names": ["insect"], "child_names": ["ringlet", "ringlet butterfly", "sulphur butterfly", "sulfur butterfly", "lycaenid", "lycaenid butterfly", "pierid", "pierid butterfly", "nymphalid", "nymphalid butterfly", "brush-footed butterfly", "four-footed butterfly", "danaid", "danaid butterfly"], "candidate_sentences": ["a ringlet, which is a butterfly, which is a insect", "a ringlet butterfly, which is a butterfly, which is a insect", "a sulphur butterfly, which is a butterfly, which is a insect", "a sulfur butterfly, which is a butterfly, which is a insect", "a lycaenid, which is a butterfly, which is a insect", "a lycaenid butterfly, which is a butterfly, which is a insect", "a pierid, which is a butterfly, which is a insect", "a pierid butterfly, which is a butterfly, which is a insect", "a nymphalid, which is a butterfly, which is a insect", "a nymphalid butterfly, which is a butterfly, which is a insect", "a brush-footed butterfly, which is a butterfly, which is a insect", "a four-footed butterfly, which is a butterfly, which is a insect", "a danaid, which is a butterfly, which is a insect", "a danaid butterfly, which is a butterfly, which is a insect"]}, "321": {"node_name": "giant panda, panda, panda bear, coon bear, Ailuropoda melanoleuca", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a giant panda, panda, panda bear, coon bear, Ailuropoda melanoleuca, which is a carnivore"]}, "322": {"node_name": "jaguar, panther, Panthera onca, Felis onca", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a jaguar, panther, Panthera onca, Felis onca, which is a carnivore"]}, "323": {"node_name": "skunk, polecat, wood pussy", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a skunk, polecat, wood pussy, which is a carnivore"]}, "324": {"node_name": "lakeside, lakeshore", "parent_names": ["shore"], "child_names": [], "candidate_sentences": ["a lakeside, lakeshore, which is a shore"]}, "325": {"node_name": "spectacles, specs, eyeglasses, glasses", "parent_names": ["facial accessories"], "child_names": ["sunglasses", "dark glasses", "shades"], "candidate_sentences": ["a sunglasses, which is a spectacles, specs, eyeglasses, glasses, which is a facial accessories", "a dark glasses, which is a spectacles, specs, eyeglasses, glasses, which is a facial accessories", "a shades, which is a spectacles, specs, eyeglasses, glasses, which is a facial accessories"]}, "326": {"node_name": "hip, rose hip, rosehip", "parent_names": ["fruit"], "child_names": [], "candidate_sentences": ["a hip, rose hip, rosehip, which is a fruit"]}, "327": {"node_name": "daisy", "parent_names": ["flower"], "child_names": [], "candidate_sentences": ["a daisy, which is a flower"]}, "328": {"node_name": "jellyfish", "parent_names": ["dummy10"], "child_names": [], "candidate_sentences": ["a jellyfish, which is a dummy10"]}, "329": {"node_name": "magpie", "parent_names": ["passerine", "passeriform bird"], "child_names": [], "candidate_sentences": ["a magpie, which is a passerine", "a magpie, which is a passeriform bird"]}, "330": {"node_name": "sofa, couch, lounge", "parent_names": ["seat"], "child_names": ["bench", "convertible", "sofa bed"], "candidate_sentences": ["a bench, which is a sofa, couch, lounge, which is a seat", "a convertible, which is a sofa, couch, lounge, which is a seat", "a sofa bed, which is a sofa, couch, lounge, which is a seat"]}, "331": {"node_name": "jug", "parent_names": ["tableware"], "child_names": ["water jug", "whiskey jug"], "candidate_sentences": ["a water jug, which is a jug, which is a tableware", "a whiskey jug, which is a jug, which is a tableware"]}, "332": {"node_name": "meerkat, mierkat", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a meerkat, mierkat, which is a carnivore"]}, "333": {"node_name": "orchid, orchidaceous plant", "parent_names": ["flower"], "child_names": ["lady's slipper", "lady-slipper", "ladies' slipper", "slipper orchid"], "candidate_sentences": ["a lady's slipper, which is a orchid, orchidaceous plant, which is a flower", "a lady-slipper, which is a orchid, orchidaceous plant, which is a flower", "a ladies' slipper, which is a orchid, orchidaceous plant, which is a flower", "a slipper orchid, which is a orchid, orchidaceous plant, which is a flower"]}, "334": {"node_name": "goldfinch, <PERSON><PERSON><PERSON> card<PERSON>", "parent_names": ["passerine", "passeriform bird"], "child_names": [], "candidate_sentences": ["a goldfinch, <PERSON><PERSON><PERSON> carduelis, which is a passerine", "a goldfinch, <PERSON><PERSON><PERSON> carduelis, which is a passeriform bird"]}, "335": {"node_name": "black-footed ferret, ferret, <PERSON><PERSON> nigripes", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a black-footed ferret, ferret, <PERSON>ela nigripes, which is a carnivore"]}, "336": {"node_name": "dwelling, home, domicile, abode, habitation, dwelling house", "parent_names": ["building", "edifice"], "child_names": ["birdhouse", "castle", "cliff dwelling", "mobile home", "manufactured home", "monastery", "palace", "yurt"], "candidate_sentences": ["a birdhouse, which is a dwelling, home, domicile, abode, habitation, dwelling house, which is a building", "a castle, which is a dwelling, home, domicile, abode, habitation, dwelling house, which is a building", "a cliff dwelling, which is a dwelling, home, domicile, abode, habitation, dwelling house, which is a building", "a mobile home, which is a dwelling, home, domicile, abode, habitation, dwelling house, which is a building", "a manufactured home, which is a dwelling, home, domicile, abode, habitation, dwelling house, which is a building", "a monastery, which is a dwelling, home, domicile, abode, habitation, dwelling house, which is a building", "a palace, which is a dwelling, home, domicile, abode, habitation, dwelling house, which is a building", "a yurt, which is a dwelling, home, domicile, abode, habitation, dwelling house, which is a building", "a birdhouse, which is a dwelling, home, domicile, abode, habitation, dwelling house, which is a edifice", "a castle, which is a dwelling, home, domicile, abode, habitation, dwelling house, which is a edifice", "a cliff dwelling, which is a dwelling, home, domicile, abode, habitation, dwelling house, which is a edifice", "a mobile home, which is a dwelling, home, domicile, abode, habitation, dwelling house, which is a edifice", "a manufactured home, which is a dwelling, home, domicile, abode, habitation, dwelling house, which is a edifice", "a monastery, which is a dwelling, home, domicile, abode, habitation, dwelling house, which is a edifice", "a palace, which is a dwelling, home, domicile, abode, habitation, dwelling house, which is a edifice", "a yurt, which is a dwelling, home, domicile, abode, habitation, dwelling house, which is a edifice"]}, "337": {"node_name": "microwave, microwave oven", "parent_names": ["home appliance", "household appliance"], "child_names": [], "candidate_sentences": ["a microwave, microwave oven, which is a home appliance", "a microwave, microwave oven, which is a household appliance"]}, "338": {"node_name": "power drill", "parent_names": ["tool"], "child_names": [], "candidate_sentences": ["a power drill, which is a tool"]}, "339": {"node_name": "sewing machine", "parent_names": ["home appliance", "household appliance"], "child_names": [], "candidate_sentences": ["a sewing machine, which is a home appliance", "a sewing machine, which is a household appliance"]}, "340": {"node_name": "scale, weighing machine", "parent_names": ["measuring instrument", "measuring system", "measuring device"], "child_names": [], "candidate_sentences": ["a scale, weighing machine, which is a measuring instrument", "a scale, weighing machine, which is a measuring system", "a scale, weighing machine, which is a measuring device"]}, "341": {"node_name": "dummy47", "parent_names": ["building", "edifice"], "child_names": ["prison", "prison house"], "candidate_sentences": ["a prison, which is a dummy47, which is a building", "a prison house, which is a dummy47, which is a building", "a prison, which is a dummy47, which is a edifice", "a prison house, which is a dummy47, which is a edifice"]}, "342": {"node_name": "squirrel", "parent_names": ["rodent", "gnawer"], "child_names": ["fox squirrel", "eastern fox squirrel", "Sciurus niger"], "candidate_sentences": ["a fox squirrel, which is a squirrel, which is a rodent", "a eastern fox squirrel, which is a squirrel, which is a rodent", "a Sciurus niger, which is a squirrel, which is a rodent", "a fox squirrel, which is a squirrel, which is a gnawer", "a eastern fox squirrel, which is a squirrel, which is a gnawer", "a Sciurus niger, which is a squirrel, which is a gnawer"]}, "343": {"node_name": "wild dog", "parent_names": ["carnivore"], "child_names": ["dingo", "warrigal", "warragal", "<PERSON>is dingo", "dhole", "Cuon alpinus", "African hunting dog", "hyena dog", "Cape hunting dog", "Lycaon pictus"], "candidate_sentences": ["a dingo, which is a wild dog, which is a carnivore", "a warrigal, which is a wild dog, which is a carnivore", "a warragal, which is a wild dog, which is a carnivore", "a Canis dingo, which is a wild dog, which is a carnivore", "a dhole, which is a wild dog, which is a carnivore", "a Cuon alpinus, which is a wild dog, which is a carnivore", "a African hunting dog, which is a wild dog, which is a carnivore", "a hyena dog, which is a wild dog, which is a carnivore", "a Cape hunting dog, which is a wild dog, which is a carnivore", "a Lycaon pictus, which is a wild dog, which is a carnivore"]}, "344": {"node_name": "lens cap, lens cover", "parent_names": ["photographic equipment"], "child_names": [], "candidate_sentences": ["a lens cap, lens cover, which is a photographic equipment"]}, "345": {"node_name": "percussion instrument, percussive instrument", "parent_names": ["musical instrument", "instrument"], "child_names": ["chime", "bell", "gong", "drum", "membranophone", "tympan", "gong", "tam-tam", "maraca", "marimba", "xylophone", "steel drum"], "candidate_sentences": ["a chime, which is a percussion instrument, percussive instrument, which is a musical instrument", "a bell, which is a percussion instrument, percussive instrument, which is a musical instrument", "a gong, which is a percussion instrument, percussive instrument, which is a musical instrument", "a drum, which is a percussion instrument, percussive instrument, which is a musical instrument", "a membranophone, which is a percussion instrument, percussive instrument, which is a musical instrument", "a tympan, which is a percussion instrument, percussive instrument, which is a musical instrument", "a gong, which is a percussion instrument, percussive instrument, which is a musical instrument", "a tam-tam, which is a percussion instrument, percussive instrument, which is a musical instrument", "a maraca, which is a percussion instrument, percussive instrument, which is a musical instrument", "a marimba, which is a percussion instrument, percussive instrument, which is a musical instrument", "a xylophone, which is a percussion instrument, percussive instrument, which is a musical instrument", "a steel drum, which is a percussion instrument, percussive instrument, which is a musical instrument", "a chime, which is a percussion instrument, percussive instrument, which is a instrument", "a bell, which is a percussion instrument, percussive instrument, which is a instrument", "a gong, which is a percussion instrument, percussive instrument, which is a instrument", "a drum, which is a percussion instrument, percussive instrument, which is a instrument", "a membranophone, which is a percussion instrument, percussive instrument, which is a instrument", "a tympan, which is a percussion instrument, percussive instrument, which is a instrument", "a gong, which is a percussion instrument, percussive instrument, which is a instrument", "a tam-tam, which is a percussion instrument, percussive instrument, which is a instrument", "a maraca, which is a percussion instrument, percussive instrument, which is a instrument", "a marimba, which is a percussion instrument, percussive instrument, which is a instrument", "a xylophone, which is a percussion instrument, percussive instrument, which is a instrument", "a steel drum, which is a percussion instrument, percussive instrument, which is a instrument"]}, "346": {"node_name": "stove", "parent_names": ["home appliance", "household appliance"], "child_names": [], "candidate_sentences": ["a stove, which is a home appliance", "a stove, which is a household appliance"]}, "347": {"node_name": "platypus, duckbill, duckbilled platypus, duck-billed platypus, Ornithorhynchus anatinus", "parent_names": ["monotreme", "egg-laying mammal"], "child_names": [], "candidate_sentences": ["a platypus, duckbill, duckbilled platypus, duck-billed platypus, Ornithorhynchus anatinus, which is a monotreme", "a platypus, duckbill, duckbilled platypus, duck-billed platypus, Ornithorhynchus anatinus, which is a egg-laying mammal"]}, "348": {"node_name": "screwdriver", "parent_names": ["tool"], "child_names": [], "candidate_sentences": ["a screwdriver, which is a tool"]}, "349": {"node_name": "ladle", "parent_names": ["kitchen utensil"], "child_names": [], "candidate_sentences": ["a ladle, which is a kitchen utensil"]}, "350": {"node_name": "goose", "parent_names": ["aquatic bird"], "child_names": [], "candidate_sentences": ["a goose, which is a aquatic bird"]}, "351": {"node_name": "vacuum, vacuum cleaner", "parent_names": ["home appliance", "household appliance"], "child_names": [], "candidate_sentences": ["a vacuum, vacuum cleaner, which is a home appliance", "a vacuum, vacuum cleaner, which is a household appliance"]}, "352": {"node_name": "dishrag, dishcloth", "parent_names": ["piece of cloth", "piece of material"], "child_names": [], "candidate_sentences": ["a dishrag, dishcloth, which is a piece of cloth", "a dishrag, dishcloth, which is a piece of material"]}, "353": {"node_name": "warplane, military plane", "parent_names": ["aircraft"], "child_names": [], "candidate_sentences": ["a warplane, military plane, which is a aircraft"]}, "354": {"node_name": "lion, king of beasts, <PERSON><PERSON> leo", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a lion, king of beasts, <PERSON><PERSON> leo, which is a carnivore"]}, "355": {"node_name": "camera, photographic camera", "parent_names": ["photographic equipment"], "child_names": ["Polaroid camera", "Polaroid Land camera", "reflex camera"], "candidate_sentences": ["a Polaroid camera, which is a camera, photographic camera, which is a photographic equipment", "a Polaroid Land camera, which is a camera, photographic camera, which is a photographic equipment", "a reflex camera, which is a camera, photographic camera, which is a photographic equipment"]}, "356": {"node_name": "torch", "parent_names": ["tool"], "child_names": [], "candidate_sentences": ["a torch, which is a tool"]}, "357": {"node_name": "horse cart, horse-cart", "parent_names": ["cart"], "child_names": [], "candidate_sentences": ["a horse cart, horse-cart, which is a cart"]}, "358": {"node_name": "house finch, linnet, Carpodacus mexicanus", "parent_names": ["passerine", "passeriform bird"], "child_names": [], "candidate_sentences": ["a house finch, linnet, Carpodacus mexicanus, which is a passerine", "a house finch, linnet, Carpodacus mexicanus, which is a passeriform bird"]}, "359": {"node_name": "airplane, aeroplane, plane", "parent_names": ["aircraft"], "child_names": ["airliner"], "candidate_sentences": ["a airliner, which is a airplane, aeroplane, plane, which is a aircraft"]}, "360": {"node_name": "trouser, pant", "parent_names": ["garment"], "child_names": ["jean", "blue jean", "denim"], "candidate_sentences": ["a jean, which is a trouser, pant, which is a garment", "a blue jean, which is a trouser, pant, which is a garment", "a denim, which is a trouser, pant, which is a garment"]}, "361": {"node_name": "boot", "parent_names": ["footwear", "legwear"], "child_names": ["cowboy boot"], "candidate_sentences": ["a cowboy boot, which is a boot, which is a footwear", "a cowboy boot, which is a boot, which is a legwear"]}, "362": {"node_name": "antelope", "parent_names": ["ungulate", "hoofed mammal"], "child_names": ["hartebeest", "impala", "Aepycer<PERSON> melampus", "gazelle"], "candidate_sentences": ["a hartebeest, which is a antelope, which is a ungulate", "a impala, which is a antelope, which is a ungulate", "a Aepyceros melampus, which is a antelope, which is a ungulate", "a gazelle, which is a antelope, which is a ungulate", "a hartebeest, which is a antelope, which is a hoofed mammal", "a impala, which is a antelope, which is a hoofed mammal", "a Aepyceros melampus, which is a antelope, which is a hoofed mammal", "a gazelle, which is a antelope, which is a hoofed mammal"]}, "363": {"node_name": "wardrobe, closet, press", "parent_names": ["wall unit"], "child_names": [], "candidate_sentences": ["a wardrobe, closet, press, which is a wall unit"]}, "364": {"node_name": "cocktail shaker", "parent_names": ["kitchen utensil"], "child_names": [], "candidate_sentences": ["a cocktail shaker, which is a kitchen utensil"]}, "365": {"node_name": "cooker", "parent_names": ["home appliance", "household appliance"], "child_names": ["Crock Pot"], "candidate_sentences": ["a Crock Pot, which is a cooker, which is a home appliance", "a Crock Pot, which is a cooker, which is a household appliance"]}, "366": {"node_name": "umbrella", "parent_names": ["dummy57"], "child_names": [], "candidate_sentences": ["a umbrella, which is a dummy57"]}, "367": {"node_name": "buckle", "parent_names": ["dummy53"], "child_names": [], "candidate_sentences": ["a buckle, which is a dummy53"]}, "368": {"node_name": "projectile, missile", "parent_names": ["weapon", "arm", "weapon system"], "child_names": [], "candidate_sentences": ["a projectile, missile, which is a weapon", "a projectile, missile, which is a arm", "a projectile, missile, which is a weapon system"]}, "369": {"node_name": "meat loaf, meatloaf", "parent_names": ["dish"], "child_names": [], "candidate_sentences": ["a meat loaf, meatloaf, which is a dish"]}, "370": {"node_name": "glass, drinking glass", "parent_names": ["tableware"], "child_names": ["beer glass", "goblet"], "candidate_sentences": ["a beer glass, which is a glass, drinking glass, which is a tableware", "a goblet, which is a glass, drinking glass, which is a tableware"]}, "371": {"node_name": "cannon", "parent_names": ["weapon", "arm", "weapon system"], "child_names": [], "candidate_sentences": ["a cannon, which is a weapon", "a cannon, which is a arm", "a cannon, which is a weapon system"]}, "372": {"node_name": "mushroom", "parent_names": ["vegetable", "veggie", "veg"], "child_names": [], "candidate_sentences": ["a mushroom, which is a vegetable", "a mushroom, which is a veggie", "a mushroom, which is a veg"]}, "373": {"node_name": "robin, American robin, <PERSON><PERSON><PERSON> migratorius", "parent_names": ["passerine", "passeriform bird"], "child_names": [], "candidate_sentences": ["a robin, American robin, <PERSON><PERSON><PERSON> migratorius, which is a passerine", "a robin, American robin, Turd<PERSON> migratorius, which is a passeriform bird"]}, "374": {"node_name": "submarine, pigboat, sub, U-boat", "parent_names": ["vessel", "watercraft"], "child_names": [], "candidate_sentences": ["a submarine, pigboat, sub, U-boat, which is a vessel", "a submarine, pigboat, sub, U-boat, which is a watercraft"]}, "375": {"node_name": "oxcart", "parent_names": ["cart"], "child_names": [], "candidate_sentences": ["a oxcart, which is a cart"]}, "376": {"node_name": "knee pad", "parent_names": ["footwear", "legwear"], "child_names": [], "candidate_sentences": ["a knee pad, which is a footwear", "a knee pad, which is a legwear"]}, "377": {"node_name": "stethoscope", "parent_names": ["medical instrument"], "child_names": [], "candidate_sentences": ["a stethoscope, which is a medical instrument"]}, "378": {"node_name": "hare", "parent_names": ["lagomorph", "gnawing mammal"], "child_names": [], "candidate_sentences": ["a hare, which is a lagomorph", "a hare, which is a gnawing mammal"]}, "379": {"node_name": "isopod", "parent_names": ["crustacean"], "child_names": [], "candidate_sentences": ["a isopod, which is a crustacean"]}, "380": {"node_name": "oscilloscope, scope, cathode-ray oscilloscope, CRO", "parent_names": ["electronic equipment"], "child_names": [], "candidate_sentences": ["a oscilloscope, scope, cathode-ray oscilloscope, CRO, which is a electronic equipment"]}, "381": {"node_name": "wild sheep", "parent_names": ["ungulate", "hoofed mammal"], "child_names": ["mountain sheep"], "candidate_sentences": ["a mountain sheep, which is a wild sheep, which is a ungulate", "a mountain sheep, which is a wild sheep, which is a hoofed mammal"]}, "382": {"node_name": "racket, racquet", "parent_names": ["sports equipment"], "child_names": [], "candidate_sentences": ["a racket, racquet, which is a sports equipment"]}, "383": {"node_name": "timepiece, timekeeper, horologe", "parent_names": ["measuring instrument", "measuring system", "measuring device"], "child_names": ["sundial", "clock", "sandglass", "watch", "ticker", "timer"], "candidate_sentences": ["a sundial, which is a timepiece, timekeeper, horologe, which is a measuring instrument", "a clock, which is a timepiece, timekeeper, horologe, which is a measuring instrument", "a sandglass, which is a timepiece, timekeeper, horologe, which is a measuring instrument", "a watch, which is a timepiece, timekeeper, horologe, which is a measuring instrument", "a ticker, which is a timepiece, timekeeper, horologe, which is a measuring instrument", "a timer, which is a timepiece, timekeeper, horologe, which is a measuring instrument", "a sundial, which is a timepiece, timekeeper, horologe, which is a measuring system", "a clock, which is a timepiece, timekeeper, horologe, which is a measuring system", "a sandglass, which is a timepiece, timekeeper, horologe, which is a measuring system", "a watch, which is a timepiece, timekeeper, horologe, which is a measuring system", "a ticker, which is a timepiece, timekeeper, horologe, which is a measuring system", "a timer, which is a timepiece, timekeeper, horologe, which is a measuring system", "a sundial, which is a timepiece, timekeeper, horologe, which is a measuring device", "a clock, which is a timepiece, timekeeper, horologe, which is a measuring device", "a sandglass, which is a timepiece, timekeeper, horologe, which is a measuring device", "a watch, which is a timepiece, timekeeper, horologe, which is a measuring device", "a ticker, which is a timepiece, timekeeper, horologe, which is a measuring device", "a timer, which is a timepiece, timekeeper, horologe, which is a measuring device"]}, "384": {"node_name": "dishwasher, dish washer, dishwashing machine", "parent_names": ["home appliance", "household appliance"], "child_names": [], "candidate_sentences": ["a dishwasher, dish washer, dishwashing machine, which is a home appliance", "a dishwasher, dish washer, dishwashing machine, which is a household appliance"]}, "385": {"node_name": "armadillo", "parent_names": ["edentate"], "child_names": [], "candidate_sentences": ["a armadillo, which is a edentate"]}, "386": {"node_name": "ski", "parent_names": ["sports equipment"], "child_names": [], "candidate_sentences": ["a ski, which is a sports equipment"]}, "387": {"node_name": "mink", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a mink, which is a carnivore"]}, "388": {"node_name": "paintbrush", "parent_names": ["tool"], "child_names": [], "candidate_sentences": ["a paintbrush, which is a tool"]}, "389": {"node_name": "fig", "parent_names": ["fruit"], "child_names": [], "candidate_sentences": ["a fig, which is a fruit"]}, "390": {"node_name": "fox", "parent_names": ["carnivore"], "child_names": ["red fox", "Vulpes vulpes", "kit fox", "<PERSON><PERSON><PERSON> macrotis", "Arctic fox", "white fox", "Alopex lagopus", "grey fox", "gray fox", "<PERSON><PERSON>cy<PERSON> cinereoargenteus"], "candidate_sentences": ["a red fox, which is a fox, which is a carnivore", "a Vulpes vulpes, which is a fox, which is a carnivore", "a kit fox, which is a fox, which is a carnivore", "a Vulpes macrotis, which is a fox, which is a carnivore", "a Arctic fox, which is a fox, which is a carnivore", "a white fox, which is a fox, which is a carnivore", "a Alopex lagopus, which is a fox, which is a carnivore", "a grey fox, which is a fox, which is a carnivore", "a gray fox, which is a fox, which is a carnivore", "a Urocyon cinereoargenteus, which is a fox, which is a carnivore"]}, "391": {"node_name": "dummy46", "parent_names": ["building", "edifice"], "child_names": ["planetarium"], "candidate_sentences": ["a planetarium, which is a dummy46, which is a building", "a planetarium, which is a dummy46, which is a edifice"]}, "392": {"node_name": "elephant", "parent_names": ["proboscidean", "proboscidian"], "child_names": ["Indian elephant", "<PERSON><PERSON><PERSON> maximus", "African elephant", "Loxodonta africana"], "candidate_sentences": ["a Indian elephant, which is a elephant, which is a proboscidean", "a Elephas maximus, which is a elephant, which is a proboscidean", "a African elephant, which is a elephant, which is a proboscidean", "a Loxodonta africana, which is a elephant, which is a proboscidean", "a Indian elephant, which is a elephant, which is a proboscidian", "a Elephas maximus, which is a elephant, which is a proboscidian", "a African elephant, which is a elephant, which is a proboscidian", "a Loxodonta africana, which is a elephant, which is a proboscidian"]}, "393": {"node_name": "skirt", "parent_names": ["garment"], "child_names": ["hoopskirt", "crinoline", "miniskirt", "mini", "overskirt", "sarong"], "candidate_sentences": ["a hoopskirt, which is a skirt, which is a garment", "a crinoline, which is a skirt, which is a garment", "a miniskirt, which is a skirt, which is a garment", "a mini, which is a skirt, which is a garment", "a overskirt, which is a skirt, which is a garment", "a sarong, which is a skirt, which is a garment"]}, "394": {"node_name": "cuckoo", "parent_names": ["cuculiform bird"], "child_names": ["coucal"], "candidate_sentences": ["a coucal, which is a cuckoo, which is a cuculiform bird"]}, "395": {"node_name": "barometer", "parent_names": ["measuring instrument", "measuring system", "measuring device"], "child_names": [], "candidate_sentences": ["a barometer, which is a measuring instrument", "a barometer, which is a measuring system", "a barometer, which is a measuring device"]}, "396": {"node_name": "puffer, pufferfish, blowfish, globefish", "parent_names": ["bony fish"], "child_names": [], "candidate_sentences": ["a puffer, pufferfish, blowfish, globefish, which is a bony fish"]}, "397": {"node_name": "lynx, catamount", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a lynx, catamount, which is a carnivore"]}, "398": {"node_name": "pool table, billiard table, snooker table", "parent_names": ["table"], "child_names": [], "candidate_sentences": ["a pool table, billiard table, snooker table, which is a table"]}, "399": {"node_name": "dummy45", "parent_names": ["building", "edifice"], "child_names": ["library"], "candidate_sentences": ["a library, which is a dummy45, which is a building", "a library, which is a dummy45, which is a edifice"]}, "400": {"node_name": "patio, terrace", "parent_names": ["area"], "child_names": [], "candidate_sentences": ["a patio, terrace, which is a area"]}, "401": {"node_name": "shoji", "parent_names": ["screen"], "child_names": [], "candidate_sentences": ["a shoji, which is a screen"]}, "402": {"node_name": "jay", "parent_names": ["passerine", "passeriform bird"], "child_names": [], "candidate_sentences": ["a jay, which is a passerine", "a jay, which is a passeriform bird"]}, "403": {"node_name": "shark", "parent_names": ["cartilaginous fish", "chondrichthian"], "child_names": ["great white shark", "white shark", "man-eater", "man-eating shark", "Carcharodon carcharias", "tiger shark", "<PERSON><PERSON><PERSON><PERSON>", "hammerhead", "hammerhead shark"], "candidate_sentences": ["a great white shark, which is a shark, which is a cartilaginous fish", "a white shark, which is a shark, which is a cartilaginous fish", "a man-eater, which is a shark, which is a cartilaginous fish", "a man-eating shark, which is a shark, which is a cartilaginous fish", "a Carcharodon carcharias, which is a shark, which is a cartilaginous fish", "a tiger shark, which is a shark, which is a cartilaginous fish", "a Galeocerdo cuvieri, which is a shark, which is a cartilaginous fish", "a hammerhead, which is a shark, which is a cartilaginous fish", "a hammerhead shark, which is a shark, which is a cartilaginous fish", "a great white shark, which is a shark, which is a chondrichthian", "a white shark, which is a shark, which is a chondrichthian", "a man-eater, which is a shark, which is a chondrichthian", "a man-eating shark, which is a shark, which is a chondrichthian", "a Carcharodon carcharias, which is a shark, which is a chondrichthian", "a tiger shark, which is a shark, which is a chondrichthian", "a Galeocerdo cuvieri, which is a shark, which is a chondrichthian", "a hammerhead, which is a shark, which is a chondrichthian", "a hammerhead shark, which is a shark, which is a chondrichthian"]}, "404": {"node_name": "clog, geta, patten, sabot", "parent_names": ["footwear", "legwear"], "child_names": [], "candidate_sentences": ["a clog, geta, patten, sabot, which is a footwear", "a clog, geta, patten, sabot, which is a legwear"]}, "405": {"node_name": "oystercatcher, oyster catcher", "parent_names": ["aquatic bird"], "child_names": [], "candidate_sentences": ["a oystercatcher, oyster catcher, which is a aquatic bird"]}, "406": {"node_name": "dogsled, dog sled, dog sleigh", "parent_names": ["sled", "sledge", "sleigh"], "child_names": [], "candidate_sentences": ["a dogsled, dog sled, dog sleigh, which is a sled", "a dogsled, dog sled, dog sleigh, which is a sledge", "a dogsled, dog sled, dog sleigh, which is a sleigh"]}, "407": {"node_name": "plow, plough", "parent_names": ["tool"], "child_names": [], "candidate_sentences": ["a plow, plough, which is a tool"]}, "408": {"node_name": "cicada, cicala", "parent_names": ["insect"], "child_names": [], "candidate_sentences": ["a cicada, cicala, which is a insect"]}, "409": {"node_name": "sturgeon", "parent_names": ["bony fish"], "child_names": [], "candidate_sentences": ["a sturgeon, which is a bony fish"]}, "410": {"node_name": "chest of drawers, chest, bureau, dresser", "parent_names": ["wall unit"], "child_names": ["chiffonier", "commode"], "candidate_sentences": ["a chiffonier, which is a chest of drawers, chest, bureau, dresser, which is a wall unit", "a commode, which is a chest of drawers, chest, bureau, dresser, which is a wall unit"]}, "411": {"node_name": "cucumber, cuke", "parent_names": ["vegetable", "veggie", "veg"], "child_names": [], "candidate_sentences": ["a cucumber, cuke, which is a vegetable", "a cucumber, cuke, which is a veggie", "a cucumber, cuke, which is a veg"]}, "412": {"node_name": "passenger train", "parent_names": ["train", "railroad train"], "child_names": ["bullet train", "bullet"], "candidate_sentences": ["a bullet train, which is a passenger train, which is a train", "a bullet, which is a passenger train, which is a train", "a bullet train, which is a passenger train, which is a railroad train", "a bullet, which is a passenger train, which is a railroad train"]}, "413": {"node_name": "bear", "parent_names": ["carnivore"], "child_names": ["brown bear", "bruin", "Ursus arctos", "American black bear", "black bear", "Ursus americanus", "<PERSON><PERSON><PERSON><PERSON> americanus", "ice bear", "polar bear", "Ursus <PERSON>mus", "<PERSON><PERSON><PERSON><PERSON> maritimus", "sloth bear", "<PERSON><PERSON><PERSON> ursinus", "Ursus ursinus"], "candidate_sentences": ["a brown bear, which is a bear, which is a carnivore", "a bruin, which is a bear, which is a carnivore", "a Ursus arctos, which is a bear, which is a carnivore", "a American black bear, which is a bear, which is a carnivore", "a black bear, which is a bear, which is a carnivore", "a Ursus americanus, which is a bear, which is a carnivore", "a Euarctos americanus, which is a bear, which is a carnivore", "a ice bear, which is a bear, which is a carnivore", "a polar bear, which is a bear, which is a carnivore", "a Ursus Maritimus, which is a bear, which is a carnivore", "a Thalarctos maritimus, which is a bear, which is a carnivore", "a sloth bear, which is a bear, which is a carnivore", "a Melursus ursinus, which is a bear, which is a carnivore", "a Ursus ursinus, which is a bear, which is a carnivore"]}, "414": {"node_name": "bell cote, bell cot", "parent_names": ["area"], "child_names": [], "candidate_sentences": ["a bell cote, bell cot, which is a area"]}, "415": {"node_name": "vulture", "parent_names": ["bird of prey", "raptor", "raptorial bird"], "child_names": [], "candidate_sentences": ["a vulture, which is a bird of prey", "a vulture, which is a raptor", "a vulture, which is a raptorial bird"]}, "416": {"node_name": "cattle, cows, kine, oxen, Bos taurus", "parent_names": ["ungulate", "hoofed mammal"], "child_names": ["ox"], "candidate_sentences": ["a ox, which is a cattle, cows, kine, oxen, Bos taurus, which is a ungulate", "a ox, which is a cattle, cows, kine, oxen, Bos taurus, which is a hoofed mammal"]}, "417": {"node_name": "dam, dike, dyke", "parent_names": ["barrier"], "child_names": [], "candidate_sentences": ["a dam, dike, dyke, which is a barrier"]}, "418": {"node_name": "sheep", "parent_names": ["ungulate", "hoofed mammal"], "child_names": ["ram", "tup"], "candidate_sentences": ["a ram, which is a sheep, which is a ungulate", "a tup, which is a sheep, which is a ungulate", "a ram, which is a sheep, which is a hoofed mammal", "a tup, which is a sheep, which is a hoofed mammal"]}, "419": {"node_name": "porcupine, hedgehog", "parent_names": ["rodent", "gnawer"], "child_names": [], "candidate_sentences": ["a porcupine, hedgehog, which is a rodent", "a porcupine, hedgehog, which is a gnawer"]}, "420": {"node_name": "turtle", "parent_names": ["chelonian", "chelonian reptile"], "child_names": ["mud turtle", "terrapin", "box turtle", "box tortoise", "sea turtle", "marine turtle"], "candidate_sentences": ["a mud turtle, which is a turtle, which is a chelonian", "a terrapin, which is a turtle, which is a chelonian", "a box turtle, which is a turtle, which is a chelonian", "a box tortoise, which is a turtle, which is a chelonian", "a sea turtle, which is a turtle, which is a chelonian", "a marine turtle, which is a turtle, which is a chelonian", "a mud turtle, which is a turtle, which is a chelonian reptile", "a terrapin, which is a turtle, which is a chelonian reptile", "a box turtle, which is a turtle, which is a chelonian reptile", "a box tortoise, which is a turtle, which is a chelonian reptile", "a sea turtle, which is a turtle, which is a chelonian reptile", "a marine turtle, which is a turtle, which is a chelonian reptile"]}, "421": {"node_name": "dip", "parent_names": ["condiment"], "child_names": ["guacamole"], "candidate_sentences": ["a guacamole, which is a dip, which is a condiment"]}, "422": {"node_name": "Old World buffalo, buffalo", "parent_names": ["ungulate", "hoofed mammal"], "child_names": ["water buffalo", "water ox", "Asiatic buffalo", "<PERSON><PERSON><PERSON> bubalis"], "candidate_sentences": ["a water buffalo, which is a Old World buffalo, buffalo, which is a ungulate", "a water ox, which is a Old World buffalo, buffalo, which is a ungulate", "a Asiatic buffalo, which is a Old World buffalo, buffalo, which is a ungulate", "a Bubalus bubalis, which is a Old World buffalo, buffalo, which is a ungulate", "a water buffalo, which is a Old World buffalo, buffalo, which is a hoofed mammal", "a water ox, which is a Old World buffalo, buffalo, which is a hoofed mammal", "a Asiatic buffalo, which is a Old World buffalo, buffalo, which is a hoofed mammal", "a Bubalus bubalis, which is a Old World buffalo, buffalo, which is a hoofed mammal"]}, "423": {"node_name": "penguin", "parent_names": ["aquatic bird"], "child_names": ["king penguin", "Aptenodytes patagonica"], "candidate_sentences": ["a king penguin, which is a penguin, which is a aquatic bird", "a Aptenodytes patagonica, which is a penguin, which is a aquatic bird"]}, "424": {"node_name": "bag", "parent_names": ["bag"], "child_names": ["backpack", "back pack", "knapsack", "packsack", "rucksack", "haversack", "mailbag", "postbag", "plastic bag", "purse"], "candidate_sentences": ["a backpack, which is a bag, which is a bag", "a back pack, which is a bag, which is a bag", "a knapsack, which is a bag, which is a bag", "a packsack, which is a bag, which is a bag", "a rucksack, which is a bag, which is a bag", "a haversack, which is a bag, which is a bag", "a mailbag, which is a bag, which is a bag", "a postbag, which is a bag, which is a bag", "a plastic bag, which is a bag, which is a bag", "a purse, which is a bag, which is a bag"]}, "425": {"node_name": "leopard, Panthera pardus", "parent_names": ["carnivore"], "child_names": [], "candidate_sentences": ["a leopard, <PERSON><PERSON> pardus, which is a carnivore"]}, "426": {"node_name": "volcano", "parent_names": ["mountain", "mount"], "child_names": [], "candidate_sentences": ["a volcano, which is a mountain", "a volcano, which is a mount"]}, "427": {"node_name": "clip", "parent_names": ["headdress", "headgear"], "child_names": ["hair slide"], "candidate_sentences": ["a hair slide, which is a clip, which is a headdress", "a hair slide, which is a clip, which is a headgear"]}, "428": {"node_name": "telescope, scope", "parent_names": ["scientific instrument"], "child_names": ["radio telescope", "radio reflector"], "candidate_sentences": ["a radio telescope, which is a telescope, scope, which is a scientific instrument", "a radio reflector, which is a telescope, scope, which is a scientific instrument"]}, "429": {"node_name": "adhesive bandage", "parent_names": ["bandage", "patch"], "child_names": ["Band Aid"], "candidate_sentences": ["a Band Aid, which is a adhesive bandage, which is a bandage", "a Band Aid, which is a adhesive bandage, which is a patch"]}, "430": {"node_name": "maze, labyrinth", "parent_names": ["dummy74"], "child_names": [], "candidate_sentences": ["a maze, labyrinth, which is a dummy74"]}, "431": {"node_name": "hornbill", "parent_names": ["coraciiform bird"], "child_names": [], "candidate_sentences": ["a hornbill, which is a coraciiform bird"]}, "432": {"node_name": "dog, domestic dog, Canis familiaris", "parent_names": ["carnivore"], "child_names": ["Chihuahua", "Japanese spaniel", "Maltese dog", "Maltese terrier", "Maltese", "Pekinese", "Pekingese", "<PERSON><PERSON><PERSON>", "Shih-<PERSON><PERSON>", "toy terrier", "Rhodesian ridgeback", "Afghan hound", "Afghan", "basset", "basset hound", "beagle", "bloodhound", "sleuthhound", "bluetick", "black-and-tan coonhound", "<PERSON> hound", "Walker foxhound", "English foxhound", "redbone", "bor<PERSON>i", "Russian wolfhound", "Irish wolfhound", "Italian greyhound", "whippet", "Ibizan hound", "Ibizan <PERSON>", "Norwegian elkhound", "elkhound", "otterhound", "otter hound", "Saluki", "gazelle hound", "Scottish deerhound", "deerhound", "Weimaraner", "Bedlington terrier", "Border terrier", "Kerry blue terrier", "Irish terrier", "Norfolk terrier", "Norwich terrier", "Yorkshire terrier", "Airedale", "Airedale terrier", "cairn", "cairn terrier", "Australian terrier", "<PERSON><PERSON>", "Dandie <PERSON> terrier", "Boston bull", "Boston terrier", "Scotch terrier", "Scottish terrier", "<PERSON><PERSON>", "Tibetan terrier", "chrysanthemum dog", "silky terrier", "Sydney silky", "soft-coated wheaten terrier", "West Highland white terrier", "Lhasa", "Lhasa apso", "flat-coated retriever", "curly-coated retriever", "golden retriever", "Labrador retriever", "Chesapeake Bay retriever", "German short-haired pointer", "vizsla", "Hungarian pointer", "English setter", "Irish setter", "red setter", "<PERSON> setter", "Brittany spaniel", "clumber", "clumber spaniel", "cocker spaniel", "English cocker spaniel", "cocker", "Sussex spaniel", "kuvasz", "s<PERSON><PERSON><PERSON>", "briard", "kelpie", "komondor", "Old English sheepdog", "bobtail", "Shetland sheepdog", "Shetland sheep dog", "Shetland", "collie", "Border collie", "Bouvier des Flandres", "Bouviers des Flandres", "Rottweiler", "German shepherd", "German shepherd dog", "German police dog", "alsatian", "<PERSON><PERSON>", "<PERSON>berman pinscher", "miniature pinscher", "Greater Swiss Mountain dog", "Bernese mountain dog", "<PERSON><PERSON><PERSON><PERSON>", "EntleBucher", "boxer", "bull mastiff", "Great Dane", "<PERSON>", "St Bernard", "Eskimo dog", "husky", "malamute", "malemute", "Alaskan malamute", "Siberian husky", "da<PERSON><PERSON>", "coach dog", "carriage dog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monkey pinscher", "monkey dog", "basenji", "pug", "pug-dog", "<PERSON><PERSON>", "Newfoundland", "Newfoundland dog", "Great Pyrenees", "<PERSON>oyed", "<PERSON><PERSON><PERSON>", "Pomeranian", "chow", "chow chow", "keeshond", "Mexican hairless", "schna<PERSON><PERSON>", "bulldog", "English bulldog", "springer spaniel", "springer", "griffon", "Brussels griffon", "Belgian griffon", "fox terrier", "wirehair", "wirehaired terrier", "wire-haired terrier", "Belgian sheepdog", "Belgian shepherd", "toy spaniel", "corgi", "Welsh corgi", "water spaniel", "poodle", "poodle dog", "bullterrier", "bull terrier", "mastiff"], "candidate_sentences": ["a Chihuahua, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Japanese spaniel, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Maltese dog, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a Maltese terrier, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a Maltese, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a Pekinese, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Pekingese, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Peke, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Shih-Tzu, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a toy terrier, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a Rhodesian ridgeback, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a Afghan hound, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Afghan, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a basset, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a basset hound, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a beagle, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a bloodhound, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a sleuthhound, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a bluetick, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a black-and-tan coonhound, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a Walker hound, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Walker foxhound, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a English foxhound, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a redbone, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a borzoi, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Russian wolfhound, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Irish wolfhound, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Italian greyhound, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a whippet, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Ibizan hound, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a Ibizan Podenco, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a Norwegian elkhound, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a elkhound, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a otterhound, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a otter hound, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Saluki, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a gazelle hound, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Scottish deerhound, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a deerhound, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Weimaraner, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Bedlington terrier, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Border terrier, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Kerry blue terrier, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a Irish terrier, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Norfolk terrier, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Norwich terrier, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Yorkshire terrier, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Airedale, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Airedale terrier, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a cairn, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a cairn terrier, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a Australian terrier, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Dandie <PERSON>, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Dandie <PERSON> terrier, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a Boston bull, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Boston terrier, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Scotch terrier, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Scottish terrier, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Scottie, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Tibetan terrier, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a chrysanthemum dog, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a silky terrier, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a Sydney silky, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a soft-coated wheaten terrier, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a West Highland white terrier, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Lhasa, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Lhasa apso, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a flat-coated retriever, which is a dog, domestic dog, <PERSON><PERSON> familiaris, which is a carnivore", "a curly-coated retriever, which is a dog, domestic dog, <PERSON><PERSON> familiaris, which is a carnivore", "a golden retriever, which is a dog, domestic dog, <PERSON><PERSON> familiaris, which is a carnivore", "a Labrador retriever, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a Chesapeake Bay retriever, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a German short-haired pointer, which is a dog, domestic dog, <PERSON><PERSON> familiaris, which is a carnivore", "a vizsla, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Hungarian pointer, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a English setter, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Irish setter, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a red setter, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a Gordon setter, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a Brittany spaniel, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a clumber, which is a dog, domestic dog, <PERSON><PERSON> familiaris, which is a carnivore", "a clumber spaniel, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a cocker spaniel, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a English cocker spaniel, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a cocker, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Sussex spaniel, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a kuvasz, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a schipperke, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a briard, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a kelpie, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a komondor, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Old English sheepdog, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a bobtail, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Shetland sheepdog, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Shetland sheep dog, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Shetland, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a collie, which is a dog, domestic dog, <PERSON><PERSON> familiaris, which is a carnivore", "a Border collie, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a Bouvier des Flandres, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Bouviers des Flandres, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Rottweiler, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a German shepherd, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a German shepherd dog, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a German police dog, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a alsatian, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Doberman, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Doberman pinscher, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a miniature pinscher, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a Greater Swiss Mountain dog, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Bernese mountain dog, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Appenzeller, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a EntleBucher, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a boxer, which is a dog, domestic dog, <PERSON><PERSON> familiaris, which is a carnivore", "a bull mastiff, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Great Dane, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Saint Bernard, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a St Bernard, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Eskimo dog, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a husky, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a malamute, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a malemute, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Alaskan malamute, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Siberian husky, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a dalmatian, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a coach dog, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a carriage dog, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a affenpinscher, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a monkey pinscher, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a monkey dog, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a basenji, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a pug, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a pug-dog, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Leonberg, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Newfoundland, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Newfoundland dog, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Great Pyrenees, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Samoyed, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Samoyede, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Pomeranian, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a chow, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a chow chow, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a keeshond, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Mexican hairless, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a schnauzer, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a bulldog, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a English bulldog, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a springer spaniel, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a springer, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a griffon, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Brussels griffon, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Belgian griffon, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a fox terrier, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a wirehair, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a wirehaired terrier, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a wire-haired terrier, which is a dog, domestic dog, <PERSON><PERSON> familiaris, which is a carnivore", "a Belgian sheepdog, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Belgian shepherd, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a toy spaniel, which is a dog, domestic dog, <PERSON>is familiaris, which is a carnivore", "a corgi, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a Welsh corgi, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a water spaniel, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a poodle, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a poodle dog, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a bullterrier, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a bull terrier, which is a dog, domestic dog, Canis familiaris, which is a carnivore", "a mastiff, which is a dog, domestic dog, Canis familiaris, which is a carnivore"]}, "433": {"node_name": "apple", "parent_names": ["fruit"], "child_names": ["eating apple", "dessert apple"], "candidate_sentences": ["a eating apple, which is a apple, which is a fruit", "a dessert apple, which is a apple, which is a fruit"]}, "434": {"node_name": "eel", "parent_names": ["bony fish"], "child_names": [], "candidate_sentences": ["a eel, which is a bony fish"]}, "435": {"node_name": "truck, motortruck", "parent_names": ["motor vehicle", "automotive vehicle"], "child_names": ["fire engine", "fire truck", "forklift", "garbage truck", "dustcart", "moving van", "pickup", "pickup truck", "snowplow", "snowplough", "tow truck", "tow car", "wrecker", "tractor", "trailer truck", "tractor trailer", "trucking rig", "rig", "articulated lorry", "semi"], "candidate_sentences": ["a fire engine, which is a truck, motortruck, which is a motor vehicle", "a fire truck, which is a truck, motortruck, which is a motor vehicle", "a forklift, which is a truck, motortruck, which is a motor vehicle", "a garbage truck, which is a truck, motortruck, which is a motor vehicle", "a dustcart, which is a truck, motortruck, which is a motor vehicle", "a moving van, which is a truck, motortruck, which is a motor vehicle", "a pickup, which is a truck, motortruck, which is a motor vehicle", "a pickup truck, which is a truck, motortruck, which is a motor vehicle", "a snowplow, which is a truck, motortruck, which is a motor vehicle", "a snowplough, which is a truck, motortruck, which is a motor vehicle", "a tow truck, which is a truck, motortruck, which is a motor vehicle", "a tow car, which is a truck, motortruck, which is a motor vehicle", "a wrecker, which is a truck, motortruck, which is a motor vehicle", "a tractor, which is a truck, motortruck, which is a motor vehicle", "a trailer truck, which is a truck, motortruck, which is a motor vehicle", "a tractor trailer, which is a truck, motortruck, which is a motor vehicle", "a trucking rig, which is a truck, motortruck, which is a motor vehicle", "a rig, which is a truck, motortruck, which is a motor vehicle", "a articulated lorry, which is a truck, motortruck, which is a motor vehicle", "a semi, which is a truck, motortruck, which is a motor vehicle", "a fire engine, which is a truck, motortruck, which is a automotive vehicle", "a fire truck, which is a truck, motortruck, which is a automotive vehicle", "a forklift, which is a truck, motortruck, which is a automotive vehicle", "a garbage truck, which is a truck, motortruck, which is a automotive vehicle", "a dustcart, which is a truck, motortruck, which is a automotive vehicle", "a moving van, which is a truck, motortruck, which is a automotive vehicle", "a pickup, which is a truck, motortruck, which is a automotive vehicle", "a pickup truck, which is a truck, motortruck, which is a automotive vehicle", "a snowplow, which is a truck, motortruck, which is a automotive vehicle", "a snowplough, which is a truck, motortruck, which is a automotive vehicle", "a tow truck, which is a truck, motortruck, which is a automotive vehicle", "a tow car, which is a truck, motortruck, which is a automotive vehicle", "a wrecker, which is a truck, motortruck, which is a automotive vehicle", "a tractor, which is a truck, motortruck, which is a automotive vehicle", "a trailer truck, which is a truck, motortruck, which is a automotive vehicle", "a tractor trailer, which is a truck, motortruck, which is a automotive vehicle", "a trucking rig, which is a truck, motortruck, which is a automotive vehicle", "a rig, which is a truck, motortruck, which is a automotive vehicle", "a articulated lorry, which is a truck, motortruck, which is a automotive vehicle", "a semi, which is a truck, motortruck, which is a automotive vehicle"]}, "436": {"node_name": "snail", "parent_names": ["gastropod", "univalve"], "child_names": [], "candidate_sentences": ["a snail, which is a gastropod", "a snail, which is a univalve"]}, "437": {"node_name": "starfish, sea star", "parent_names": ["dummy11"], "child_names": [], "candidate_sentences": ["a starfish, sea star, which is a dummy11"]}, "438": {"node_name": "gallinule, marsh hen, water hen, swamphen", "parent_names": ["aquatic bird"], "child_names": ["purple gallinule"], "candidate_sentences": ["a purple gallinule, which is a gallinule, marsh hen, water hen, swamphen, which is a aquatic bird"]}, "439": {"node_name": "chest", "parent_names": ["dummy79"], "child_names": [], "candidate_sentences": ["a chest, which is a dummy79"]}, "440": {"node_name": "shoe", "parent_names": ["footwear", "legwear"], "child_names": ["Loafer", "running shoe", "sandal"], "candidate_sentences": ["a Loafer, which is a shoe, which is a footwear", "a running shoe, which is a shoe, which is a footwear", "a sandal, which is a shoe, which is a footwear", "a Loafer, which is a shoe, which is a legwear", "a running shoe, which is a shoe, which is a legwear", "a sandal, which is a shoe, which is a legwear"]}, "441": {"node_name": "<PERSON><PERSON>, Aramus pictus", "parent_names": ["aquatic bird"], "child_names": [], "candidate_sentences": ["a limpkin, <PERSON>mus pictus, which is a aquatic bird"]}, "442": {"node_name": "locomotive, engine, locomotive engine, railway locomotive", "parent_names": ["train", "railroad train"], "child_names": ["electric locomotive", "steam locomotive"], "candidate_sentences": ["a electric locomotive, which is a locomotive, engine, locomotive engine, railway locomotive, which is a train", "a steam locomotive, which is a locomotive, engine, locomotive engine, railway locomotive, which is a train", "a electric locomotive, which is a locomotive, engine, locomotive engine, railway locomotive, which is a railroad train", "a steam locomotive, which is a locomotive, engine, locomotive engine, railway locomotive, which is a railroad train"]}, "443": {"node_name": "bookcase", "parent_names": ["wall unit"], "child_names": [], "candidate_sentences": ["a bookcase, which is a wall unit"]}, "444": {"node_name": "handcart, pushcart, cart, go-cart", "parent_names": ["cart"], "child_names": ["barrow", "garden cart", "lawn cart", "wheelbarrow", "shopping cart"], "candidate_sentences": ["a barrow, which is a handcart, pushcart, cart, go-cart, which is a cart", "a garden cart, which is a handcart, pushcart, cart, go-cart, which is a cart", "a lawn cart, which is a handcart, pushcart, cart, go-cart, which is a cart", "a wheelbarrow, which is a handcart, pushcart, cart, go-cart, which is a cart", "a shopping cart, which is a handcart, pushcart, cart, go-cart, which is a cart"]}, "445": {"node_name": "corn", "parent_names": ["fruit"], "child_names": [], "candidate_sentences": ["a corn, which is a fruit"]}, "446": {"node_name": "scorpaenid, scorpaenid fish", "parent_names": ["bony fish"], "child_names": ["lionfish"], "candidate_sentences": ["a lionfish, which is a scorpaenid, scorpaenid fish, which is a bony fish"]}, "447": {"node_name": "gate", "parent_names": ["barrier"], "child_names": ["turnstile"], "candidate_sentences": ["a turnstile, which is a gate, which is a barrier"]}, "448": {"node_name": "sea slug, nudibranch", "parent_names": ["gastropod", "univalve"], "child_names": [], "candidate_sentences": ["a sea slug, nudibranch, which is a gastropod", "a sea slug, nudibranch, which is a univalve"]}, "449": {"node_name": "coat", "parent_names": ["garment"], "child_names": ["a<PERSON>a", "academic gown", "academic robe", "judge's robe", "cloak", "fur coat", "kimono", "lab coat", "laboratory coat", "poncho", "vestment", "raincoat", "waterproof"], "candidate_sentences": ["a abaya, which is a coat, which is a garment", "a academic gown, which is a coat, which is a garment", "a academic robe, which is a coat, which is a garment", "a judge's robe, which is a coat, which is a garment", "a cloak, which is a coat, which is a garment", "a fur coat, which is a coat, which is a garment", "a kimono, which is a coat, which is a garment", "a lab coat, which is a coat, which is a garment", "a laboratory coat, which is a coat, which is a garment", "a poncho, which is a coat, which is a garment", "a vestment, which is a coat, which is a garment", "a raincoat, which is a coat, which is a garment", "a waterproof, which is a coat, which is a garment"]}, "450": {"node_name": "measuring cup", "parent_names": ["kitchen utensil"], "child_names": [], "candidate_sentences": ["a measuring cup, which is a kitchen utensil"]}, "451": {"node_name": "buckeye, horse chestnut, conker", "parent_names": ["fruit"], "child_names": [], "candidate_sentences": ["a buckeye, horse chestnut, conker, which is a fruit"]}, "452": {"node_name": "crayfish, crawfish, crawdad, crawdaddy", "parent_names": ["crustacean"], "child_names": [], "candidate_sentences": ["a crayfish, crawfish, crawdad, crawdaddy, which is a crustacean"]}, "453": {"node_name": "stinkhorn, carrion fungus", "parent_names": ["basidiomycete", "basidiomycetous fungi"], "child_names": [], "candidate_sentences": ["a stinkhorn, carrion fungus, which is a basidiomycete", "a stinkhorn, carrion fungus, which is a basidiomycetous fungi"]}, "454": {"node_name": "acorn", "parent_names": ["fruit"], "child_names": [], "candidate_sentences": ["a acorn, which is a fruit"]}, "455": {"node_name": "bee", "parent_names": ["insect"], "child_names": [], "candidate_sentences": ["a bee, which is a insect"]}, "456": {"node_name": "tank, army tank, armored combat vehicle, armoured combat vehicle", "parent_names": ["tracked vehicle"], "child_names": [], "candidate_sentences": ["a tank, army tank, armored combat vehicle, armoured combat vehicle, which is a tracked vehicle"]}, "457": {"node_name": "stork", "parent_names": ["aquatic bird"], "child_names": ["white stork", "Ciconia ciconia", "black stork", "Ciconia nigra"], "candidate_sentences": ["a white stork, which is a stork, which is a aquatic bird", "a Ciconia ciconia, which is a stork, which is a aquatic bird", "a black stork, which is a stork, which is a aquatic bird", "a Ciconia nigra, which is a stork, which is a aquatic bird"]}, "458": {"node_name": "apron", "parent_names": ["garment"], "child_names": [], "candidate_sentences": ["a apron, which is a garment"]}, "459": {"node_name": "plover", "parent_names": ["aquatic bird"], "child_names": ["ruddy turnstone", "Arenaria interpres"], "candidate_sentences": ["a ruddy turnstone, which is a plover, which is a aquatic bird", "a Arenaria interpres, which is a plover, which is a aquatic bird"]}, "460": {"node_name": "ear, spike, capitulum", "parent_names": ["fruit"], "child_names": [], "candidate_sentences": ["a ear, spike, capitulum, which is a fruit"]}, "461": {"node_name": "nightingale, Luscinia megarhynchos", "parent_names": ["passerine", "passeriform bird"], "child_names": ["bulbul"], "candidate_sentences": ["a bulbul, which is a nightingale, Luscinia megarhynchos, which is a passerine", "a bulbul, which is a nightingale, Luscinia megarhynchos, which is a passeriform bird"]}, "462": {"node_name": "coral fungus", "parent_names": ["basidiomycete", "basidiomycetous fungi"], "child_names": [], "candidate_sentences": ["a coral fungus, which is a basidiomycete", "a coral fungus, which is a basidiomycetous fungi"]}, "463": {"node_name": "television, television system", "parent_names": ["electronic equipment"], "child_names": [], "candidate_sentences": ["a television, television system, which is a electronic equipment"]}, "464": {"node_name": "cream, ointment, emollient", "parent_names": ["cosmetic"], "child_names": ["lotion", "sunscreen", "sunblock", "sun blocker"], "candidate_sentences": ["a lotion, which is a cream, ointment, emollient, which is a cosmetic", "a sunscreen, which is a cream, ointment, emollient, which is a cosmetic", "a sunblock, which is a cream, ointment, emollient, which is a cosmetic", "a sun blocker, which is a cream, ointment, emollient, which is a cosmetic"]}, "465": {"node_name": "toaster", "parent_names": ["home appliance", "household appliance"], "child_names": [], "candidate_sentences": ["a toaster, which is a home appliance", "a toaster, which is a household appliance"]}}