{"0": {"node_name": "volleyball", "parent_names": ["ball"], "child_names": [], "candidate_sentences": ["a volleyball, which is a ball"]}, "1": {"node_name": "golfcart, golf cart", "parent_names": ["car", "auto", "automobile", "machine", "motorcar"], "child_names": [], "candidate_sentences": ["a golfcart, golf cart, which is a car", "a golfcart, golf cart, which is a auto", "a golfcart, golf cart, which is a automobile", "a golfcart, golf cart, which is a machine", "a golfcart, golf cart, which is a motorcar"]}, "2": {"node_name": "laptop, laptop computer", "parent_names": ["digital computer"], "child_names": [], "candidate_sentences": ["a laptop, laptop computer, which is a digital computer"]}, "3": {"node_name": "Afghan hound, Afghan", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Afghan hound, Afghan, which is a dog", "a Afghan hound, Afghan, which is a domestic dog", "a Afghan hound, Afghan, which is a Canis familiaris"]}, "4": {"node_name": "bull mastiff", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a bull mastiff, which is a dog", "a bull mastiff, which is a domestic dog", "a bull mastiff, which is a Canis familiaris"]}, "5": {"node_name": "academic gown, academic robe, judge's robe", "parent_names": ["coat"], "child_names": [], "candidate_sentences": ["a academic gown, academic robe, judge's robe, which is a coat"]}, "6": {"node_name": "butcher shop, meat market", "parent_names": ["mercantile establishment", "retail store", "sales outlet", "outlet"], "child_names": [], "candidate_sentences": ["a butcher shop, meat market, which is a mercantile establishment", "a butcher shop, meat market, which is a retail store", "a butcher shop, meat market, which is a sales outlet", "a butcher shop, meat market, which is a outlet"]}, "7": {"node_name": "trailer truck, tractor trailer, trucking rig, rig, articulated lorry, semi", "parent_names": ["truck", "motortruck"], "child_names": [], "candidate_sentences": ["a trailer truck, tractor trailer, trucking rig, rig, articulated lorry, semi, which is a truck", "a trailer truck, tractor trailer, trucking rig, rig, articulated lorry, semi, which is a motortruck"]}, "8": {"node_name": "Siamese cat, Siamese", "parent_names": ["domestic cat", "house cat", "Fe<PERSON> domesticus", "<PERSON><PERSON> catus"], "child_names": [], "candidate_sentences": ["a Siamese cat, Siamese, which is a domestic cat", "a Siamese cat, Siamese, which is a house cat", "a Siamese cat, Siamese, which is a Felis domesticus", "a Siamese cat, Siamese, which is a Felis catus"]}, "9": {"node_name": "raincoat, waterproof", "parent_names": ["coat"], "child_names": ["trench coat"], "candidate_sentences": ["a trench coat, which is a raincoat, waterproof, which is a coat"]}, "10": {"node_name": "schna<PERSON><PERSON>", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": ["miniature schnauzer", "giant schnauzer", "standard schnauzer"], "candidate_sentences": ["a miniature schnauzer, which is a schnauzer, which is a dog", "a giant schnauzer, which is a schnauzer, which is a dog", "a standard schnauzer, which is a schnauzer, which is a dog", "a miniature schnauzer, which is a schnauzer, which is a domestic dog", "a giant schnauzer, which is a schnauzer, which is a domestic dog", "a standard schnauzer, which is a schnauzer, which is a domestic dog", "a miniature schnauzer, which is a schnauzer, which is a Canis familiaris", "a giant schnauzer, which is a schnauzer, which is a Canis familiaris", "a standard schnauzer, which is a schnauzer, which is a Canis familiaris"]}, "11": {"node_name": "tailed frog, bell toad, ribbed toad, tailed toad, Ascaphus trui", "parent_names": ["frog", "toad", "toad frog", "<PERSON><PERSON>n", "batrachian", "salientian"], "child_names": [], "candidate_sentences": ["a tailed frog, bell toad, ribbed toad, tailed toad, Ascaphus trui, which is a frog", "a tailed frog, bell toad, ribbed toad, tailed toad, Ascaphus trui, which is a toad", "a tailed frog, bell toad, ribbed toad, tailed toad, Ascaphus trui, which is a toad frog", "a tailed frog, bell toad, ribbed toad, tailed toad, Ascaphus trui, which is a anuran", "a tailed frog, bell toad, ribbed toad, tailed toad, Ascaphus trui, which is a batrachian", "a tailed frog, bell toad, ribbed toad, tailed toad, Ascaphus trui, which is a salientian"]}, "12": {"node_name": "ground beetle, carabid beetle", "parent_names": ["beetle"], "child_names": [], "candidate_sentences": ["a ground beetle, carabid beetle, which is a beetle"]}, "13": {"node_name": "purple gallinule", "parent_names": ["gallinule", "marsh hen", "water hen", "swamphen"], "child_names": ["European gallinule", "<PERSON><PERSON><PERSON><PERSON> porphyrio"], "candidate_sentences": ["a European gallinule, which is a purple gallinule, which is a gallinule", "a Porphyrio porphyrio, which is a purple gallinule, which is a gallinule", "a European gallinule, which is a purple gallinule, which is a marsh hen", "a Porphyrio porphyrio, which is a purple gallinule, which is a marsh hen", "a European gallinule, which is a purple gallinule, which is a water hen", "a Porphyrio porphyrio, which is a purple gallinule, which is a water hen", "a European gallinule, which is a purple gallinule, which is a swamphen", "a Porphyrio porphyrio, which is a purple gallinule, which is a swamphen"]}, "14": {"node_name": "tiger shark, <PERSON><PERSON><PERSON><PERSON> cu<PERSON>i", "parent_names": ["shark"], "child_names": [], "candidate_sentences": ["a tiger shark, <PERSON><PERSON><PERSON><PERSON> cu<PERSON>i, which is a shark"]}, "15": {"node_name": "bassoon", "parent_names": ["wind instrument", "wind"], "child_names": [], "candidate_sentences": ["a bassoon, which is a wind instrument", "a bassoon, which is a wind"]}, "16": {"node_name": "hot pot, hotpot", "parent_names": ["stew"], "child_names": [], "candidate_sentences": ["a hot pot, hotpot, which is a stew"]}, "17": {"node_name": "pierid, pierid butterfly", "parent_names": ["butterfly"], "child_names": ["cabbage butterfly"], "candidate_sentences": ["a cabbage butterfly, which is a pierid, pierid butterfly, which is a butterfly"]}, "18": {"node_name": "racer, race car, racing car", "parent_names": ["car", "auto", "automobile", "machine", "motorcar"], "child_names": [], "candidate_sentences": ["a racer, race car, racing car, which is a car", "a racer, race car, racing car, which is a auto", "a racer, race car, racing car, which is a automobile", "a racer, race car, racing car, which is a machine", "a racer, race car, racing car, which is a motorcar"]}, "19": {"node_name": "chickadee", "parent_names": ["titmouse", "tit"], "child_names": [], "candidate_sentences": ["a chickadee, which is a titmouse", "a chickadee, which is a tit"]}, "20": {"node_name": "ram, tup", "parent_names": ["sheep"], "child_names": [], "candidate_sentences": ["a ram, tup, which is a sheep"]}, "21": {"node_name": "English foxhound", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a English foxhound, which is a dog", "a English foxhound, which is a domestic dog", "a English foxhound, which is a Canis familiaris"]}, "22": {"node_name": "motor scooter, scooter", "parent_names": ["motorcycle", "bike"], "child_names": [], "candidate_sentences": ["a motor scooter, scooter, which is a motorcycle", "a motor scooter, scooter, which is a bike"]}, "23": {"node_name": "cairn, cairn terrier", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a cairn, cairn terrier, which is a dog", "a cairn, cairn terrier, which is a domestic dog", "a cairn, cairn terrier, which is a Canis familiaris"]}, "24": {"node_name": "bulldog, English bulldog", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": ["French bulldog"], "candidate_sentences": ["a French bulldog, which is a bulldog, English bulldog, which is a dog", "a French bulldog, which is a bulldog, English bulldog, which is a domestic dog", "a French bulldog, which is a bulldog, English bulldog, which is a Canis familiaris"]}, "25": {"node_name": "cello, violoncello", "parent_names": ["stringed instrument"], "child_names": [], "candidate_sentences": ["a cello, violoncello, which is a stringed instrument"]}, "26": {"node_name": "Sussex spaniel", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Sussex spaniel, which is a dog", "a Sussex spaniel, which is a domestic dog", "a Sussex spaniel, which is a Canis familiaris"]}, "27": {"node_name": "barrow, garden cart, lawn cart, wheelbarrow", "parent_names": ["handcart", "pushcart", "cart", "go-cart"], "child_names": [], "candidate_sentences": ["a barrow, garden cart, lawn cart, wheelbarrow, which is a handcart", "a barrow, garden cart, lawn cart, wheelbarrow, which is a pushcart", "a barrow, garden cart, lawn cart, wheelbarrow, which is a cart", "a barrow, garden cart, lawn cart, wheelbarrow, which is a go-cart"]}, "28": {"node_name": "African crocodile, Nile crocodile, Crocodylus niloticus", "parent_names": ["crocodile"], "child_names": [], "candidate_sentences": ["a African crocodile, Nile crocodile, Crocodylus niloticus, which is a crocodile"]}, "29": {"node_name": "necklace", "parent_names": ["jewelry", "jewellery"], "child_names": [], "candidate_sentences": ["a necklace, which is a jewelry", "a necklace, which is a jewellery"]}, "30": {"node_name": "nymphalid, nymphalid butterfly, brush-footed butterfly, four-footed butterfly", "parent_names": ["butterfly"], "child_names": ["admiral"], "candidate_sentences": ["a admiral, which is a nymphalid, nymphalid butterfly, brush-footed butterfly, four-footed butterfly, which is a butterfly"]}, "31": {"node_name": "chow, chow chow", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a chow, chow chow, which is a dog", "a chow, chow chow, which is a domestic dog", "a chow, chow chow, which is a Canis familiaris"]}, "32": {"node_name": "Egyptian cat", "parent_names": ["domestic cat", "house cat", "Fe<PERSON> domesticus", "<PERSON><PERSON> catus"], "child_names": [], "candidate_sentences": ["a Egyptian cat, which is a domestic cat", "a Egyptian cat, which is a house cat", "a Egyptian cat, which is a Felis domesticus", "a Egyptian cat, which is a Felis catus"]}, "33": {"node_name": "true lobster", "parent_names": ["lobster"], "child_names": ["American lobster", "Northern lobster", "Maine lobster", "<PERSON><PERSON><PERSON> americanus"], "candidate_sentences": ["a American lobster, which is a true lobster, which is a lobster", "a Northern lobster, which is a true lobster, which is a lobster", "a Maine lobster, which is a true lobster, which is a lobster", "a Homarus americanus, which is a true lobster, which is a lobster"]}, "34": {"node_name": "toyshop", "parent_names": ["mercantile establishment", "retail store", "sales outlet", "outlet"], "child_names": [], "candidate_sentences": ["a toyshop, which is a mercantile establishment", "a toyshop, which is a retail store", "a toyshop, which is a sales outlet", "a toyshop, which is a outlet"]}, "35": {"node_name": "totem pole", "parent_names": ["column", "pillar"], "child_names": [], "candidate_sentences": ["a totem pole, which is a column", "a totem pole, which is a pillar"]}, "36": {"node_name": "quail", "parent_names": ["<PERSON><PERSON><PERSON><PERSON>"], "child_names": [], "candidate_sentences": ["a quail, which is a phasianid"]}, "37": {"node_name": "bonnet, poke bonnet", "parent_names": ["hat", "chapeau", "lid"], "child_names": [], "candidate_sentences": ["a bonnet, poke bonnet, which is a hat", "a bonnet, poke bonnet, which is a chapeau", "a bonnet, poke bonnet, which is a lid"]}, "38": {"node_name": "little blue heron, Egretta caerulea", "parent_names": ["heron"], "child_names": [], "candidate_sentences": ["a little blue heron, Egretta caerulea, which is a heron"]}, "39": {"node_name": "da<PERSON><PERSON>, coach dog, carriage dog", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a dalmatian, coach dog, carriage dog, which is a dog", "a dalmatian, coach dog, carriage dog, which is a domestic dog", "a dalmatian, coach dog, carriage dog, which is a Canis familiaris"]}, "40": {"node_name": "Labrador retriever", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Labrador retriever, which is a dog", "a Labrador retriever, which is a domestic dog", "a Labrador retriever, which is a Canis familiaris"]}, "41": {"node_name": "pay-phone, pay-station", "parent_names": ["telephone", "phone", "telephone set"], "child_names": [], "candidate_sentences": ["a pay-phone, pay-station, which is a telephone", "a pay-phone, pay-station, which is a phone", "a pay-phone, pay-station, which is a telephone set"]}, "42": {"node_name": "chameleon, chamaeleon", "parent_names": ["lizard"], "child_names": ["African chameleon", "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>"], "candidate_sentences": ["a African chameleon, which is a chameleon, chamaeleon, which is a lizard", "a Chamaeleo chamaeleon, which is a chameleon, chamaeleon, which is a lizard"]}, "43": {"node_name": "Christmas stocking", "parent_names": ["stocking"], "child_names": [], "candidate_sentences": ["a Christmas stocking, which is a stocking"]}, "44": {"node_name": "rifle", "parent_names": ["firearm", "piece", "small-arm"], "child_names": [], "candidate_sentences": ["a rifle, which is a firearm", "a rifle, which is a piece", "a rifle, which is a small-arm"]}, "45": {"node_name": "dugong, Dugong dugon", "parent_names": ["sea cow", "sirenian mammal", "sirenian"], "child_names": [], "candidate_sentences": ["a dugong, Dugong dugon, which is a sea cow", "a dugong, Dugong dugon, which is a sirenian mammal", "a dugong, <PERSON><PERSON> dugon, which is a sirenian"]}, "46": {"node_name": "rock beauty, Holocanthus tricolor", "parent_names": ["butterfly fish"], "child_names": [], "candidate_sentences": ["a rock beauty, Holocanthus tricolor, which is a butterfly fish"]}, "47": {"node_name": "jean, blue jean, denim", "parent_names": ["trouser", "pant"], "child_names": [], "candidate_sentences": ["a jean, blue jean, denim, which is a trouser", "a jean, blue jean, denim, which is a pant"]}, "48": {"node_name": "bloodhound, sleuthhound", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a bloodhound, sleuthhound, which is a dog", "a bloodhound, sleuthhound, which is a domestic dog", "a bloodhound, sleuthhound, which is a Canis familiaris"]}, "49": {"node_name": "<PERSON><PERSON>, <PERSON><PERSON> terrier", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Dandie <PERSON>, <PERSON><PERSON> terrier, which is a dog", "a Dandie <PERSON>, <PERSON><PERSON> terrier, which is a domestic dog", "a Dandie <PERSON>, <PERSON><PERSON> terrier, which is a Canis familiaris"]}, "50": {"node_name": "sailboat, sailing boat", "parent_names": ["boat"], "child_names": ["catamaran", "trimaran"], "candidate_sentences": ["a catamaran, which is a sailboat, sailing boat, which is a boat", "a trimaran, which is a sailboat, sailing boat, which is a boat"]}, "51": {"node_name": "tractor", "parent_names": ["truck", "motortruck"], "child_names": [], "candidate_sentences": ["a tractor, which is a truck", "a tractor, which is a motortruck"]}, "52": {"node_name": "china cabinet, china closet", "parent_names": ["cabinet"], "child_names": [], "candidate_sentences": ["a china cabinet, china closet, which is a cabinet"]}, "53": {"node_name": "wallaby, brush kangaroo", "parent_names": ["kangaroo"], "child_names": [], "candidate_sentences": ["a wallaby, brush kangaroo, which is a kangaroo"]}, "54": {"node_name": "Band Aid", "parent_names": ["adhesive bandage"], "child_names": [], "candidate_sentences": ["a Band Aid, which is a adhesive bandage"]}, "55": {"node_name": "combination lock", "parent_names": ["lock"], "child_names": [], "candidate_sentences": ["a combination lock, which is a lock"]}, "56": {"node_name": "steel arch bridge", "parent_names": ["bridge", "span"], "child_names": [], "candidate_sentences": ["a steel arch bridge, which is a bridge", "a steel arch bridge, which is a span"]}, "57": {"node_name": "maillot", "parent_names": ["tights", "leotards"], "child_names": [], "candidate_sentences": ["a maillot, which is a tights", "a maillot, which is a leotards"]}, "58": {"node_name": "barbell", "parent_names": ["weight", "free weight", "exercising weight"], "child_names": [], "candidate_sentences": ["a barbell, which is a weight", "a barbell, which is a free weight", "a barbell, which is a exercising weight"]}, "59": {"node_name": "hognose snake, puff adder, sand viper", "parent_names": ["snake", "serpent", "ophidian"], "child_names": [], "candidate_sentences": ["a hognose snake, puff adder, sand viper, which is a snake", "a hognose snake, puff adder, sand viper, which is a serpent", "a hognose snake, puff adder, sand viper, which is a ophidian"]}, "60": {"node_name": "tow truck, tow car, wrecker", "parent_names": ["truck", "motortruck"], "child_names": [], "candidate_sentences": ["a tow truck, tow car, wrecker, which is a truck", "a tow truck, tow car, wrecker, which is a motortruck"]}, "61": {"node_name": "green snake, grass snake", "parent_names": ["snake", "serpent", "ophidian"], "child_names": [], "candidate_sentences": ["a green snake, grass snake, which is a snake", "a green snake, grass snake, which is a serpent", "a green snake, grass snake, which is a ophidian"]}, "62": {"node_name": "mamba", "parent_names": ["snake", "serpent", "ophidian"], "child_names": ["black mamba", "Dendroasp<PERSON> augusticeps"], "candidate_sentences": ["a black mamba, which is a mamba, which is a snake", "a Dendroasp<PERSON> augusticeps, which is a mamba, which is a snake", "a black mamba, which is a mamba, which is a serpent", "a Dendroaspis augusticeps, which is a mamba, which is a serpent", "a black mamba, which is a mamba, which is a ophidian", "a Dendroaspis augusticeps, which is a mamba, which is a ophidian"]}, "63": {"node_name": "venomous lizard", "parent_names": ["lizard"], "child_names": ["Gila monster", "Heloderma suspectum"], "candidate_sentences": ["a Gila monster, which is a venomous lizard, which is a lizard", "a Heloderma suspectum, which is a venomous lizard, which is a lizard"]}, "64": {"node_name": "dhole, Cuon alpinus", "parent_names": ["wild dog"], "child_names": [], "candidate_sentences": ["a dhole, Cuon alpinus, which is a wild dog"]}, "65": {"node_name": "sombrero", "parent_names": ["hat", "chapeau", "lid"], "child_names": [], "candidate_sentences": ["a sombrero, which is a hat", "a sombrero, which is a chapeau", "a sombrero, which is a lid"]}, "66": {"node_name": "Arabian camel, dromedary, Camelus dromedarius", "parent_names": ["camel"], "child_names": [], "candidate_sentences": ["a Arabian camel, dromedary, Camelus dromedarius, which is a camel"]}, "67": {"node_name": "garbage truck, dustcart", "parent_names": ["truck", "motortruck"], "child_names": [], "candidate_sentences": ["a garbage truck, dustcart, which is a truck", "a garbage truck, dustcart, which is a motortruck"]}, "68": {"node_name": "Weimaraner", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Weimaraner, which is a dog", "a Weimaraner, which is a domestic dog", "a Weimaraner, which is a Canis familiaris"]}, "69": {"node_name": "true frog, ranid", "parent_names": ["frog", "toad", "toad frog", "<PERSON><PERSON>n", "batrachian", "salientian"], "child_names": ["bullfrog", "<PERSON>"], "candidate_sentences": ["a bullfrog, which is a true frog, ranid, which is a frog", "a Rana catesbeiana, which is a true frog, ranid, which is a frog", "a bullfrog, which is a true frog, ranid, which is a toad", "a Rana catesbeiana, which is a true frog, ranid, which is a toad", "a bullfrog, which is a true frog, ranid, which is a toad frog", "a Rana catesbeiana, which is a true frog, ranid, which is a toad frog", "a bullfrog, which is a true frog, ranid, which is a anuran", "a Rana catesbeiana, which is a true frog, ranid, which is a anuran", "a bullfrog, which is a true frog, ranid, which is a batrachian", "a Rana catesbeiana, which is a true frog, ranid, which is a batrachian", "a bullfrog, which is a true frog, ranid, which is a salientian", "a Rana catesbeiana, which is a true frog, ranid, which is a salientian"]}, "70": {"node_name": "gondola", "parent_names": ["boat"], "child_names": [], "candidate_sentences": ["a gondola, which is a boat"]}, "71": {"node_name": "springer spaniel, springer", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": ["English springer", "English springer spaniel", "Welsh springer spaniel"], "candidate_sentences": ["a English springer, which is a springer spaniel, springer, which is a dog", "a English springer spaniel, which is a springer spaniel, springer, which is a dog", "a Welsh springer spaniel, which is a springer spaniel, springer, which is a dog", "a English springer, which is a springer spaniel, springer, which is a domestic dog", "a English springer spaniel, which is a springer spaniel, springer, which is a domestic dog", "a Welsh springer spaniel, which is a springer spaniel, springer, which is a domestic dog", "a English springer, which is a springer spaniel, springer, which is a Canis familiaris", "a English springer spaniel, which is a springer spaniel, springer, which is a Canis familiaris", "a Welsh springer spaniel, which is a springer spaniel, springer, which is a Canis familiaris"]}, "72": {"node_name": "sea snake", "parent_names": ["snake", "serpent", "ophidian"], "child_names": [], "candidate_sentences": ["a sea snake, which is a snake", "a sea snake, which is a serpent", "a sea snake, which is a ophidian"]}, "73": {"node_name": "tile roof", "parent_names": ["roof"], "child_names": [], "candidate_sentences": ["a tile roof, which is a roof"]}, "74": {"node_name": "barn", "parent_names": ["outbuilding"], "child_names": [], "candidate_sentences": ["a barn, which is a outbuilding"]}, "75": {"node_name": "Airedale, Airedale terrier", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Airedale, Airedale terrier, which is a dog", "a Airedale, Airedale terrier, which is a domestic dog", "a Airedale, Airedale terrier, which is a Canis familiaris"]}, "76": {"node_name": "canoe", "parent_names": ["boat"], "child_names": [], "candidate_sentences": ["a canoe, which is a boat"]}, "77": {"node_name": "bath towel", "parent_names": ["towel"], "child_names": [], "candidate_sentences": ["a bath towel, which is a towel"]}, "78": {"node_name": "kimono", "parent_names": ["coat"], "child_names": [], "candidate_sentences": ["a kimono, which is a coat"]}, "79": {"node_name": "beer glass", "parent_names": ["glass", "drinking glass"], "child_names": [], "candidate_sentences": ["a beer glass, which is a glass", "a beer glass, which is a drinking glass"]}, "80": {"node_name": "coffeepot", "parent_names": ["pot"], "child_names": [], "candidate_sentences": ["a coffeepot, which is a pot"]}, "81": {"node_name": "Norwegian elkhound, elkhound", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Norwegian elkhound, elkhound, which is a dog", "a Norwegian elkhound, elkhound, which is a domestic dog", "a Norwegian elkhound, elkhound, which is a Canis familiaris"]}, "82": {"node_name": "cardigan", "parent_names": ["sweater", "jumper"], "child_names": [], "candidate_sentences": ["a cardigan, which is a sweater", "a cardigan, which is a jumper"]}, "83": {"node_name": "Shih-<PERSON><PERSON>", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Shih-Tzu, which is a dog", "a Shih-Tzu, which is a domestic dog", "a Shih-Tzu, which is a Canis familiaris"]}, "84": {"node_name": "black and gold garden spider, <PERSON><PERSON><PERSON><PERSON> aurantia", "parent_names": ["spider"], "child_names": [], "candidate_sentences": ["a black and gold garden spider, <PERSON><PERSON><PERSON><PERSON> aurantia, which is a spider"]}, "85": {"node_name": "Loafer", "parent_names": ["shoe"], "child_names": [], "candidate_sentences": ["a Loafer, which is a shoe"]}, "86": {"node_name": "iPod", "parent_names": ["audio system", "sound system"], "child_names": [], "candidate_sentences": ["a iPod, which is a audio system", "a iPod, which is a sound system"]}, "87": {"node_name": "balance beam, beam", "parent_names": ["gymnastic apparatus", "exerciser"], "child_names": [], "candidate_sentences": ["a balance beam, beam, which is a gymnastic apparatus", "a balance beam, beam, which is a exerciser"]}, "88": {"node_name": "overskirt", "parent_names": ["skirt"], "child_names": [], "candidate_sentences": ["a overskirt, which is a skirt"]}, "89": {"node_name": "timber wolf, grey wolf, gray wolf, Canis lupus", "parent_names": ["wolf"], "child_names": [], "candidate_sentences": ["a timber wolf, grey wolf, gray wolf, Canis lupus, which is a wolf"]}, "90": {"node_name": "ice cream, icecream", "parent_names": ["frozen dessert"], "child_names": [], "candidate_sentences": ["a ice cream, icecream, which is a frozen dessert"]}, "91": {"node_name": "indri, indris, <PERSON>dr<PERSON> indri, Indri brevicaudatus", "parent_names": ["lemur"], "child_names": [], "candidate_sentences": ["a indri, indris, Indri indri, Indri brevicaudatus, which is a lemur"]}, "92": {"node_name": "rugby ball", "parent_names": ["ball"], "child_names": [], "candidate_sentences": ["a rugby ball, which is a ball"]}, "93": {"node_name": "minibus", "parent_names": ["bus", "autobus", "coach", "charabanc", "double-decker", "jitney", "motorbus", "motorcoach", "omnibus", "passenger vehicle"], "child_names": [], "candidate_sentences": ["a minibus, which is a bus", "a minibus, which is a autobus", "a minibus, which is a coach", "a minibus, which is a charabanc", "a minibus, which is a double-decker", "a minibus, which is a jitney", "a minibus, which is a motorbus", "a minibus, which is a motorcoach", "a minibus, which is a omnibus", "a minibus, which is a passenger vehicle"]}, "94": {"node_name": "hoopskirt, crinoline", "parent_names": ["skirt"], "child_names": [], "candidate_sentences": ["a hoopskirt, crinoline, which is a skirt"]}, "95": {"node_name": "macaw", "parent_names": ["parrot"], "child_names": [], "candidate_sentences": ["a macaw, which is a parrot"]}, "96": {"node_name": "miniskirt, mini", "parent_names": ["skirt"], "child_names": [], "candidate_sentences": ["a miniskirt, mini, which is a skirt"]}, "97": {"node_name": "African elephant, Loxodonta africana", "parent_names": ["elephant"], "child_names": [], "candidate_sentences": ["a African elephant, Loxodonta africana, which is a elephant"]}, "98": {"node_name": "viaduct", "parent_names": ["bridge", "span"], "child_names": [], "candidate_sentences": ["a viaduct, which is a bridge", "a viaduct, which is a span"]}, "99": {"node_name": "bittern", "parent_names": ["heron"], "child_names": [], "candidate_sentences": ["a bittern, which is a heron"]}, "100": {"node_name": "cuirass", "parent_names": ["body armor", "body armour", "suit of armor", "suit of armour", "coat of mail", "cataphract"], "child_names": [], "candidate_sentences": ["a cuirass, which is a body armor", "a cuirass, which is a body armour", "a cuirass, which is a suit of armor", "a cuirass, which is a suit of armour", "a cuirass, which is a coat of mail", "a cuirass, which is a cataphract"]}, "101": {"node_name": "maillot, tank suit", "parent_names": ["swimsuit", "swimwear", "bathing suit", "swimming costume", "bathing costume"], "child_names": [], "candidate_sentences": ["a maillot, tank suit, which is a swimsuit", "a maillot, tank suit, which is a swimwear", "a maillot, tank suit, which is a bathing suit", "a maillot, tank suit, which is a swimming costume", "a maillot, tank suit, which is a bathing costume"]}, "102": {"node_name": "basketball", "parent_names": ["ball"], "child_names": [], "candidate_sentences": ["a basketball, which is a ball"]}, "103": {"node_name": "mitten", "parent_names": ["glove"], "child_names": [], "candidate_sentences": ["a mitten, which is a glove"]}, "104": {"node_name": "notebook, notebook computer", "parent_names": ["digital computer"], "child_names": [], "candidate_sentences": ["a notebook, notebook computer, which is a digital computer"]}, "105": {"node_name": "spiny lobster, langouste, rock lobster, crawfish, crayfish, sea crawfish", "parent_names": ["lobster"], "child_names": [], "candidate_sentences": ["a spiny lobster, langouste, rock lobster, crawfish, crayfish, sea crawfish, which is a lobster"]}, "106": {"node_name": "turnstile", "parent_names": ["gate"], "child_names": [], "candidate_sentences": ["a turnstile, which is a gate"]}, "107": {"node_name": "shed", "parent_names": ["outbuilding"], "child_names": ["apiary", "bee house", "boathouse"], "candidate_sentences": ["a apiary, which is a shed, which is a outbuilding", "a bee house, which is a shed, which is a outbuilding", "a boathouse, which is a shed, which is a outbuilding"]}, "108": {"node_name": "bookshop, bookstore, bookstall", "parent_names": ["mercantile establishment", "retail store", "sales outlet", "outlet"], "child_names": [], "candidate_sentences": ["a bookshop, bookstore, bookstall, which is a mercantile establishment", "a bookshop, bookstore, bookstall, which is a retail store", "a bookshop, bookstore, bookstall, which is a sales outlet", "a bookshop, bookstore, bookstall, which is a outlet"]}, "109": {"node_name": "Saluki, gazelle hound", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Saluki, gazelle hound, which is a dog", "a Saluki, gazelle hound, which is a domestic dog", "a Saluki, gazelle hound, which is a Canis familiaris"]}, "110": {"node_name": "garter snake, grass snake", "parent_names": ["snake", "serpent", "ophidian"], "child_names": [], "candidate_sentences": ["a garter snake, grass snake, which is a snake", "a garter snake, grass snake, which is a serpent", "a garter snake, grass snake, which is a ophidian"]}, "111": {"node_name": "rock crab, Cancer irroratus", "parent_names": ["crab"], "child_names": [], "candidate_sentences": ["a rock crab, <PERSON> irroratus, which is a crab"]}, "112": {"node_name": "cowboy boot", "parent_names": ["boot"], "child_names": [], "candidate_sentences": ["a cowboy boot, which is a boot"]}, "113": {"node_name": "face powder", "parent_names": ["makeup", "make-up", "war paint"], "child_names": [], "candidate_sentences": ["a face powder, which is a makeup", "a face powder, which is a make-up", "a face powder, which is a war paint"]}, "114": {"node_name": "steel drum", "parent_names": ["percussion instrument", "percussive instrument"], "child_names": [], "candidate_sentences": ["a steel drum, which is a percussion instrument", "a steel drum, which is a percussive instrument"]}, "115": {"node_name": "<PERSON> hound, Walker foxhound", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Walker hound, <PERSON> foxhound, which is a dog", "a Walker hound, <PERSON> foxhound, which is a domestic dog", "a Walker hound, <PERSON> foxhound, which is a Canis familiaris"]}, "116": {"node_name": "ptarmigan", "parent_names": ["grouse"], "child_names": [], "candidate_sentences": ["a ptarmigan, which is a grouse"]}, "117": {"node_name": "frying pan, frypan, skillet", "parent_names": ["pan", "cooking pan"], "child_names": [], "candidate_sentences": ["a frying pan, frypan, skillet, which is a pan", "a frying pan, frypan, skillet, which is a cooking pan"]}, "118": {"node_name": "bolo tie, bolo, bola tie, bola", "parent_names": ["necktie", "tie"], "child_names": [], "candidate_sentences": ["a bolo tie, bolo, bola tie, bola, which is a necktie", "a bolo tie, bolo, bola tie, bola, which is a tie"]}, "119": {"node_name": "brass, memorial tablet, plaque", "parent_names": ["memorial", "monument"], "child_names": [], "candidate_sentences": ["a brass, memorial tablet, plaque, which is a memorial", "a brass, memorial tablet, plaque, which is a monument"]}, "120": {"node_name": "goblet", "parent_names": ["glass", "drinking glass"], "child_names": [], "candidate_sentences": ["a goblet, which is a glass", "a goblet, which is a drinking glass"]}, "121": {"node_name": "chime, bell, gong", "parent_names": ["percussion instrument", "percussive instrument"], "child_names": [], "candidate_sentences": ["a chime, bell, gong, which is a percussion instrument", "a chime, bell, gong, which is a percussive instrument"]}, "122": {"node_name": "Ang<PERSON>, Angora rabbit", "parent_names": ["rabbit", "coney", "cony"], "child_names": [], "candidate_sentences": ["a Angora, Angora rabbit, which is a rabbit", "a Angora, Angora rabbit, which is a coney", "a Angora, Angora rabbit, which is a cony"]}, "123": {"node_name": "sorrel", "parent_names": ["horse", "<PERSON>qu<PERSON> caballus"], "child_names": [], "candidate_sentences": ["a sorrel, which is a horse", "a sorrel, which is a Equus caballus"]}, "124": {"node_name": "English setter", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a English setter, which is a dog", "a English setter, which is a domestic dog", "a English setter, which is a Canis familiaris"]}, "125": {"node_name": "bolete", "parent_names": ["mushroom"], "child_names": [], "candidate_sentences": ["a bolete, which is a mushroom"]}, "126": {"node_name": "medicine chest, medicine cabinet", "parent_names": ["cabinet"], "child_names": [], "candidate_sentences": ["a medicine chest, medicine cabinet, which is a cabinet"]}, "127": {"node_name": "shoe shop, shoe-shop, shoe store", "parent_names": ["mercantile establishment", "retail store", "sales outlet", "outlet"], "child_names": [], "candidate_sentences": ["a shoe shop, shoe-shop, shoe store, which is a mercantile establishment", "a shoe shop, shoe-shop, shoe store, which is a retail store", "a shoe shop, shoe-shop, shoe store, which is a sales outlet", "a shoe shop, shoe-shop, shoe store, which is a outlet"]}, "128": {"node_name": "chiffonier, commode", "parent_names": ["chest of drawers", "chest", "bureau", "dresser"], "child_names": [], "candidate_sentences": ["a chiffonier, commode, which is a chest of drawers", "a chiffonier, commode, which is a chest", "a chiffonier, commode, which is a bureau", "a chiffonier, commode, which is a dresser"]}, "129": {"node_name": "wooden spoon", "parent_names": ["spoon"], "child_names": [], "candidate_sentences": ["a wooden spoon, which is a spoon"]}, "130": {"node_name": "bassinet", "parent_names": ["baby bed", "baby's bed"], "child_names": [], "candidate_sentences": ["a bassinet, which is a baby bed", "a bassinet, which is a baby's bed"]}, "131": {"node_name": "cockatoo", "parent_names": ["parrot"], "child_names": ["sulphur-crested cockatoo", "Kakatoe galerita", "Cacatua galerita"], "candidate_sentences": ["a sulphur-crested cockatoo, which is a cockatoo, which is a parrot", "a Kakatoe galerita, which is a cockatoo, which is a parrot", "a Cacatua galerita, which is a cockatoo, which is a parrot"]}, "132": {"node_name": "planetarium", "parent_names": ["dummy46"], "child_names": [], "candidate_sentences": ["a planetarium, which is a dummy46"]}, "133": {"node_name": "griffon, Brussels griffon, Belgian griffon", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": ["Brabancon griffon"], "candidate_sentences": ["a Brabancon griffon, which is a griffon, Brussels griffon, Belgian griffon, which is a dog", "a Brabancon griffon, which is a griffon, Brussels griffon, Belgian griffon, which is a domestic dog", "a Brabancon griffon, which is a griffon, Brussels griffon, Belgian griffon, which is a Canis familiaris"]}, "134": {"node_name": "radio telescope, radio reflector", "parent_names": ["telescope", "scope"], "child_names": [], "candidate_sentences": ["a radio telescope, radio reflector, which is a telescope", "a radio telescope, radio reflector, which is a scope"]}, "135": {"node_name": "lady's slipper, lady-slipper, ladies' slipper, slipper orchid", "parent_names": ["orchid", "orchidaceous plant"], "child_names": ["yellow lady's slipper", "yellow lady-slipper", "Cypripedium calceolus", "Cypripedium parviflorum"], "candidate_sentences": ["a yellow lady's slipper, which is a lady's slipper, lady-slipper, ladies' slipper, slipper orchid, which is a orchid", "a yellow lady-slipper, which is a lady's slipper, lady-slipper, ladies' slipper, slipper orchid, which is a orchid", "a Cypripedium calceolus, which is a lady's slipper, lady-slipper, ladies' slipper, slipper orchid, which is a orchid", "a Cypripedium parviflorum, which is a lady's slipper, lady-slipper, ladies' slipper, slipper orchid, which is a orchid", "a yellow lady's slipper, which is a lady's slipper, lady-slipper, ladies' slipper, slipper orchid, which is a orchidaceous plant", "a yellow lady-slipper, which is a lady's slipper, lady-slipper, ladies' slipper, slipper orchid, which is a orchidaceous plant", "a Cypripedium calceolus, which is a lady's slipper, lady-slipper, ladies' slipper, slipper orchid, which is a orchidaceous plant", "a Cypripedium parviflorum, which is a lady's slipper, lady-slipper, ladies' slipper, slipper orchid, which is a orchidaceous plant"]}, "136": {"node_name": "gorilla, Gorilla gorilla", "parent_names": ["ape"], "child_names": [], "candidate_sentences": ["a gorilla, Gorilla gorilla, which is a ape"]}, "137": {"node_name": "hammerhead, hammerhead shark", "parent_names": ["shark"], "child_names": [], "candidate_sentences": ["a hammerhead, hammerhead shark, which is a shark"]}, "138": {"node_name": "French loaf", "parent_names": ["loaf of bread", "loaf"], "child_names": [], "candidate_sentences": ["a French loaf, which is a loaf of bread", "a French loaf, which is a loaf"]}, "139": {"node_name": "forklift", "parent_names": ["truck", "motortruck"], "child_names": [], "candidate_sentences": ["a forklift, which is a truck", "a forklift, which is a motortruck"]}, "140": {"node_name": "flat-coated retriever", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a flat-coated retriever, which is a dog", "a flat-coated retriever, which is a domestic dog", "a flat-coated retriever, which is a Canis familiaris"]}, "141": {"node_name": "marmoset", "parent_names": ["monkey"], "child_names": [], "candidate_sentences": ["a marmoset, which is a monkey"]}, "142": {"node_name": "passenger car, coach, carriage", "parent_names": ["car", "railcar", "railway car", "railroad car"], "child_names": [], "candidate_sentences": ["a passenger car, coach, carriage, which is a car", "a passenger car, coach, carriage, which is a railcar", "a passenger car, coach, carriage, which is a railway car", "a passenger car, coach, carriage, which is a railroad car"]}, "143": {"node_name": "bald eagle, American eagle, Haliaeetus leucocephalus", "parent_names": ["eagle", "bird of Jove"], "child_names": [], "candidate_sentences": ["a bald eagle, American eagle, <PERSON><PERSON><PERSON> leucocephalus, which is a eagle", "a bald eagle, American eagle, <PERSON><PERSON><PERSON> leucocephalus, which is a bird of Jove"]}, "144": {"node_name": "mailbag, postbag", "parent_names": ["bag"], "child_names": [], "candidate_sentences": ["a mailbag, postbag, which is a bag"]}, "145": {"node_name": "poncho", "parent_names": ["coat"], "child_names": [], "candidate_sentences": ["a poncho, which is a coat"]}, "146": {"node_name": "scoreboard", "parent_names": ["signboard", "sign"], "child_names": [], "candidate_sentences": ["a scoreboard, which is a signboard", "a scoreboard, which is a sign"]}, "147": {"node_name": "moving van", "parent_names": ["truck", "motortruck"], "child_names": [], "candidate_sentences": ["a moving van, which is a truck", "a moving van, which is a motortruck"]}, "148": {"node_name": "scarabaeid beetle, scarabaeid, scarabaean", "parent_names": ["beetle"], "child_names": ["dung beetle", "rhinoceros beetle"], "candidate_sentences": ["a dung beetle, which is a scarabaeid beetle, scarabaeid, scarabaean, which is a beetle", "a rhinoceros beetle, which is a scarabaeid beetle, scarabaeid, scarabaean, which is a beetle"]}, "149": {"node_name": "espresso maker", "parent_names": ["coffee maker"], "child_names": [], "candidate_sentences": ["a espresso maker, which is a coffee maker"]}, "150": {"node_name": "bulletproof vest", "parent_names": ["body armor", "body armour", "suit of armor", "suit of armour", "coat of mail", "cataphract"], "child_names": [], "candidate_sentences": ["a bulletproof vest, which is a body armor", "a bulletproof vest, which is a body armour", "a bulletproof vest, which is a suit of armor", "a bulletproof vest, which is a suit of armour", "a bulletproof vest, which is a coat of mail", "a bulletproof vest, which is a cataphract"]}, "151": {"node_name": "golden retriever", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a golden retriever, which is a dog", "a golden retriever, which is a domestic dog", "a golden retriever, which is a Canis familiaris"]}, "152": {"node_name": "ruddy turnstone, Arenaria interpres", "parent_names": ["plover"], "child_names": [], "candidate_sentences": ["a ruddy turnstone, Arenaria interpres, which is a plover"]}, "153": {"node_name": "scabbard", "parent_names": ["sheath"], "child_names": [], "candidate_sentences": ["a scabbard, which is a sheath"]}, "154": {"node_name": "magnetic compass", "parent_names": ["compass"], "child_names": [], "candidate_sentences": ["a magnetic compass, which is a compass"]}, "155": {"node_name": "whiskey jug", "parent_names": ["jug"], "child_names": [], "candidate_sentences": ["a whiskey jug, which is a jug"]}, "156": {"node_name": "piano, pianoforte, forte-piano", "parent_names": ["keyboard instrument"], "child_names": ["grand piano", "grand", "upright", "upright piano"], "candidate_sentences": ["a grand piano, which is a piano, pianoforte, forte-piano, which is a keyboard instrument", "a grand, which is a piano, pianoforte, forte-piano, which is a keyboard instrument", "a upright, which is a piano, pianoforte, forte-piano, which is a keyboard instrument", "a upright piano, which is a piano, pianoforte, forte-piano, which is a keyboard instrument"]}, "157": {"node_name": "Rottweiler", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Rottweiler, which is a dog", "a Rottweiler, which is a domestic dog", "a Rottweiler, which is a Canis familiaris"]}, "158": {"node_name": "fox terrier", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": ["wire-haired fox terrier"], "candidate_sentences": ["a wire-haired fox terrier, which is a fox terrier, which is a dog", "a wire-haired fox terrier, which is a fox terrier, which is a domestic dog", "a wire-haired fox terrier, which is a fox terrier, which is a Canis familiaris"]}, "159": {"node_name": "Ibizan hound, Ibizan Podenco", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Ibizan hound, <PERSON><PERSON><PERSON>, which is a dog", "a Ibizan hound, <PERSON><PERSON><PERSON>, which is a domestic dog", "a Ibizan hound, <PERSON><PERSON>zan <PERSON>den<PERSON>, which is a Canis familiaris"]}, "160": {"node_name": "colobus, colobus monkey", "parent_names": ["monkey"], "child_names": [], "candidate_sentences": ["a colobus, colobus monkey, which is a monkey"]}, "161": {"node_name": "stingray", "parent_names": ["ray"], "child_names": [], "candidate_sentences": ["a stingray, which is a ray"]}, "162": {"node_name": "horned viper, cerastes, sand viper, horned asp, <PERSON>rastes cornutus", "parent_names": ["snake", "serpent", "ophidian"], "child_names": [], "candidate_sentences": ["a horned viper, cerastes, sand viper, horned asp, <PERSON>rastes cornutus, which is a snake", "a horned viper, cerastes, sand viper, horned asp, <PERSON>rastes cornutus, which is a serpent", "a horned viper, cerastes, sand viper, horned asp, <PERSON>rastes cornutus, which is a ophidian"]}, "163": {"node_name": "Model T", "parent_names": ["car", "auto", "automobile", "machine", "motorcar"], "child_names": [], "candidate_sentences": ["a Model T, which is a car", "a Model T, which is a auto", "a Model T, which is a automobile", "a Model T, which is a machine", "a Model T, which is a motorcar"]}, "164": {"node_name": "wok", "parent_names": ["pan", "cooking pan"], "child_names": [], "candidate_sentences": ["a wok, which is a pan", "a wok, which is a cooking pan"]}, "165": {"node_name": "water jug", "parent_names": ["jug"], "child_names": [], "candidate_sentences": ["a water jug, which is a jug"]}, "166": {"node_name": "Maltese dog, Maltese terrier, Maltese", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Maltese dog, Maltese terrier, Maltese, which is a dog", "a Maltese dog, Maltese terrier, Maltese, which is a domestic dog", "a Maltese dog, Maltese terrier, Maltese, which is a Canis familiaris"]}, "167": {"node_name": "kuvasz", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a kuvasz, which is a dog", "a kuvasz, which is a domestic dog", "a kuvasz, which is a Canis familiaris"]}, "168": {"node_name": "impala, Aepyceros melampus", "parent_names": ["antelope"], "child_names": [], "candidate_sentences": ["a impala, Aepyceros melampus, which is a antelope"]}, "169": {"node_name": "sea turtle, marine turtle", "parent_names": ["turtle"], "child_names": ["loggerhead", "loggerhead turtle", "Caretta caretta", "leatherback turtle", "leatherback", "leathery turtle", "<PERSON><PERSON><PERSON><PERSON> coriacea"], "candidate_sentences": ["a loggerhead, which is a sea turtle, marine turtle, which is a turtle", "a loggerhead turtle, which is a sea turtle, marine turtle, which is a turtle", "a Caretta caretta, which is a sea turtle, marine turtle, which is a turtle", "a leatherback turtle, which is a sea turtle, marine turtle, which is a turtle", "a leatherback, which is a sea turtle, marine turtle, which is a turtle", "a leathery turtle, which is a sea turtle, marine turtle, which is a turtle", "a Dermochelys coriacea, which is a sea turtle, marine turtle, which is a turtle"]}, "170": {"node_name": "baseball", "parent_names": ["ball"], "child_names": [], "candidate_sentences": ["a baseball, which is a ball"]}, "171": {"node_name": "shower cap", "parent_names": ["cap"], "child_names": [], "candidate_sentences": ["a shower cap, which is a cap"]}, "172": {"node_name": "folding chair", "parent_names": ["chair"], "child_names": [], "candidate_sentences": ["a folding chair, which is a chair"]}, "173": {"node_name": "coffee mug", "parent_names": ["mug"], "child_names": [], "candidate_sentences": ["a coffee mug, which is a mug"]}, "174": {"node_name": "howler monkey, howler", "parent_names": ["monkey"], "child_names": [], "candidate_sentences": ["a howler monkey, howler, which is a monkey"]}, "175": {"node_name": "library", "parent_names": ["dummy45"], "child_names": [], "candidate_sentences": ["a library, which is a dummy45"]}, "176": {"node_name": "plastic bag", "parent_names": ["bag"], "child_names": [], "candidate_sentences": ["a plastic bag, which is a bag"]}, "177": {"node_name": "lotion", "parent_names": ["cream", "ointment", "emollient"], "child_names": [], "candidate_sentences": ["a lotion, which is a cream", "a lotion, which is a ointment", "a lotion, which is a emollient"]}, "178": {"node_name": "four-poster", "parent_names": ["bed"], "child_names": [], "candidate_sentences": ["a four-poster, which is a bed"]}, "179": {"node_name": "golf ball", "parent_names": ["ball"], "child_names": [], "candidate_sentences": ["a golf ball, which is a ball"]}, "180": {"node_name": "mobile home, manufactured home", "parent_names": ["dwelling", "home", "domicile", "abode", "habitation", "dwelling house"], "child_names": [], "candidate_sentences": ["a mobile home, manufactured home, which is a dwelling", "a mobile home, manufactured home, which is a home", "a mobile home, manufactured home, which is a domicile", "a mobile home, manufactured home, which is a abode", "a mobile home, manufactured home, which is a habitation", "a mobile home, manufactured home, which is a dwelling house"]}, "181": {"node_name": "bagel, beigel", "parent_names": ["bun", "roll"], "child_names": [], "candidate_sentences": ["a bagel, beigel, which is a bun", "a bagel, beigel, which is a roll"]}, "182": {"node_name": "bluetick", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a bluetick, which is a dog", "a bluetick, which is a domestic dog", "a bluetick, which is a Canis familiaris"]}, "183": {"node_name": "schooner", "parent_names": ["ship"], "child_names": [], "candidate_sentences": ["a schooner, which is a ship"]}, "184": {"node_name": "Norwich terrier", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Norwich terrier, which is a dog", "a Norwich terrier, which is a domestic dog", "a Norwich terrier, which is a Canis familiaris"]}, "185": {"node_name": "Newfoundland, Newfoundland dog", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Newfoundland, Newfoundland dog, which is a dog", "a Newfoundland, Newfoundland dog, which is a domestic dog", "a Newfoundland, Newfoundland dog, which is a Canis familiaris"]}, "186": {"node_name": "hair slide", "parent_names": ["clip"], "child_names": [], "candidate_sentences": ["a hair slide, which is a clip"]}, "187": {"node_name": "brassiere, bra, bandeau", "parent_names": ["undergarment", "unmentionable"], "child_names": [], "candidate_sentences": ["a brassiere, bra, bandeau, which is a undergarment", "a brassiere, bra, bandeau, which is a unmentionable"]}, "188": {"node_name": "Tibetan terrier, chrysanthemum dog", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Tibetan terrier, chrysanthemum dog, which is a dog", "a Tibetan terrier, chrysanthemum dog, which is a domestic dog", "a Tibetan terrier, chrysanthemum dog, which is a Canis familiaris"]}, "189": {"node_name": "Madagascar cat, ring-tailed lemur, Lemur catta", "parent_names": ["lemur"], "child_names": [], "candidate_sentences": ["a Madagascar cat, ring-tailed lemur, Lemur catta, which is a lemur"]}, "190": {"node_name": "wreck", "parent_names": ["ship"], "child_names": [], "candidate_sentences": ["a wreck, which is a ship"]}, "191": {"node_name": "go-kart", "parent_names": ["car", "auto", "automobile", "machine", "motorcar"], "child_names": [], "candidate_sentences": ["a go-kart, which is a car", "a go-kart, which is a auto", "a go-kart, which is a automobile", "a go-kart, which is a machine", "a go-kart, which is a motorcar"]}, "192": {"node_name": "soccer ball", "parent_names": ["ball"], "child_names": [], "candidate_sentences": ["a soccer ball, which is a ball"]}, "193": {"node_name": "winter squash", "parent_names": ["squash"], "child_names": ["acorn squash", "butternut squash"], "candidate_sentences": ["a acorn squash, which is a winter squash, which is a squash", "a butternut squash, which is a winter squash, which is a squash"]}, "194": {"node_name": "clumber, clumber spaniel", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a clumber, clumber spaniel, which is a dog", "a clumber, clumber spaniel, which is a domestic dog", "a clumber, clumber spaniel, which is a Canis familiaris"]}, "195": {"node_name": "Pomeranian", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Pomeranian, which is a dog", "a Pomeranian, which is a domestic dog", "a Pomeranian, which is a Canis familiaris"]}, "196": {"node_name": "tick", "parent_names": ["acarine"], "child_names": [], "candidate_sentences": ["a tick, which is a acarine"]}, "197": {"node_name": "harmonica, mouth organ, harp, mouth harp", "parent_names": ["wind instrument", "wind"], "child_names": [], "candidate_sentences": ["a harmonica, mouth organ, harp, mouth harp, which is a wind instrument", "a harmonica, mouth organ, harp, mouth harp, which is a wind"]}, "198": {"node_name": "Saint Bernard, St Bernard", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Saint Bernard, St Bernard, which is a dog", "a Saint Bernard, St Bernard, which is a domestic dog", "a Saint Bernard, St Bernard, which is a Canis familiaris"]}, "199": {"node_name": "bulbul", "parent_names": ["nightingale", "Luscinia megarhynchos"], "child_names": [], "candidate_sentences": ["a bulbul, which is a nightingale", "a bulbul, which is a Luscinia megarhynchos"]}, "200": {"node_name": "shrine", "parent_names": ["place of worship", "house of prayer", "house of God", "house of worship"], "child_names": ["stupa", "tope"], "candidate_sentences": ["a stupa, which is a shrine, which is a place of worship", "a tope, which is a shrine, which is a place of worship", "a stupa, which is a shrine, which is a house of prayer", "a tope, which is a shrine, which is a house of prayer", "a stupa, which is a shrine, which is a house of God", "a tope, which is a shrine, which is a house of God", "a stupa, which is a shrine, which is a house of worship", "a tope, which is a shrine, which is a house of worship"]}, "201": {"node_name": "tabby, tabby cat", "parent_names": ["domestic cat", "house cat", "Fe<PERSON> domesticus", "<PERSON><PERSON> catus"], "child_names": [], "candidate_sentences": ["a tabby, tabby cat, which is a domestic cat", "a tabby, tabby cat, which is a house cat", "a tabby, tabby cat, which is a Felis domesticus", "a tabby, tabby cat, which is a Felis catus"]}, "202": {"node_name": "wirehair, wirehaired terrier, wire-haired terrier", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": ["Lakeland terrier", "Welsh terrier"], "candidate_sentences": ["a Lakeland terrier, which is a wirehair, wirehaired terrier, wire-haired terrier, which is a dog", "a Welsh terrier, which is a wirehair, wirehaired terrier, wire-haired terrier, which is a dog", "a Lakeland terrier, which is a wirehair, wirehaired terrier, wire-haired terrier, which is a domestic dog", "a Welsh terrier, which is a wirehair, wirehaired terrier, wire-haired terrier, which is a domestic dog", "a Lakeland terrier, which is a wirehair, wirehaired terrier, wire-haired terrier, which is a Canis familiaris", "a Welsh terrier, which is a wirehair, wirehaired terrier, wire-haired terrier, which is a Canis familiaris"]}, "203": {"node_name": "Kerry blue terrier", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Kerry blue terrier, which is a dog", "a Kerry blue terrier, which is a domestic dog", "a Kerry blue terrier, which is a Canis familiaris"]}, "204": {"node_name": "titi, titi monkey", "parent_names": ["monkey"], "child_names": [], "candidate_sentences": ["a titi, titi monkey, which is a monkey"]}, "205": {"node_name": "fire engine, fire truck", "parent_names": ["truck", "motortruck"], "child_names": [], "candidate_sentences": ["a fire engine, fire truck, which is a truck", "a fire engine, fire truck, which is a motortruck"]}, "206": {"node_name": "snowplow, snowplough", "parent_names": ["truck", "motortruck"], "child_names": [], "candidate_sentences": ["a snowplow, snowplough, which is a truck", "a snowplow, snowplough, which is a motortruck"]}, "207": {"node_name": "passenger ship", "parent_names": ["ship"], "child_names": ["liner", "ocean liner"], "candidate_sentences": ["a liner, which is a passenger ship, which is a ship", "a ocean liner, which is a passenger ship, which is a ship"]}, "208": {"node_name": "Crock Pot", "parent_names": ["cooker"], "child_names": [], "candidate_sentences": ["a Crock Pot, which is a cooker"]}, "209": {"node_name": "coot", "parent_names": ["rail"], "child_names": ["American coot", "marsh hen", "mud hen", "water hen", "Fulica americana"], "candidate_sentences": ["a American coot, which is a coot, which is a rail", "a marsh hen, which is a coot, which is a rail", "a mud hen, which is a coot, which is a rail", "a water hen, which is a coot, which is a rail", "a Fulica americana, which is a coot, which is a rail"]}, "210": {"node_name": "chimpanzee, chimp, Pan troglodytes", "parent_names": ["ape"], "child_names": [], "candidate_sentences": ["a chimpanzee, chimp, Pan troglodytes, which is a ape"]}, "211": {"node_name": "weevil", "parent_names": ["beetle"], "child_names": [], "candidate_sentences": ["a weevil, which is a beetle"]}, "212": {"node_name": "pretzel", "parent_names": ["cracker"], "child_names": [], "candidate_sentences": ["a pretzel, which is a cracker"]}, "213": {"node_name": "neck brace", "parent_names": ["brace"], "child_names": [], "candidate_sentences": ["a neck brace, which is a brace"]}, "214": {"node_name": "ice bear, polar bear, Ursus Maritimus, T<PERSON><PERSON><PERSON> maritimus", "parent_names": ["bear"], "child_names": [], "candidate_sentences": ["a ice bear, polar bear, Ursus Maritimus, Thalarctos maritimus, which is a bear"]}, "215": {"node_name": "teiid lizard, teiid", "parent_names": ["lizard"], "child_names": ["whiptail", "whiptail lizard"], "candidate_sentences": ["a whiptail, which is a teiid lizard, teiid, which is a lizard", "a whiptail lizard, which is a teiid lizard, teiid, which is a lizard"]}, "216": {"node_name": "boxer", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a boxer, which is a dog", "a boxer, which is a domestic dog", "a boxer, which is a Canis familiaris"]}, "217": {"node_name": "mashed potato", "parent_names": ["potato", "white potato", "Irish potato", "murphy", "spud", "tater"], "child_names": [], "candidate_sentences": ["a mashed potato, which is a potato", "a mashed potato, which is a white potato", "a mashed potato, which is a Irish potato", "a mashed potato, which is a murphy", "a mashed potato, which is a spud", "a mashed potato, which is a tater"]}, "218": {"node_name": "castle", "parent_names": ["dwelling", "home", "domicile", "abode", "habitation", "dwelling house"], "child_names": [], "candidate_sentences": ["a castle, which is a dwelling", "a castle, which is a home", "a castle, which is a domicile", "a castle, which is a abode", "a castle, which is a habitation", "a castle, which is a dwelling house"]}, "219": {"node_name": "chocolate sauce, chocolate syrup", "parent_names": ["sauce"], "child_names": [], "candidate_sentences": ["a chocolate sauce, chocolate syrup, which is a sauce"]}, "220": {"node_name": "mosque", "parent_names": ["place of worship", "house of prayer", "house of God", "house of worship"], "child_names": [], "candidate_sentences": ["a mosque, which is a place of worship", "a mosque, which is a house of prayer", "a mosque, which is a house of God", "a mosque, which is a house of worship"]}, "221": {"node_name": "pullover, slipover", "parent_names": ["sweater", "jumper"], "child_names": ["sweatshirt"], "candidate_sentences": ["a sweatshirt, which is a pullover, slipover, which is a sweater", "a sweatshirt, which is a pullover, slipover, which is a jumper"]}, "222": {"node_name": "church, church building", "parent_names": ["place of worship", "house of prayer", "house of God", "house of worship"], "child_names": [], "candidate_sentences": ["a church, church building, which is a place of worship", "a church, church building, which is a house of prayer", "a church, church building, which is a house of God", "a church, church building, which is a house of worship"]}, "223": {"node_name": "<PERSON><PERSON><PERSON><PERSON>", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Appenzeller, which is a dog", "a Appenzeller, which is a domestic dog", "a Appenzeller, which is a Canis familiaris"]}, "224": {"node_name": "trolleybus, trolley coach, trackless trolley", "parent_names": ["bus", "autobus", "coach", "charabanc", "double-decker", "jitney", "motorbus", "motorcoach", "omnibus", "passenger vehicle"], "child_names": [], "candidate_sentences": ["a trolleybus, trolley coach, trackless trolley, which is a bus", "a trolleybus, trolley coach, trackless trolley, which is a autobus", "a trolleybus, trolley coach, trackless trolley, which is a coach", "a trolleybus, trolley coach, trackless trolley, which is a charabanc", "a trolleybus, trolley coach, trackless trolley, which is a double-decker", "a trolleybus, trolley coach, trackless trolley, which is a jitney", "a trolleybus, trolley coach, trackless trolley, which is a motorbus", "a trolleybus, trolley coach, trackless trolley, which is a motorcoach", "a trolleybus, trolley coach, trackless trolley, which is a omnibus", "a trolleybus, trolley coach, trackless trolley, which is a passenger vehicle"]}, "225": {"node_name": "grey whale, gray whale, devilfish, <PERSON><PERSON><PERSON><PERSON> gibbosus, <PERSON><PERSON><PERSON><PERSON> robustus", "parent_names": ["whale"], "child_names": [], "candidate_sentences": ["a grey whale, gray whale, devilfish, <PERSON><PERSON><PERSON><PERSON> gibbosus, <PERSON><PERSON><PERSON><PERSON> robustus, which is a whale"]}, "226": {"node_name": "parallel bars, bars", "parent_names": ["gymnastic apparatus", "exerciser"], "child_names": [], "candidate_sentences": ["a parallel bars, bars, which is a gymnastic apparatus", "a parallel bars, bars, which is a exerciser"]}, "227": {"node_name": "Greater Swiss Mountain dog", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Greater Swiss Mountain dog, which is a dog", "a Greater Swiss Mountain dog, which is a domestic dog", "a Greater Swiss Mountain dog, which is a Canis familiaris"]}, "228": {"node_name": "Belgian sheepdog, Belgian shepherd", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "malinois"], "candidate_sentences": ["a groenendael, which is a Belgian sheepdog, Belgian shepherd, which is a dog", "a malinois, which is a Belgian sheepdog, Belgian shepherd, which is a dog", "a groenendael, which is a Belgian sheepdog, Belgian shepherd, which is a domestic dog", "a malinois, which is a Belgian sheepdog, Belgian shepherd, which is a domestic dog", "a groenendael, which is a Belgian sheepdog, Belgian shepherd, which is a Canis familiaris", "a malinois, which is a Belgian sheepdog, Belgian shepherd, which is a Canis familiaris"]}, "229": {"node_name": "jersey, T-shirt, tee shirt", "parent_names": ["shirt"], "child_names": [], "candidate_sentences": ["a jersey, T-shirt, tee shirt, which is a shirt"]}, "230": {"node_name": "koala, koala bear, kangaroo bear, native bear, Phascolarctos cinereus", "parent_names": ["phalanger", "opossum", "possum"], "child_names": [], "candidate_sentences": ["a koala, koala bear, kangaroo bear, native bear, Phascolarctos cinereus, which is a phalanger", "a koala, koala bear, kangaroo bear, native bear, Phascolarctos cinereus, which is a opossum", "a koala, koala bear, kangaroo bear, native bear, Phascolarctos cinereus, which is a possum"]}, "231": {"node_name": "patas, hussar monkey, Erythrocebus patas", "parent_names": ["monkey"], "child_names": [], "candidate_sentences": ["a patas, hussar monkey, Erythrocebus patas, which is a monkey"]}, "232": {"node_name": "jeep, landrover", "parent_names": ["car", "auto", "automobile", "machine", "motorcar"], "child_names": [], "candidate_sentences": ["a jeep, landrover, which is a car", "a jeep, landrover, which is a auto", "a jeep, landrover, which is a automobile", "a jeep, landrover, which is a machine", "a jeep, landrover, which is a motorcar"]}, "233": {"node_name": "agaric", "parent_names": ["mushroom"], "child_names": [], "candidate_sentences": ["a agaric, which is a mushroom"]}, "234": {"node_name": "violin, fiddle", "parent_names": ["stringed instrument"], "child_names": [], "candidate_sentences": ["a violin, fiddle, which is a stringed instrument"]}, "235": {"node_name": "American alligator, Alligator mississipiensis", "parent_names": ["alligator", "gator"], "child_names": [], "candidate_sentences": ["a American alligator, Alligator mississipiensis, which is a alligator", "a American alligator, Alligator mississipiensis, which is a gator"]}, "236": {"node_name": "beacon, lighthouse, beacon light, pharos", "parent_names": ["tower"], "child_names": [], "candidate_sentences": ["a beacon, lighthouse, beacon light, pharos, which is a tower"]}, "237": {"node_name": "egret", "parent_names": ["heron"], "child_names": ["American egret", "great white heron", "Egretta albus"], "candidate_sentences": ["a American egret, which is a egret, which is a heron", "a great white heron, which is a egret, which is a heron", "a Egretta albus, which is a egret, which is a heron"]}, "238": {"node_name": "spotted salamander, Ambystoma maculatum", "parent_names": ["salamander"], "child_names": [], "candidate_sentences": ["a spotted salamander, Ambystoma maculatum, which is a salamander"]}, "239": {"node_name": "garden spider, Aranea diademata", "parent_names": ["spider"], "child_names": [], "candidate_sentences": ["a garden spider, <PERSON><PERSON> diademata, which is a spider"]}, "240": {"node_name": "lycaenid, lycaenid butterfly", "parent_names": ["butterfly"], "child_names": [], "candidate_sentences": ["a lycaenid, lycaenid butterfly, which is a butterfly"]}, "241": {"node_name": "Chesapeake Bay retriever", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Chesapeake Bay retriever, which is a dog", "a Chesapeake Bay retriever, which is a domestic dog", "a Chesapeake Bay retriever, which is a Canis familiaris"]}, "242": {"node_name": "ski mask", "parent_names": ["face mask"], "child_names": [], "candidate_sentences": ["a ski mask, which is a face mask"]}, "243": {"node_name": "red fox, Vulpes vulpes", "parent_names": ["fox"], "child_names": [], "candidate_sentences": ["a red fox, <PERSON><PERSON><PERSON> vulpes, which is a fox"]}, "244": {"node_name": "hand-held computer, hand-held microcomputer", "parent_names": ["digital computer"], "child_names": [], "candidate_sentences": ["a hand-held computer, hand-held microcomputer, which is a digital computer"]}, "245": {"node_name": "fox squirrel, eastern fox squirrel, Sciurus niger", "parent_names": ["squirrel"], "child_names": [], "candidate_sentences": ["a fox squirrel, eastern fox squirrel, Sciurus niger, which is a squirrel"]}, "246": {"node_name": "mud turtle", "parent_names": ["turtle"], "child_names": [], "candidate_sentences": ["a mud turtle, which is a turtle"]}, "247": {"node_name": "quill, quill pen", "parent_names": ["pen"], "child_names": [], "candidate_sentences": ["a quill, quill pen, which is a pen"]}, "248": {"node_name": "Norfolk terrier", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Norfolk terrier, which is a dog", "a Norfolk terrier, which is a domestic dog", "a Norfolk terrier, which is a Canis familiaris"]}, "249": {"node_name": "proboscis monkey, Nasalis larvatus", "parent_names": ["monkey"], "child_names": [], "candidate_sentences": ["a proboscis monkey, <PERSON><PERSON><PERSON> larvatus, which is a monkey"]}, "250": {"node_name": "head cabbage", "parent_names": ["cabbage", "chou"], "child_names": [], "candidate_sentences": ["a head cabbage, which is a cabbage", "a head cabbage, which is a chou"]}, "251": {"node_name": "tarantula", "parent_names": ["spider"], "child_names": [], "candidate_sentences": ["a tarantula, which is a spider"]}, "252": {"node_name": "ox", "parent_names": ["cattle", "cows", "kine", "oxen", "Bos taurus"], "child_names": [], "candidate_sentences": ["a ox, which is a cattle", "a ox, which is a cows", "a ox, which is a kine", "a ox, which is a oxen", "a ox, which is a Bos taurus"]}, "253": {"node_name": "ballpoint, ballpoint pen, ballpen, Biro", "parent_names": ["pen"], "child_names": [], "candidate_sentences": ["a ballpoint, ballpoint pen, ballpen, Biro, which is a pen"]}, "254": {"node_name": "iguanid, iguanid lizard", "parent_names": ["lizard"], "child_names": ["common iguana", "iguana", "Iguana iguana", "American chameleon", "anole", "An<PERSON> carolinensis"], "candidate_sentences": ["a common iguana, which is a iguanid, iguanid lizard, which is a lizard", "a iguana, which is a iguanid, iguanid lizard, which is a lizard", "a Iguana iguana, which is a iguanid, iguanid lizard, which is a lizard", "a American chameleon, which is a iguanid, iguanid lizard, which is a lizard", "a anole, which is a iguanid, iguanid lizard, which is a lizard", "a Anolis carolinensis, which is a iguanid, iguanid lizard, which is a lizard"]}, "255": {"node_name": "coyote, prairie wolf, brush wolf, Canis latrans", "parent_names": ["wolf"], "child_names": [], "candidate_sentences": ["a coyote, prairie wolf, brush wolf, Canis latrans, which is a wolf"]}, "256": {"node_name": "baboon", "parent_names": ["monkey"], "child_names": [], "candidate_sentences": ["a baboon, which is a monkey"]}, "257": {"node_name": "gecko", "parent_names": ["lizard"], "child_names": ["banded gecko"], "candidate_sentences": ["a banded gecko, which is a gecko, which is a lizard"]}, "258": {"node_name": "<PERSON><PERSON><PERSON>, Hungarian pointer", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a vizsla, Hungarian pointer, which is a dog", "a vizsla, Hungarian pointer, which is a domestic dog", "a vizsla, Hungarian pointer, which is a Canis familiaris"]}, "259": {"node_name": "briard", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a briard, which is a dog", "a briard, which is a domestic dog", "a briard, which is a Canis familiaris"]}, "260": {"node_name": "great white shark, white shark, man-eater, man-eating shark, Carcharodon carcharias", "parent_names": ["shark"], "child_names": [], "candidate_sentences": ["a great white shark, white shark, man-eater, man-eating shark, Carcharodon carcharias, which is a shark"]}, "261": {"node_name": "Chihuahua", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Chihuahua, which is a dog", "a Chihuahua, which is a domestic dog", "a Chihuahua, which is a Canis familiaris"]}, "262": {"node_name": "shower curtain", "parent_names": ["curtain", "drape", "drapery", "mantle", "pall"], "child_names": [], "candidate_sentences": ["a shower curtain, which is a curtain", "a shower curtain, which is a drape", "a shower curtain, which is a drapery", "a shower curtain, which is a mantle", "a shower curtain, which is a pall"]}, "263": {"node_name": "fireboat", "parent_names": ["boat"], "child_names": [], "candidate_sentences": ["a fireboat, which is a boat"]}, "264": {"node_name": "toy spaniel", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": ["Blenheim spaniel", "papillon"], "candidate_sentences": ["a Blenheim spaniel, which is a toy spaniel, which is a dog", "a papillon, which is a toy spaniel, which is a dog", "a Blenheim spaniel, which is a toy spaniel, which is a domestic dog", "a papillon, which is a toy spaniel, which is a domestic dog", "a Blenheim spaniel, which is a toy spaniel, which is a Canis familiaris", "a papillon, which is a toy spaniel, which is a Canis familiaris"]}, "265": {"node_name": "lab coat, laboratory coat", "parent_names": ["coat"], "child_names": [], "candidate_sentences": ["a lab coat, laboratory coat, which is a coat"]}, "266": {"node_name": "cellular telephone, cellular phone, cellphone, cell, mobile phone", "parent_names": ["telephone", "phone", "telephone set"], "child_names": [], "candidate_sentences": ["a cellular telephone, cellular phone, cellphone, cell, mobile phone, which is a telephone", "a cellular telephone, cellular phone, cellphone, cell, mobile phone, which is a phone", "a cellular telephone, cellular phone, cellphone, cell, mobile phone, which is a telephone set"]}, "267": {"node_name": "Boston bull, Boston terrier", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Boston bull, Boston terrier, which is a dog", "a Boston bull, Boston terrier, which is a domestic dog", "a Boston bull, Boston terrier, which is a Canis familiaris"]}, "268": {"node_name": "pipe", "parent_names": ["wind instrument", "wind"], "child_names": ["panpipe", "pandean pipe", "syrinx"], "candidate_sentences": ["a panpipe, which is a pipe, which is a wind instrument", "a pandean pipe, which is a pipe, which is a wind instrument", "a syrinx, which is a pipe, which is a wind instrument", "a panpipe, which is a pipe, which is a wind", "a pandean pipe, which is a pipe, which is a wind", "a syrinx, which is a pipe, which is a wind"]}, "269": {"node_name": "fur coat", "parent_names": ["coat"], "child_names": [], "candidate_sentences": ["a fur coat, which is a coat"]}, "270": {"node_name": "bench", "parent_names": ["sofa", "couch", "lounge"], "child_names": ["park bench"], "candidate_sentences": ["a park bench, which is a bench, which is a sofa", "a park bench, which is a bench, which is a couch", "a park bench, which is a bench, which is a lounge"]}, "271": {"node_name": "harp", "parent_names": ["stringed instrument"], "child_names": [], "candidate_sentences": ["a harp, which is a stringed instrument"]}, "272": {"node_name": "croquet ball", "parent_names": ["ball"], "child_names": [], "candidate_sentences": ["a croquet ball, which is a ball"]}, "273": {"node_name": "kelpie", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a kelpie, which is a dog", "a kelpie, which is a domestic dog", "a kelpie, which is a Canis familiaris"]}, "274": {"node_name": "chainlink fence", "parent_names": ["fence", "fencing"], "child_names": [], "candidate_sentences": ["a chainlink fence, which is a fence", "a chainlink fence, which is a fencing"]}, "275": {"node_name": "sloth bear, <PERSON><PERSON><PERSON> ursinus, Ursus ursinus", "parent_names": ["bear"], "child_names": [], "candidate_sentences": ["a sloth bear, <PERSON><PERSON><PERSON> ursinus, <PERSON>rsus ursinus, which is a bear"]}, "276": {"node_name": "spaghetti sauce, pasta sauce", "parent_names": ["sauce"], "child_names": ["carbonara"], "candidate_sentences": ["a carbonara, which is a spaghetti sauce, pasta sauce, which is a sauce"]}, "277": {"node_name": "<PERSON>shank, Tringa totanus", "parent_names": ["sandpiper"], "child_names": [], "candidate_sentences": ["a redshank, <PERSON>nga totanus, which is a sandpiper"]}, "278": {"node_name": "Shetland sheepdog, Shetland sheep dog, Shetland", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Shetland sheepdog, Shetland sheep dog, Shetland, which is a dog", "a Shetland sheepdog, Shetland sheep dog, Shetland, which is a domestic dog", "a Shetland sheepdog, Shetland sheep dog, Shetland, which is a Canis familiaris"]}, "279": {"node_name": "black swan, Cygnus atratus", "parent_names": ["swan"], "child_names": [], "candidate_sentences": ["a black swan, <PERSON><PERSON>us atratus, which is a swan"]}, "280": {"node_name": "suspension bridge", "parent_names": ["bridge", "span"], "child_names": [], "candidate_sentences": ["a suspension bridge, which is a bridge", "a suspension bridge, which is a span"]}, "281": {"node_name": "black grouse", "parent_names": ["grouse"], "child_names": [], "candidate_sentences": ["a black grouse, which is a grouse"]}, "282": {"node_name": "theater curtain, theatre curtain", "parent_names": ["curtain", "drape", "drapery", "mantle", "pall"], "child_names": [], "candidate_sentences": ["a theater curtain, theatre curtain, which is a curtain", "a theater curtain, theatre curtain, which is a drape", "a theater curtain, theatre curtain, which is a drapery", "a theater curtain, theatre curtain, which is a mantle", "a theater curtain, theatre curtain, which is a pall"]}, "283": {"node_name": "partridge", "parent_names": ["<PERSON><PERSON><PERSON><PERSON>"], "child_names": [], "candidate_sentences": ["a partridge, which is a phasianid"]}, "284": {"node_name": "Great Dane", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Great Dane, which is a dog", "a Great Dane, which is a domestic dog", "a Great Dane, which is a Canis familiaris"]}, "285": {"node_name": "curly-coated retriever", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a curly-coated retriever, which is a dog", "a curly-coated retriever, which is a domestic dog", "a curly-coated retriever, which is a Canis familiaris"]}, "286": {"node_name": "long-horned beetle, longicorn, longicorn beetle", "parent_names": ["beetle"], "child_names": [], "candidate_sentences": ["a long-horned beetle, longicorn, longicorn beetle, which is a beetle"]}, "287": {"node_name": "red wolf, maned wolf, Canis rufus, Canis niger", "parent_names": ["wolf"], "child_names": [], "candidate_sentences": ["a red wolf, maned wolf, Canis rufus, Canis niger, which is a wolf"]}, "288": {"node_name": "mountain sheep", "parent_names": ["wild sheep"], "child_names": ["bighorn", "bighorn sheep", "cimarron", "Rocky Mountain bighorn", "Rocky Mountain sheep", "<PERSON><PERSON> canadensis"], "candidate_sentences": ["a bighorn, which is a mountain sheep, which is a wild sheep", "a bighorn sheep, which is a mountain sheep, which is a wild sheep", "a cimarron, which is a mountain sheep, which is a wild sheep", "a Rocky Mountain bighorn, which is a mountain sheep, which is a wild sheep", "a Rocky Mountain sheep, which is a mountain sheep, which is a wild sheep", "a Ovis canadensis, which is a mountain sheep, which is a wild sheep"]}, "289": {"node_name": "lionfish", "parent_names": ["scorpaenid", "scorpaenid fish"], "child_names": [], "candidate_sentences": ["a lionfish, which is a scorpaenid", "a lionfish, which is a scorpaenid fish"]}, "290": {"node_name": "agamid, agamid lizard", "parent_names": ["lizard"], "child_names": ["agama", "frilled lizard", "Chlamydosaurus kingi"], "candidate_sentences": ["a agama, which is a agamid, agamid lizard, which is a lizard", "a frilled lizard, which is a agamid, agamid lizard, which is a lizard", "a Chlamydosaurus kingi, which is a agamid, agamid lizard, which is a lizard"]}, "291": {"node_name": "trombone", "parent_names": ["wind instrument", "wind"], "child_names": [], "candidate_sentences": ["a trombone, which is a wind instrument", "a trombone, which is a wind"]}, "292": {"node_name": "eggnog", "parent_names": ["punch"], "child_names": [], "candidate_sentences": ["a eggnog, which is a punch"]}, "293": {"node_name": "reflex camera", "parent_names": ["camera", "photographic camera"], "child_names": [], "candidate_sentences": ["a reflex camera, which is a camera", "a reflex camera, which is a photographic camera"]}, "294": {"node_name": "pistol, handgun, side arm, shooting iron", "parent_names": ["firearm", "piece", "small-arm"], "child_names": ["revolver", "six-gun", "six-shooter"], "candidate_sentences": ["a revolver, which is a pistol, handgun, side arm, shooting iron, which is a firearm", "a six-gun, which is a pistol, handgun, side arm, shooting iron, which is a firearm", "a six-shooter, which is a pistol, handgun, side arm, shooting iron, which is a firearm", "a revolver, which is a pistol, handgun, side arm, shooting iron, which is a piece", "a six-gun, which is a pistol, handgun, side arm, shooting iron, which is a piece", "a six-shooter, which is a pistol, handgun, side arm, shooting iron, which is a piece", "a revolver, which is a pistol, handgun, side arm, shooting iron, which is a small-arm", "a six-gun, which is a pistol, handgun, side arm, shooting iron, which is a small-arm", "a six-shooter, which is a pistol, handgun, side arm, shooting iron, which is a small-arm"]}, "295": {"node_name": "mountain bike, all-terrain bike, off-roader", "parent_names": ["bicycle", "bike", "wheel", "cycle"], "child_names": [], "candidate_sentences": ["a mountain bike, all-terrain bike, off-roader, which is a bicycle", "a mountain bike, all-terrain bike, off-roader, which is a bike", "a mountain bike, all-terrain bike, off-roader, which is a wheel", "a mountain bike, all-terrain bike, off-roader, which is a cycle"]}, "296": {"node_name": "megalith, megalithic structure", "parent_names": ["memorial", "monument"], "child_names": [], "candidate_sentences": ["a megalith, megalithic structure, which is a memorial", "a megalith, megalithic structure, which is a monument"]}, "297": {"node_name": "marimba, xylophone", "parent_names": ["percussion instrument", "percussive instrument"], "child_names": [], "candidate_sentences": ["a marimba, xylophone, which is a percussion instrument", "a marimba, xylophone, which is a percussive instrument"]}, "298": {"node_name": "speedboat", "parent_names": ["boat"], "child_names": [], "candidate_sentences": ["a speedboat, which is a boat"]}, "299": {"node_name": "German short-haired pointer", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a German short-haired pointer, which is a dog", "a German short-haired pointer, which is a domestic dog", "a German short-haired pointer, which is a Canis familiaris"]}, "300": {"node_name": "bow tie, bow-tie, bowtie", "parent_names": ["necktie", "tie"], "child_names": [], "candidate_sentences": ["a bow tie, bow-tie, bowtie, which is a necktie", "a bow tie, bow-tie, bowtie, which is a tie"]}, "301": {"node_name": "cab, hack, taxi, taxicab", "parent_names": ["car", "auto", "automobile", "machine", "motorcar"], "child_names": [], "candidate_sentences": ["a cab, hack, taxi, taxicab, which is a car", "a cab, hack, taxi, taxicab, which is a auto", "a cab, hack, taxi, taxicab, which is a automobile", "a cab, hack, taxi, taxicab, which is a machine", "a cab, hack, taxi, taxicab, which is a motorcar"]}, "302": {"node_name": "bottle opener", "parent_names": ["opener"], "child_names": ["corkscrew", "bottle screw"], "candidate_sentences": ["a corkscrew, which is a bottle opener, which is a opener", "a bottle screw, which is a bottle opener, which is a opener"]}, "303": {"node_name": "merganser, fish duck, sawbill, sheldrake", "parent_names": ["duck"], "child_names": ["red-breasted merganser", "Mergus serrator"], "candidate_sentences": ["a red-breasted merganser, which is a merganser, fish duck, sawbill, sheldrake, which is a duck", "a Mergus serrator, which is a merganser, fish duck, sawbill, sheldrake, which is a duck"]}, "304": {"node_name": "grey fox, gray fox, Urocyon cinereoargenteus", "parent_names": ["fox"], "child_names": [], "candidate_sentences": ["a grey fox, gray fox, Urocyon cinereoargenteus, which is a fox"]}, "305": {"node_name": "yawl", "parent_names": ["boat"], "child_names": [], "candidate_sentences": ["a yawl, which is a boat"]}, "306": {"node_name": "holster", "parent_names": ["sheath"], "child_names": [], "candidate_sentences": ["a holster, which is a sheath"]}, "307": {"node_name": "pajama, pyjama, pj's, jammies", "parent_names": ["nightwear", "sleepwear", "nightclothes"], "child_names": [], "candidate_sentences": ["a pajama, pyjama, pj's, jammies, which is a nightwear", "a pajama, pyjama, pj's, jammies, which is a sleepwear", "a pajama, pyjama, pj's, jammies, which is a nightclothes"]}, "308": {"node_name": "anemone fish", "parent_names": ["damselfish", "demoiselle"], "child_names": [], "candidate_sentences": ["a anemone fish, which is a damselfish", "a anemone fish, which is a demoiselle"]}, "309": {"node_name": "lumbermill, sawmill", "parent_names": ["factory", "mill", "manufacturing plant", "manufactory"], "child_names": [], "candidate_sentences": ["a lumbermill, sawmill, which is a factory", "a lumbermill, sawmill, which is a mill", "a lumbermill, sawmill, which is a manufacturing plant", "a lumbermill, sawmill, which is a manufactory"]}, "310": {"node_name": "cliff dwelling", "parent_names": ["dwelling", "home", "domicile", "abode", "habitation", "dwelling house"], "child_names": [], "candidate_sentences": ["a cliff dwelling, which is a dwelling", "a cliff dwelling, which is a home", "a cliff dwelling, which is a domicile", "a cliff dwelling, which is a abode", "a cliff dwelling, which is a habitation", "a cliff dwelling, which is a dwelling house"]}, "311": {"node_name": "German shepherd, German shepherd dog, German police dog, alsatian", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a German shepherd, German shepherd dog, German police dog, alsatian, which is a dog", "a German shepherd, German shepherd dog, German police dog, alsatian, which is a domestic dog", "a German shepherd, German shepherd dog, German police dog, alsatian, which is a Canis familiaris"]}, "312": {"node_name": "hotdog, hot dog, red hot", "parent_names": ["sandwich"], "child_names": [], "candidate_sentences": ["a hotdog, hot dog, red hot, which is a sandwich"]}, "313": {"node_name": "brain coral", "parent_names": ["coral"], "child_names": [], "candidate_sentences": ["a brain coral, which is a coral"]}, "314": {"node_name": "dingo, warrigal, warragal, Canis dingo", "parent_names": ["wild dog"], "child_names": [], "candidate_sentences": ["a dingo, warrigal, warragal, Canis dingo, which is a wild dog"]}, "315": {"node_name": "minibike, motorbike", "parent_names": ["motorcycle", "bike"], "child_names": ["moped"], "candidate_sentences": ["a moped, which is a minibike, motorbike, which is a motorcycle", "a moped, which is a minibike, motorbike, which is a bike"]}, "316": {"node_name": "ocarina, sweet potato", "parent_names": ["wind instrument", "wind"], "child_names": [], "candidate_sentences": ["a ocarina, sweet potato, which is a wind instrument", "a ocarina, sweet potato, which is a wind"]}, "317": {"node_name": "corgi, Welsh corgi", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": ["Pembroke", "Pembroke Welsh corgi", "<PERSON>igan", "Cardigan Welsh corgi"], "candidate_sentences": ["a Pembroke, which is a corgi, Welsh corgi, which is a dog", "a Pembroke Welsh corgi, which is a corgi, Welsh corgi, which is a dog", "a Cardigan, which is a corgi, Welsh corgi, which is a dog", "a Cardigan Welsh corgi, which is a corgi, Welsh corgi, which is a dog", "a Pembroke, which is a corgi, Welsh corgi, which is a domestic dog", "a Pembroke Welsh corgi, which is a corgi, Welsh corgi, which is a domestic dog", "a Cardigan, which is a corgi, Welsh corgi, which is a domestic dog", "a Cardigan Welsh corgi, which is a corgi, Welsh corgi, which is a domestic dog", "a Pembroke, which is a corgi, Welsh corgi, which is a Canis familiaris", "a Pembroke Welsh corgi, which is a corgi, Welsh corgi, which is a Canis familiaris", "a Cardigan, which is a corgi, Welsh corgi, which is a Canis familiaris", "a Cardigan Welsh corgi, which is a corgi, Welsh corgi, which is a Canis familiaris"]}, "318": {"node_name": "ambulance", "parent_names": ["car", "auto", "automobile", "machine", "motorcar"], "child_names": [], "candidate_sentences": ["a ambulance, which is a car", "a ambulance, which is a auto", "a ambulance, which is a automobile", "a ambulance, which is a machine", "a ambulance, which is a motorcar"]}, "319": {"node_name": "malamute, malemute, Alaskan malamute", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a malamute, malemute, Alaskan malamute, which is a dog", "a malamute, malemute, Alaskan malamute, which is a domestic dog", "a malamute, malemute, Alaskan malamute, which is a Canis familiaris"]}, "320": {"node_name": "gibbon, Hylobates lar", "parent_names": ["ape"], "child_names": [], "candidate_sentences": ["a gibbon, Hylobates lar, which is a ape"]}, "321": {"node_name": "box turtle, box tortoise", "parent_names": ["turtle"], "child_names": [], "candidate_sentences": ["a box turtle, box tortoise, which is a turtle"]}, "322": {"node_name": "swimming trunks, bathing trunks", "parent_names": ["swimsuit", "swimwear", "bathing suit", "swimming costume", "bathing costume"], "child_names": [], "candidate_sentences": ["a swimming trunks, bathing trunks, which is a swimsuit", "a swimming trunks, bathing trunks, which is a swimwear", "a swimming trunks, bathing trunks, which is a bathing suit", "a swimming trunks, bathing trunks, which is a swimming costume", "a swimming trunks, bathing trunks, which is a bathing costume"]}, "323": {"node_name": "sax, saxophone", "parent_names": ["wind instrument", "wind"], "child_names": [], "candidate_sentences": ["a sax, saxophone, which is a wind instrument", "a sax, saxophone, which is a wind"]}, "324": {"node_name": "freight car", "parent_names": ["car", "railcar", "railway car", "railroad car"], "child_names": [], "candidate_sentences": ["a freight car, which is a car", "a freight car, which is a railcar", "a freight car, which is a railway car", "a freight car, which is a railroad car"]}, "325": {"node_name": "caldron, cauldron", "parent_names": ["pot"], "child_names": [], "candidate_sentences": ["a caldron, cauldron, which is a pot"]}, "326": {"node_name": "can opener, tin opener", "parent_names": ["opener"], "child_names": [], "candidate_sentences": ["a can opener, tin opener, which is a opener"]}, "327": {"node_name": "safety pin", "parent_names": ["pin"], "child_names": [], "candidate_sentences": ["a safety pin, which is a pin"]}, "328": {"node_name": "dome", "parent_names": ["roof"], "child_names": [], "candidate_sentences": ["a dome, which is a roof"]}, "329": {"node_name": "bikini, two-piece", "parent_names": ["swimsuit", "swimwear", "bathing suit", "swimming costume", "bathing costume"], "child_names": [], "candidate_sentences": ["a bikini, two-piece, which is a swimsuit", "a bikini, two-piece, which is a swimwear", "a bikini, two-piece, which is a bathing suit", "a bikini, two-piece, which is a swimming costume", "a bikini, two-piece, which is a bathing costume"]}, "330": {"node_name": "guacamole", "parent_names": ["dip"], "child_names": [], "candidate_sentences": ["a guacamole, which is a dip"]}, "331": {"node_name": "limousine, limo", "parent_names": ["car", "auto", "automobile", "machine", "motorcar"], "child_names": [], "candidate_sentences": ["a limousine, limo, which is a car", "a limousine, limo, which is a auto", "a limousine, limo, which is a automobile", "a limousine, limo, which is a machine", "a limousine, limo, which is a motorcar"]}, "332": {"node_name": "obelisk", "parent_names": ["column", "pillar"], "child_names": [], "candidate_sentences": ["a obelisk, which is a column", "a obelisk, which is a pillar"]}, "333": {"node_name": "West Highland white terrier", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a West Highland white terrier, which is a dog", "a West Highland white terrier, which is a domestic dog", "a West Highland white terrier, which is a Canis familiaris"]}, "334": {"node_name": "collie", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a collie, which is a dog", "a collie, which is a domestic dog", "a collie, which is a Canis familiaris"]}, "335": {"node_name": "Bernese mountain dog", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Bernese mountain dog, which is a dog", "a Bernese mountain dog, which is a domestic dog", "a Bernese mountain dog, which is a Canis familiaris"]}, "336": {"node_name": "cobra", "parent_names": ["snake", "serpent", "ophidian"], "child_names": ["Indian cobra", "<PERSON>ja naja"], "candidate_sentences": ["a Indian cobra, which is a cobra, which is a snake", "a Naja naja, which is a cobra, which is a snake", "a Indian cobra, which is a cobra, which is a serpent", "a Naja naja, which is a cobra, which is a serpent", "a Indian cobra, which is a cobra, which is a ophidian", "a Naja naja, which is a cobra, which is a ophidian"]}, "337": {"node_name": "water spaniel", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": ["Irish water spaniel"], "candidate_sentences": ["a Irish water spaniel, which is a water spaniel, which is a dog", "a Irish water spaniel, which is a water spaniel, which is a domestic dog", "a Irish water spaniel, which is a water spaniel, which is a Canis familiaris"]}, "338": {"node_name": "teapot", "parent_names": ["pot"], "child_names": [], "candidate_sentences": ["a teapot, which is a pot"]}, "339": {"node_name": "Brittany spaniel", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Brittany spaniel, which is a dog", "a Brittany spaniel, which is a domestic dog", "a Brittany spaniel, which is a Canis familiaris"]}, "340": {"node_name": "python", "parent_names": ["snake", "serpent", "ophidian"], "child_names": ["rock python", "rock snake", "Python sebae"], "candidate_sentences": ["a rock python, which is a python, which is a snake", "a rock snake, which is a python, which is a snake", "a Python sebae, which is a python, which is a snake", "a rock python, which is a python, which is a serpent", "a rock snake, which is a python, which is a serpent", "a Python sebae, which is a python, which is a serpent", "a rock python, which is a python, which is a ophidian", "a rock snake, which is a python, which is a ophidian", "a Python sebae, which is a python, which is a ophidian"]}, "341": {"node_name": "bicycle-built-for-two, tandem bicycle, tandem", "parent_names": ["bicycle", "bike", "wheel", "cycle"], "child_names": [], "candidate_sentences": ["a bicycle-built-for-two, tandem bicycle, tandem, which is a bicycle", "a bicycle-built-for-two, tandem bicycle, tandem, which is a bike", "a bicycle-built-for-two, tandem bicycle, tandem, which is a wheel", "a bicycle-built-for-two, tandem bicycle, tandem, which is a cycle"]}, "342": {"node_name": "komondor", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a komondor, which is a dog", "a komondor, which is a domestic dog", "a komondor, which is a Canis familiaris"]}, "343": {"node_name": "recreational vehicle, RV, R.V.", "parent_names": ["bus", "autobus", "coach", "charabanc", "double-decker", "jitney", "motorbus", "motorcoach", "omnibus", "passenger vehicle"], "child_names": [], "candidate_sentences": ["a recreational vehicle, RV, R.V., which is a bus", "a recreational vehicle, RV, R.V., which is a autobus", "a recreational vehicle, RV, R.V., which is a coach", "a recreational vehicle, RV, R.V., which is a charabanc", "a recreational vehicle, RV, R.V., which is a double-decker", "a recreational vehicle, RV, R.V., which is a jitney", "a recreational vehicle, RV, R.V., which is a motorbus", "a recreational vehicle, RV, R.V., which is a motorcoach", "a recreational vehicle, RV, R.V., which is a omnibus", "a recreational vehicle, RV, R.V., which is a passenger vehicle"]}, "344": {"node_name": "soup bowl", "parent_names": ["bowl"], "child_names": [], "candidate_sentences": ["a soup bowl, which is a bowl"]}, "345": {"node_name": "Old English sheepdog, bobtail", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Old English sheepdog, bobtail, which is a dog", "a Old English sheepdog, bobtail, which is a domestic dog", "a Old English sheepdog, bobtail, which is a Canis familiaris"]}, "346": {"node_name": "Japanese spaniel", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Japanese spaniel, which is a dog", "a Japanese spaniel, which is a domestic dog", "a Japanese spaniel, which is a Canis familiaris"]}, "347": {"node_name": "lory", "parent_names": ["parrot"], "child_names": ["lorikeet"], "candidate_sentences": ["a lorikeet, which is a lory, which is a parrot"]}, "348": {"node_name": "Lhasa, Lhasa apso", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Lhasa, Lhasa apso, which is a dog", "a Lhasa, Lhasa apso, which is a domestic dog", "a Lhasa, Lhasa apso, which is a Canis familiaris"]}, "349": {"node_name": "sweet pepper", "parent_names": ["pepper"], "child_names": ["bell pepper"], "candidate_sentences": ["a bell pepper, which is a sweet pepper, which is a pepper"]}, "350": {"node_name": "poodle, poodle dog", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": ["toy poodle", "miniature poodle", "standard poodle"], "candidate_sentences": ["a toy poodle, which is a poodle, poodle dog, which is a dog", "a miniature poodle, which is a poodle, poodle dog, which is a dog", "a standard poodle, which is a poodle, poodle dog, which is a dog", "a toy poodle, which is a poodle, poodle dog, which is a domestic dog", "a miniature poodle, which is a poodle, poodle dog, which is a domestic dog", "a standard poodle, which is a poodle, poodle dog, which is a domestic dog", "a toy poodle, which is a poodle, poodle dog, which is a Canis familiaris", "a miniature poodle, which is a poodle, poodle dog, which is a Canis familiaris", "a standard poodle, which is a poodle, poodle dog, which is a Canis familiaris"]}, "351": {"node_name": "palace", "parent_names": ["dwelling", "home", "domicile", "abode", "habitation", "dwelling house"], "child_names": [], "candidate_sentences": ["a palace, which is a dwelling", "a palace, which is a home", "a palace, which is a domicile", "a palace, which is a abode", "a palace, which is a habitation", "a palace, which is a dwelling house"]}, "352": {"node_name": "thatch, thatched roof", "parent_names": ["roof"], "child_names": [], "candidate_sentences": ["a thatch, thatched roof, which is a roof"]}, "353": {"node_name": "cinema, movie theater, movie theatre, movie house, picture palace", "parent_names": ["theater", "theatre", "house"], "child_names": [], "candidate_sentences": ["a cinema, movie theater, movie theatre, movie house, picture palace, which is a theater", "a cinema, movie theater, movie theatre, movie house, picture palace, which is a theatre", "a cinema, movie theater, movie theatre, movie house, picture palace, which is a house"]}, "354": {"node_name": "bearskin, busby, shako", "parent_names": ["hat", "chapeau", "lid"], "child_names": [], "candidate_sentences": ["a bearskin, busby, shako, which is a hat", "a bearskin, busby, shako, which is a chapeau", "a bearskin, busby, shako, which is a lid"]}, "355": {"node_name": "banjo", "parent_names": ["stringed instrument"], "child_names": [], "candidate_sentences": ["a banjo, which is a stringed instrument"]}, "356": {"node_name": "hartebeest", "parent_names": ["antelope"], "child_names": [], "candidate_sentences": ["a hartebeest, which is a antelope"]}, "357": {"node_name": "<PERSON><PERSON>", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Leonberg, which is a dog", "a Leonberg, which is a domestic dog", "a Leonberg, which is a Canis familiaris"]}, "358": {"node_name": "ringneck snake, ring-necked snake, ring snake", "parent_names": ["snake", "serpent", "ophidian"], "child_names": [], "candidate_sentences": ["a ringneck snake, ring-necked snake, ring snake, which is a snake", "a ringneck snake, ring-necked snake, ring snake, which is a serpent", "a ringneck snake, ring-necked snake, ring snake, which is a ophidian"]}, "359": {"node_name": "miniature pinscher", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a miniature pinscher, which is a dog", "a miniature pinscher, which is a domestic dog", "a miniature pinscher, which is a Canis familiaris"]}, "360": {"node_name": "mask", "parent_names": ["face mask"], "child_names": [], "candidate_sentences": ["a mask, which is a face mask"]}, "361": {"node_name": "cloak", "parent_names": ["coat"], "child_names": [], "candidate_sentences": ["a cloak, which is a coat"]}, "362": {"node_name": "maraca", "parent_names": ["percussion instrument", "percussive instrument"], "child_names": [], "candidate_sentences": ["a maraca, which is a percussion instrument", "a maraca, which is a percussive instrument"]}, "363": {"node_name": "king snake, kingsnake", "parent_names": ["snake", "serpent", "ophidian"], "child_names": [], "candidate_sentences": ["a king snake, kingsnake, which is a snake", "a king snake, kingsnake, which is a serpent", "a king snake, kingsnake, which is a ophidian"]}, "364": {"node_name": "anguid lizard", "parent_names": ["lizard"], "child_names": ["alligator lizard"], "candidate_sentences": ["a alligator lizard, which is a anguid lizard, which is a lizard"]}, "365": {"node_name": "toy terrier", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a toy terrier, which is a dog", "a toy terrier, which is a domestic dog", "a toy terrier, which is a Canis familiaris"]}, "366": {"node_name": "cocker spaniel, English cocker spaniel, cocker", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a cocker spaniel, English cocker spaniel, cocker, which is a dog", "a cocker spaniel, English cocker spaniel, cocker, which is a domestic dog", "a cocker spaniel, English cocker spaniel, cocker, which is a Canis familiaris"]}, "367": {"node_name": "gong, tam-tam", "parent_names": ["percussion instrument", "percussive instrument"], "child_names": [], "candidate_sentences": ["a gong, tam-tam, which is a percussion instrument", "a gong, tam-tam, which is a percussive instrument"]}, "368": {"node_name": "Bedlington terrier", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Bedlington terrier, which is a dog", "a Bedlington terrier, which is a domestic dog", "a Bedlington terrier, which is a Canis familiaris"]}, "369": {"node_name": "Dutch oven", "parent_names": ["pot"], "child_names": [], "candidate_sentences": ["a Dutch oven, which is a pot"]}, "370": {"node_name": "barbershop", "parent_names": ["mercantile establishment", "retail store", "sales outlet", "outlet"], "child_names": [], "candidate_sentences": ["a barbershop, which is a mercantile establishment", "a barbershop, which is a retail store", "a barbershop, which is a sales outlet", "a barbershop, which is a outlet"]}, "371": {"node_name": "convertible", "parent_names": ["car", "auto", "automobile", "machine", "motorcar"], "child_names": [], "candidate_sentences": ["a convertible, which is a car", "a convertible, which is a auto", "a convertible, which is a automobile", "a convertible, which is a machine", "a convertible, which is a motorcar"]}, "372": {"node_name": "wood rabbit, cottontail, cottontail rabbit", "parent_names": ["rabbit", "coney", "cony"], "child_names": [], "candidate_sentences": ["a wood rabbit, cottontail, cottontail rabbit, which is a rabbit", "a wood rabbit, cottontail, cottontail rabbit, which is a coney", "a wood rabbit, cottontail, cottontail rabbit, which is a cony"]}, "373": {"node_name": "doormat, welcome mat", "parent_names": ["mat"], "child_names": [], "candidate_sentences": ["a doormat, welcome mat, which is a mat"]}, "374": {"node_name": "basenji", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a basenji, which is a dog", "a basenji, which is a domestic dog", "a basenji, which is a Canis familiaris"]}, "375": {"node_name": "white stork, Ciconia ciconia", "parent_names": ["stork"], "child_names": [], "candidate_sentences": ["a white stork, Ciconia ciconia, which is a stork"]}, "376": {"node_name": "water snake", "parent_names": ["snake", "serpent", "ophidian"], "child_names": [], "candidate_sentences": ["a water snake, which is a snake", "a water snake, which is a serpent", "a water snake, which is a ophidian"]}, "377": {"node_name": "brown bear, bruin, Ursus arctos", "parent_names": ["bear"], "child_names": [], "candidate_sentences": ["a brown bear, bruin, Ursus arctos, which is a bear"]}, "378": {"node_name": "bathing cap, swimming cap", "parent_names": ["cap"], "child_names": [], "candidate_sentences": ["a bathing cap, swimming cap, which is a cap"]}, "379": {"node_name": "bullet train, bullet", "parent_names": ["passenger train"], "child_names": [], "candidate_sentences": ["a bullet train, bullet, which is a passenger train"]}, "380": {"node_name": "football helmet", "parent_names": ["helmet"], "child_names": [], "candidate_sentences": ["a football helmet, which is a helmet"]}, "381": {"node_name": "minivan", "parent_names": ["car", "auto", "automobile", "machine", "motorcar"], "child_names": [], "candidate_sentences": ["a minivan, which is a car", "a minivan, which is a auto", "a minivan, which is a automobile", "a minivan, which is a machine", "a minivan, which is a motorcar"]}, "382": {"node_name": "clock", "parent_names": ["timepiece", "timekeeper", "horologe"], "child_names": ["analog clock", "digital clock", "wall clock"], "candidate_sentences": ["a analog clock, which is a clock, which is a timepiece", "a digital clock, which is a clock, which is a timepiece", "a wall clock, which is a clock, which is a timepiece", "a analog clock, which is a clock, which is a timekeeper", "a digital clock, which is a clock, which is a timekeeper", "a wall clock, which is a clock, which is a timekeeper", "a analog clock, which is a clock, which is a horologe", "a digital clock, which is a clock, which is a horologe", "a wall clock, which is a clock, which is a horologe"]}, "383": {"node_name": "airliner", "parent_names": ["airplane", "aeroplane", "plane"], "child_names": [], "candidate_sentences": ["a airliner, which is a airplane", "a airliner, which is a aeroplane", "a airliner, which is a plane"]}, "384": {"node_name": "ruffed grouse, partridge, Bonasa umbellus", "parent_names": ["grouse"], "child_names": [], "candidate_sentences": ["a ruffed grouse, partridge, Bonasa umbellus, which is a grouse"]}, "385": {"node_name": "pickup, pickup truck", "parent_names": ["truck", "motortruck"], "child_names": [], "candidate_sentences": ["a pickup, pickup truck, which is a truck", "a pickup, pickup truck, which is a motortruck"]}, "386": {"node_name": "Yorkshire terrier", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Yorkshire terrier, which is a dog", "a Yorkshire terrier, which is a domestic dog", "a Yorkshire terrier, which is a Canis familiaris"]}, "387": {"node_name": "coucal", "parent_names": ["cuckoo"], "child_names": [], "candidate_sentences": ["a coucal, which is a cuckoo"]}, "388": {"node_name": "ringlet, ringlet butterfly", "parent_names": ["butterfly"], "child_names": [], "candidate_sentences": ["a ringlet, ringlet butterfly, which is a butterfly"]}, "389": {"node_name": "basset, basset hound", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a basset, basset hound, which is a dog", "a basset, basset hound, which is a domestic dog", "a basset, basset hound, which is a Canis familiaris"]}, "390": {"node_name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Pekinese, Pekingese, Pek<PERSON>, which is a dog", "a Pekinese, Pekingese, Peke, which is a domestic dog", "a Pekinese, Pekingese, Peke, which is a Canis familiaris"]}, "391": {"node_name": "Siberian husky", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Siberian husky, which is a dog", "a Siberian husky, which is a domestic dog", "a Siberian husky, which is a Canis familiaris"]}, "392": {"node_name": "ladybug, ladybeetle, lady beetle, ladybird, ladybird beetle", "parent_names": ["beetle"], "child_names": [], "candidate_sentences": ["a ladybug, ladybeetle, lady beetle, ladybird, ladybird beetle, which is a beetle"]}, "393": {"node_name": "Irish terrier", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Irish terrier, which is a dog", "a Irish terrier, which is a domestic dog", "a Irish terrier, which is a Canis familiaris"]}, "394": {"node_name": "dial telephone, dial phone", "parent_names": ["telephone", "phone", "telephone set"], "child_names": [], "candidate_sentences": ["a dial telephone, dial phone, which is a telephone", "a dial telephone, dial phone, which is a phone", "a dial telephone, dial phone, which is a telephone set"]}, "395": {"node_name": "trifle", "parent_names": ["pudding", "pud"], "child_names": [], "candidate_sentences": ["a trifle, which is a pudding", "a trifle, which is a pud"]}, "396": {"node_name": "king penguin, Aptenodytes patagonica", "parent_names": ["penguin"], "child_names": [], "candidate_sentences": ["a king penguin, Aptenodytes patagon<PERSON>, which is a penguin"]}, "397": {"node_name": "Australian terrier", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Australian terrier, which is a dog", "a Australian terrier, which is a domestic dog", "a Australian terrier, which is a Canis familiaris"]}, "398": {"node_name": "ping-pong ball", "parent_names": ["ball"], "child_names": [], "candidate_sentences": ["a ping-pong ball, which is a ball"]}, "399": {"node_name": "dumbbell", "parent_names": ["weight", "free weight", "exercising weight"], "child_names": [], "candidate_sentences": ["a dumbbell, which is a weight", "a dumbbell, which is a free weight", "a dumbbell, which is a exercising weight"]}, "400": {"node_name": "electric fan, blower", "parent_names": ["fan"], "child_names": [], "candidate_sentences": ["a electric fan, blower, which is a fan"]}, "401": {"node_name": "drake", "parent_names": ["duck"], "child_names": [], "candidate_sentences": ["a drake, which is a duck"]}, "402": {"node_name": "beer bottle", "parent_names": ["bottle"], "child_names": [], "candidate_sentences": ["a beer bottle, which is a bottle"]}, "403": {"node_name": "backpack, back pack, knapsack, packsack, rucksack, haversack", "parent_names": ["bag"], "child_names": [], "candidate_sentences": ["a backpack, back pack, knapsack, packsack, rucksack, haversack, which is a bag"]}, "404": {"node_name": "lacertid lizard, lacertid", "parent_names": ["lizard"], "child_names": ["green lizard", "<PERSON><PERSON><PERSON> viridis"], "candidate_sentences": ["a green lizard, which is a lacertid lizard, lacertid, which is a lizard", "a Lacerta viridis, which is a lacertid lizard, lacertid, which is a lizard"]}, "405": {"node_name": "organ, pipe organ", "parent_names": ["keyboard instrument"], "child_names": [], "candidate_sentences": ["a organ, pipe organ, which is a keyboard instrument"]}, "406": {"node_name": "keeshond", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a keeshond, which is a dog", "a keeshond, which is a domestic dog", "a keeshond, which is a Canis familiaris"]}, "407": {"node_name": "steam locomotive", "parent_names": ["locomotive", "engine", "locomotive engine", "railway locomotive"], "child_names": [], "candidate_sentences": ["a steam locomotive, which is a locomotive", "a steam locomotive, which is a engine", "a steam locomotive, which is a locomotive engine", "a steam locomotive, which is a railway locomotive"]}, "408": {"node_name": "guenon, guenon monkey", "parent_names": ["monkey"], "child_names": [], "candidate_sentences": ["a guenon, guenon monkey, which is a monkey"]}, "409": {"node_name": "street sign", "parent_names": ["signboard", "sign"], "child_names": [], "candidate_sentences": ["a street sign, which is a signboard", "a street sign, which is a sign"]}, "410": {"node_name": "b<PERSON><PERSON><PERSON>, Russian wolfhound", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a borzoi, Russian wolfhound, which is a dog", "a borzoi, Russian wolfhound, which is a domestic dog", "a borzoi, Russian wolfhound, which is a Canis familiaris"]}, "411": {"node_name": "tiger cat", "parent_names": ["domestic cat", "house cat", "Fe<PERSON> domesticus", "<PERSON><PERSON> catus"], "child_names": [], "candidate_sentences": ["a tiger cat, which is a domestic cat", "a tiger cat, which is a house cat", "a tiger cat, which is a Felis domesticus", "a tiger cat, which is a Felis catus"]}, "412": {"node_name": "ibex, Capra ibex", "parent_names": ["goat", "caprine animal"], "child_names": [], "candidate_sentences": ["a ibex, Capra ibex, which is a goat", "a ibex, Capra ibex, which is a caprine animal"]}, "413": {"node_name": "European fire salamander, <PERSON><PERSON>dra salamandra", "parent_names": ["salamander"], "child_names": [], "candidate_sentences": ["a European fire salamander, <PERSON><PERSON><PERSON> salamandra, which is a salamander"]}, "414": {"node_name": "mortarboard", "parent_names": ["cap"], "child_names": [], "candidate_sentences": ["a mortarboard, which is a cap"]}, "415": {"node_name": "water buffalo, water ox, Asiatic buffalo, Bubalus bubalis", "parent_names": ["Old World buffalo", "buffalo"], "child_names": [], "candidate_sentences": ["a water buffalo, water ox, Asiatic buffalo, Bubalus bubalis, which is a Old World buffalo", "a water buffalo, water ox, Asiatic buffalo, Bubalus bubalis, which is a buffalo"]}, "416": {"node_name": "consomme", "parent_names": ["soup"], "child_names": [], "candidate_sentences": ["a consomme, which is a soup"]}, "417": {"node_name": "prison, prison house", "parent_names": ["dummy47"], "child_names": [], "candidate_sentences": ["a prison, prison house, which is a dummy47"]}, "418": {"node_name": "shopping cart", "parent_names": ["handcart", "pushcart", "cart", "go-cart"], "child_names": [], "candidate_sentences": ["a shopping cart, which is a handcart", "a shopping cart, which is a pushcart", "a shopping cart, which is a cart", "a shopping cart, which is a go-cart"]}, "419": {"node_name": "school bus", "parent_names": ["bus", "autobus", "coach", "charabanc", "double-decker", "jitney", "motorbus", "motorcoach", "omnibus", "passenger vehicle"], "child_names": [], "candidate_sentences": ["a school bus, which is a bus", "a school bus, which is a autobus", "a school bus, which is a coach", "a school bus, which is a charabanc", "a school bus, which is a double-decker", "a school bus, which is a jitney", "a school bus, which is a motorbus", "a school bus, which is a motorcoach", "a school bus, which is a omnibus", "a school bus, which is a passenger vehicle"]}, "420": {"node_name": "ornithischian, ornithischian dinosaur", "parent_names": ["dinosaur"], "child_names": ["ceratopsian", "horned dinosaur"], "candidate_sentences": ["a ceratopsian, which is a ornithischian, ornithischian dinosaur, which is a dinosaur", "a horned dinosaur, which is a ornithischian, ornithischian dinosaur, which is a dinosaur"]}, "421": {"node_name": "boa constrictor, Constrictor constrictor", "parent_names": ["snake", "serpent", "ophidian"], "child_names": [], "candidate_sentences": ["a boa constrictor, Constrictor constrictor, which is a snake", "a boa constrictor, Constrictor constrictor, which is a serpent", "a boa constrictor, Constrictor constrictor, which is a ophidian"]}, "422": {"node_name": "pickelhaube", "parent_names": ["helmet"], "child_names": [], "candidate_sentences": ["a pickelhaube, which is a helmet"]}, "423": {"node_name": "water bottle", "parent_names": ["bottle"], "child_names": [], "candidate_sentences": ["a water bottle, which is a bottle"]}, "424": {"node_name": "king crab, Alaska crab, Alaskan king crab, Alaska king crab, Paralithodes camtschatica", "parent_names": ["crab"], "child_names": [], "candidate_sentences": ["a king crab, Alaska crab, Alaskan king crab, Alaska king crab, Paralithodes camtschatica, which is a crab"]}, "425": {"node_name": "black stork, Ciconia nigra", "parent_names": ["stork"], "child_names": [], "candidate_sentences": ["a black stork, Ciconia nigra, which is a stork"]}, "426": {"node_name": "Irish wolfhound", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Irish wolfhound, which is a dog", "a Irish wolfhound, which is a domestic dog", "a Irish wolfhound, which is a Canis familiaris"]}, "427": {"node_name": "guitar", "parent_names": ["stringed instrument"], "child_names": ["acoustic guitar", "electric guitar"], "candidate_sentences": ["a acoustic guitar, which is a guitar, which is a stringed instrument", "a electric guitar, which is a guitar, which is a stringed instrument"]}, "428": {"node_name": "soft-coated wheaten terrier", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a soft-coated wheaten terrier, which is a dog", "a soft-coated wheaten terrier, which is a domestic dog", "a soft-coated wheaten terrier, which is a Canis familiaris"]}, "429": {"node_name": "Border collie", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Border collie, which is a dog", "a Border collie, which is a domestic dog", "a Border collie, which is a Canis familiaris"]}, "430": {"node_name": "water tower", "parent_names": ["reservoir"], "child_names": [], "candidate_sentences": ["a water tower, which is a reservoir"]}, "431": {"node_name": "pedestal, plinth, footstall", "parent_names": ["column", "pillar"], "child_names": [], "candidate_sentences": ["a pedestal, plinth, footstall, which is a column", "a pedestal, plinth, footstall, which is a pillar"]}, "432": {"node_name": "hen-of-the-woods, hen of the woods, Polyporus frondosus, <PERSON><PERSON><PERSON><PERSON> frondosa", "parent_names": ["mushroom"], "child_names": [], "candidate_sentences": ["a hen-of-the-woods, hen of the woods, Polyporus frondosus, Grifola frondosa, which is a mushroom"]}, "433": {"node_name": "sunglasses, dark glasses, shades", "parent_names": ["spectacles", "specs", "eyeglasses", "glasses"], "child_names": [], "candidate_sentences": ["a sunglasses, dark glasses, shades, which is a spectacles", "a sunglasses, dark glasses, shades, which is a specs", "a sunglasses, dark glasses, shades, which is a eyeglasses", "a sunglasses, dark glasses, shades, which is a glasses"]}, "434": {"node_name": "axolo<PERSON>, mud puppy, Ambystoma mexicanum", "parent_names": ["salamander"], "child_names": [], "candidate_sentences": ["a axolotl, mud puppy, Ambystoma mexicanum, which is a salamander"]}, "435": {"node_name": "tree frog, tree-frog", "parent_names": ["frog", "toad", "toad frog", "<PERSON><PERSON>n", "batrachian", "salientian"], "child_names": [], "candidate_sentences": ["a tree frog, tree-frog, which is a frog", "a tree frog, tree-frog, which is a toad", "a tree frog, tree-frog, which is a toad frog", "a tree frog, tree-frog, which is a anuran", "a tree frog, tree-frog, which is a batrachian", "a tree frog, tree-frog, which is a salientian"]}, "436": {"node_name": "chair of state", "parent_names": ["chair"], "child_names": ["throne"], "candidate_sentences": ["a throne, which is a chair of state, which is a chair"]}, "437": {"node_name": "pencil sharpener", "parent_names": ["sharpener"], "child_names": [], "candidate_sentences": ["a pencil sharpener, which is a sharpener"]}, "438": {"node_name": "horizontal bar, high bar", "parent_names": ["gymnastic apparatus", "exerciser"], "child_names": [], "candidate_sentences": ["a horizontal bar, high bar, which is a gymnastic apparatus", "a horizontal bar, high bar, which is a exerciser"]}, "439": {"node_name": "sandal", "parent_names": ["shoe"], "child_names": [], "candidate_sentences": ["a sandal, which is a shoe"]}, "440": {"node_name": "bakery, bakeshop, bakehouse", "parent_names": ["mercantile establishment", "retail store", "sales outlet", "outlet"], "child_names": [], "candidate_sentences": ["a bakery, bakeshop, bakehouse, which is a mercantile establishment", "a bakery, bakeshop, bakehouse, which is a retail store", "a bakery, bakeshop, bakehouse, which is a sales outlet", "a bakery, bakeshop, bakehouse, which is a outlet"]}, "441": {"node_name": "summer squash", "parent_names": ["squash"], "child_names": ["<PERSON><PERSON><PERSON>", "courgette", "spaghetti squash"], "candidate_sentences": ["a zucchini, which is a summer squash, which is a squash", "a courgette, which is a summer squash, which is a squash", "a spaghetti squash, which is a summer squash, which is a squash"]}, "442": {"node_name": "sandglass", "parent_names": ["timepiece", "timekeeper", "horologe"], "child_names": ["hourglass"], "candidate_sentences": ["a hourglass, which is a sandglass, which is a timepiece", "a hourglass, which is a sandglass, which is a timekeeper", "a hourglass, which is a sandglass, which is a horologe"]}, "443": {"node_name": "Italian greyhound", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Italian greyhound, which is a dog", "a Italian greyhound, which is a domestic dog", "a Italian greyhound, which is a Canis familiaris"]}, "444": {"node_name": "red wine", "parent_names": ["wine", "vino"], "child_names": [], "candidate_sentences": ["a red wine, which is a wine", "a red wine, which is a vino"]}, "445": {"node_name": "otterhound, otter hound", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a otterhound, otter hound, which is a dog", "a otterhound, otter hound, which is a domestic dog", "a otterhound, otter hound, which is a Canis familiaris"]}, "446": {"node_name": "stole", "parent_names": ["scarf"], "child_names": [], "candidate_sentences": ["a stole, which is a scarf"]}, "447": {"node_name": "grocery store, grocery, food market, market", "parent_names": ["mercantile establishment", "retail store", "sales outlet", "outlet"], "child_names": [], "candidate_sentences": ["a grocery store, grocery, food market, market, which is a mercantile establishment", "a grocery store, grocery, food market, market, which is a retail store", "a grocery store, grocery, food market, market, which is a sales outlet", "a grocery store, grocery, food market, market, which is a outlet"]}, "448": {"node_name": "automatic firearm, automatic gun, automatic weapon", "parent_names": ["firearm", "piece", "small-arm"], "child_names": ["assault rifle", "assault gun"], "candidate_sentences": ["a assault rifle, which is a automatic firearm, automatic gun, automatic weapon, which is a firearm", "a assault gun, which is a automatic firearm, automatic gun, automatic weapon, which is a firearm", "a assault rifle, which is a automatic firearm, automatic gun, automatic weapon, which is a piece", "a assault gun, which is a automatic firearm, automatic gun, automatic weapon, which is a piece", "a assault rifle, which is a automatic firearm, automatic gun, automatic weapon, which is a small-arm", "a assault gun, which is a automatic firearm, automatic gun, automatic weapon, which is a small-arm"]}, "449": {"node_name": "rattlesnake, rattler", "parent_names": ["snake", "serpent", "ophidian"], "child_names": ["diamondback", "diamondback rattlesnake", "<PERSON><PERSON><PERSON><PERSON> adamanteus", "sidewinder", "horned rattlesnake", "<PERSON><PERSON><PERSON><PERSON> cerastes"], "candidate_sentences": ["a diamondback, which is a rattlesnake, rattler, which is a snake", "a diamondback rattlesnake, which is a rattlesnake, rattler, which is a snake", "a Crotalus adamanteus, which is a rattlesnake, rattler, which is a snake", "a sidewinder, which is a rattlesnake, rattler, which is a snake", "a horned rattlesnake, which is a rattlesnake, rattler, which is a snake", "a Crotalus cerastes, which is a rattlesnake, rattler, which is a snake", "a diamondback, which is a rattlesnake, rattler, which is a serpent", "a diamondback rattlesnake, which is a rattlesnake, rattler, which is a serpent", "a Crotalus adamanteus, which is a rattlesnake, rattler, which is a serpent", "a sidewinder, which is a rattlesnake, rattler, which is a serpent", "a horned rattlesnake, which is a rattlesnake, rattler, which is a serpent", "a Crotalus cerastes, which is a rattlesnake, rattler, which is a serpent", "a diamondback, which is a rattlesnake, rattler, which is a ophidian", "a diamondback rattlesnake, which is a rattlesnake, rattler, which is a ophidian", "a Crotalus adamanteus, which is a rattlesnake, rattler, which is a ophidian", "a sidewinder, which is a rattlesnake, rattler, which is a ophidian", "a horned rattlesnake, which is a rattlesnake, rattler, which is a ophidian", "a Crotalus cerastes, which is a rattlesnake, rattler, which is a ophidian"]}, "450": {"node_name": "Irish setter, red setter", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Irish setter, red setter, which is a dog", "a Irish setter, red setter, which is a domestic dog", "a Irish setter, red setter, which is a Canis familiaris"]}, "451": {"node_name": "a<PERSON>a", "parent_names": ["coat"], "child_names": [], "candidate_sentences": ["a abaya, which is a coat"]}, "452": {"node_name": "desktop computer", "parent_names": ["digital computer"], "child_names": [], "candidate_sentences": ["a desktop computer, which is a digital computer"]}, "453": {"node_name": "tennis ball", "parent_names": ["ball"], "child_names": [], "candidate_sentences": ["a tennis ball, which is a ball"]}, "454": {"node_name": "cargo ship, cargo vessel", "parent_names": ["ship"], "child_names": ["container ship", "containership", "container vessel"], "candidate_sentences": ["a container ship, which is a cargo ship, cargo vessel, which is a ship", "a containership, which is a cargo ship, cargo vessel, which is a ship", "a container vessel, which is a cargo ship, cargo vessel, which is a ship"]}, "455": {"node_name": "confectionery, confectionary, candy store", "parent_names": ["mercantile establishment", "retail store", "sales outlet", "outlet"], "child_names": [], "candidate_sentences": ["a confectionery, confectionary, candy store, which is a mercantile establishment", "a confectionery, confectionary, candy store, which is a retail store", "a confectionery, confectionary, candy store, which is a sales outlet", "a confectionery, confectionary, candy store, which is a outlet"]}, "456": {"node_name": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Samoyed, <PERSON><PERSON><PERSON>, which is a dog", "a Samoyed, <PERSON><PERSON><PERSON>, which is a domestic dog", "a Samoyed, <PERSON>oyede, which is a Canis familiaris"]}, "457": {"node_name": "picket fence, paling", "parent_names": ["fence", "fencing"], "child_names": [], "candidate_sentences": ["a picket fence, paling, which is a fence", "a picket fence, paling, which is a fencing"]}, "458": {"node_name": "traffic light, traffic signal, stoplight", "parent_names": ["signboard", "sign"], "child_names": [], "candidate_sentences": ["a traffic light, traffic signal, stoplight, which is a signboard", "a traffic light, traffic signal, stoplight, which is a sign"]}, "459": {"node_name": "barn spider, Araneus cavaticus", "parent_names": ["spider"], "child_names": [], "candidate_sentences": ["a barn spider, Araneus cavaticus, which is a spider"]}, "460": {"node_name": "bullterrier, bull terrier", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": ["Staffordshire bullterrier", "Staffordshire bull terrier", "American Staffordshire terrier", "Staffordshire terrier", "American pit bull terrier", "pit bull terrier"], "candidate_sentences": ["a Staffordshire bullterrier, which is a bullterrier, bull terrier, which is a dog", "a Staffordshire bull terrier, which is a bullterrier, bull terrier, which is a dog", "a American Staffordshire terrier, which is a bullterrier, bull terrier, which is a dog", "a Staffordshire terrier, which is a bullterrier, bull terrier, which is a dog", "a American pit bull terrier, which is a bullterrier, bull terrier, which is a dog", "a pit bull terrier, which is a bullterrier, bull terrier, which is a dog", "a Staffordshire bullterrier, which is a bullterrier, bull terrier, which is a domestic dog", "a Staffordshire bull terrier, which is a bullterrier, bull terrier, which is a domestic dog", "a American Staffordshire terrier, which is a bullterrier, bull terrier, which is a domestic dog", "a Staffordshire terrier, which is a bullterrier, bull terrier, which is a domestic dog", "a American pit bull terrier, which is a bullterrier, bull terrier, which is a domestic dog", "a pit bull terrier, which is a bullterrier, bull terrier, which is a domestic dog", "a Staffordshire bullterrier, which is a bullterrier, bull terrier, which is a Canis familiaris", "a Staffordshire bull terrier, which is a bullterrier, bull terrier, which is a Canis familiaris", "a American Staffordshire terrier, which is a bullterrier, bull terrier, which is a Canis familiaris", "a Staffordshire terrier, which is a bullterrier, bull terrier, which is a Canis familiaris", "a American pit bull terrier, which is a bullterrier, bull terrier, which is a Canis familiaris", "a pit bull terrier, which is a bullterrier, bull terrier, which is a Canis familiaris"]}, "461": {"node_name": "thunder snake, worm snake, Carphophis amoenus", "parent_names": ["snake", "serpent", "ophidian"], "child_names": [], "candidate_sentences": ["a thunder snake, worm snake, Carphophis amoenus, which is a snake", "a thunder snake, worm snake, Carphophis amoenus, which is a serpent", "a thunder snake, worm snake, Carphop<PERSON> amoenus, which is a ophidian"]}, "462": {"node_name": "Eskimo dog, husky", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Eskimo dog, husky, which is a dog", "a Eskimo dog, husky, which is a domestic dog", "a Eskimo dog, husky, which is a Canis familiaris"]}, "463": {"node_name": "triumphal arch", "parent_names": ["memorial", "monument"], "child_names": [], "candidate_sentences": ["a triumphal arch, which is a memorial", "a triumphal arch, which is a monument"]}, "464": {"node_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, monkey pinscher, monkey dog", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a affen<PERSON>scher, monkey pinscher, monkey dog, which is a dog", "a affenpinscher, monkey pinscher, monkey dog, which is a domestic dog", "a affen<PERSON>scher, monkey pinscher, monkey dog, which is a Canis familiaris"]}, "465": {"node_name": "wine bottle", "parent_names": ["bottle"], "child_names": [], "candidate_sentences": ["a wine bottle, which is a bottle"]}, "466": {"node_name": "Scottish deerhound, deerhound", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Scottish deerhound, deerhound, which is a dog", "a Scottish deerhound, deerhound, which is a domestic dog", "a Scottish deerhound, deerhound, which is a Canis familiaris"]}, "467": {"node_name": "Rhodesian ridgeback", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Rhodesian ridgeback, which is a dog", "a Rhodesian ridgeback, which is a domestic dog", "a Rhodesian ridgeback, which is a Canis familiaris"]}, "468": {"node_name": "cradle", "parent_names": ["baby bed", "baby's bed"], "child_names": [], "candidate_sentences": ["a cradle, which is a baby bed", "a cradle, which is a baby's bed"]}, "469": {"node_name": "<PERSON><PERSON><PERSON>, Hylob<PERSON> syndactylus, Symphalangus syndactylus", "parent_names": ["ape"], "child_names": [], "candidate_sentences": ["a siamang, <PERSON>yl<PERSON><PERSON> syndactylus, Symphalangus syndactylus, which is a ape"]}, "470": {"node_name": "pillow", "parent_names": ["cushion"], "child_names": [], "candidate_sentences": ["a pillow, which is a cushion"]}, "471": {"node_name": "Great Pyrenees", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Great Pyrenees, which is a dog", "a Great Pyrenees, which is a domestic dog", "a Great Pyrenees, which is a Canis familiaris"]}, "472": {"node_name": "silky terrier, Sydney silky", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a silky terrier, Sydney silky, which is a dog", "a silky terrier, Sydney silky, which is a domestic dog", "a silky terrier, Sydney silky, which is a Canis familiaris"]}, "473": {"node_name": "killer whale, killer, orca, grampus, sea wolf, Orcinus orca", "parent_names": ["whale"], "child_names": [], "candidate_sentences": ["a killer whale, killer, orca, grampus, sea wolf, Orcinus orca, which is a whale"]}, "474": {"node_name": "American black bear, black bear, Ursus americanus, Euarctos americanus", "parent_names": ["bear"], "child_names": [], "candidate_sentences": ["a American black bear, black bear, Ursus americanus, Euarctos americanus, which is a bear"]}, "475": {"node_name": "gasmask, respirator, gas helmet", "parent_names": ["face mask"], "child_names": [], "candidate_sentences": ["a gasmask, respirator, gas helmet, which is a face mask"]}, "476": {"node_name": "mastiff", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": ["Tibetan mastiff"], "candidate_sentences": ["a Tibetan mastiff, which is a mastiff, which is a dog", "a Tibetan mastiff, which is a mastiff, which is a domestic dog", "a Tibetan mastiff, which is a mastiff, which is a Canis familiaris"]}, "477": {"node_name": "paper towel", "parent_names": ["towel"], "child_names": [], "candidate_sentences": ["a paper towel, which is a towel"]}, "478": {"node_name": "gown", "parent_names": ["dress", "frock"], "child_names": [], "candidate_sentences": ["a gown, which is a dress", "a gown, which is a frock"]}, "479": {"node_name": "oboe, hautboy, hautbois", "parent_names": ["wind instrument", "wind"], "child_names": [], "candidate_sentences": ["a oboe, hautboy, hautbois, which is a wind instrument", "a oboe, hautboy, hautbois, which is a wind"]}, "480": {"node_name": "whippet", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a whippet, which is a dog", "a whippet, which is a domestic dog", "a whippet, which is a Canis familiaris"]}, "481": {"node_name": "watch, ticker", "parent_names": ["timepiece", "timekeeper", "horologe"], "child_names": ["digital watch"], "candidate_sentences": ["a digital watch, which is a watch, ticker, which is a timepiece", "a digital watch, which is a watch, ticker, which is a timekeeper", "a digital watch, which is a watch, ticker, which is a horologe"]}, "482": {"node_name": "<PERSON><PERSON>, <PERSON><PERSON> pinscher", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Doberman, Doberman pinscher, which is a dog", "a Doberman, Doberman pinscher, which is a domestic dog", "a Doberman, <PERSON>berman pinscher, which is a Canis familiaris"]}, "483": {"node_name": "spider monkey, <PERSON><PERSON><PERSON>i", "parent_names": ["monkey"], "child_names": [], "candidate_sentences": ["a spider monkey, <PERSON><PERSON><PERSON>i, which is a monkey"]}, "484": {"node_name": "black-and-tan coonhound", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a black-and-tan coonhound, which is a dog", "a black-and-tan coonhound, which is a domestic dog", "a black-and-tan coonhound, which is a Canis familiaris"]}, "485": {"node_name": "convertible, sofa bed", "parent_names": ["sofa", "couch", "lounge"], "child_names": ["studio couch", "day bed"], "candidate_sentences": ["a studio couch, which is a convertible, sofa bed, which is a sofa", "a day bed, which is a convertible, sofa bed, which is a sofa", "a studio couch, which is a convertible, sofa bed, which is a couch", "a day bed, which is a convertible, sofa bed, which is a couch", "a studio couch, which is a convertible, sofa bed, which is a lounge", "a day bed, which is a convertible, sofa bed, which is a lounge"]}, "486": {"node_name": "purse", "parent_names": ["bag"], "child_names": [], "candidate_sentences": ["a purse, which is a bag"]}, "487": {"node_name": "prairie chicken, prairie grouse, prairie fowl", "parent_names": ["grouse"], "child_names": [], "candidate_sentences": ["a prairie chicken, prairie grouse, prairie fowl, which is a grouse"]}, "488": {"node_name": "loudspeaker, speaker, speaker unit, loudspeaker system, speaker system", "parent_names": ["audio system", "sound system"], "child_names": [], "candidate_sentences": ["a loudspeaker, speaker, speaker unit, loudspeaker system, speaker system, which is a audio system", "a loudspeaker, speaker, speaker unit, loudspeaker system, speaker system, which is a sound system"]}, "489": {"node_name": "ice lolly, lolly, lollipop, popsicle", "parent_names": ["frozen dessert"], "child_names": [], "candidate_sentences": ["a ice lolly, lolly, lollipop, popsicle, which is a frozen dessert"]}, "490": {"node_name": "birdhouse", "parent_names": ["dwelling", "home", "domicile", "abode", "habitation", "dwelling house"], "child_names": [], "candidate_sentences": ["a birdhouse, which is a dwelling", "a birdhouse, which is a home", "a birdhouse, which is a domicile", "a birdhouse, which is a abode", "a birdhouse, which is a habitation", "a birdhouse, which is a dwelling house"]}, "491": {"node_name": "feather boa, boa", "parent_names": ["scarf"], "child_names": [], "candidate_sentences": ["a feather boa, boa, which is a scarf"]}, "492": {"node_name": "monastery", "parent_names": ["dwelling", "home", "domicile", "abode", "habitation", "dwelling house"], "child_names": [], "candidate_sentences": ["a monastery, which is a dwelling", "a monastery, which is a home", "a monastery, which is a domicile", "a monastery, which is a abode", "a monastery, which is a habitation", "a monastery, which is a dwelling house"]}, "493": {"node_name": "Indian elephant, <PERSON>ephas maximus", "parent_names": ["elephant"], "child_names": [], "candidate_sentences": ["a Indian elephant, <PERSON><PERSON><PERSON> maximus, which is a elephant"]}, "494": {"node_name": "beach wagon, station wagon, wagon, estate car, beach waggon, station waggon, waggon", "parent_names": ["car", "auto", "automobile", "machine", "motorcar"], "child_names": [], "candidate_sentences": ["a beach wagon, station wagon, wagon, estate car, beach waggon, station waggon, waggon, which is a car", "a beach wagon, station wagon, wagon, estate car, beach waggon, station waggon, waggon, which is a auto", "a beach wagon, station wagon, wagon, estate car, beach waggon, station waggon, waggon, which is a automobile", "a beach wagon, station wagon, wagon, estate car, beach waggon, station waggon, waggon, which is a machine", "a beach wagon, station wagon, wagon, estate car, beach waggon, station waggon, waggon, which is a motorcar"]}, "495": {"node_name": "danaid, danaid butterfly", "parent_names": ["butterfly"], "child_names": ["monarch", "monarch butterfly", "milkweed butterfly", "<PERSON><PERSON> plexippus"], "candidate_sentences": ["a monarch, which is a danaid, danaid butterfly, which is a butterfly", "a monarch butterfly, which is a danaid, danaid butterfly, which is a butterfly", "a milkweed butterfly, which is a danaid, danaid butterfly, which is a butterfly", "a Danaus plexippus, which is a danaid, danaid butterfly, which is a butterfly"]}, "496": {"node_name": "vestment", "parent_names": ["coat"], "child_names": [], "candidate_sentences": ["a vestment, which is a coat"]}, "497": {"node_name": "coho, cohoe, coho salmon, blue jack, silver salmon, Oncorhynchus kisutch", "parent_names": ["salmon"], "child_names": [], "candidate_sentences": ["a coho, cohoe, coho salmon, blue jack, silver salmon, Oncorhynchus kisutch, which is a salmon"]}, "498": {"node_name": "red-backed sandpiper, dunlin, Erolia alpina", "parent_names": ["sandpiper"], "child_names": [], "candidate_sentences": ["a red-backed sandpiper, dunlin, Erolia alpina, which is a sandpiper"]}, "499": {"node_name": "rail fence", "parent_names": ["fence", "fencing"], "child_names": ["worm fence", "snake fence", "snake-rail fence", "Virginia fence"], "candidate_sentences": ["a worm fence, which is a rail fence, which is a fence", "a snake fence, which is a rail fence, which is a fence", "a snake-rail fence, which is a rail fence, which is a fence", "a Virginia fence, which is a rail fence, which is a fence", "a worm fence, which is a rail fence, which is a fencing", "a snake fence, which is a rail fence, which is a fencing", "a snake-rail fence, which is a rail fence, which is a fencing", "a Virginia fence, which is a rail fence, which is a fencing"]}, "500": {"node_name": "crash helmet", "parent_names": ["helmet"], "child_names": [], "candidate_sentences": ["a crash helmet, which is a helmet"]}, "501": {"node_name": "vine snake", "parent_names": ["snake", "serpent", "ophidian"], "child_names": [], "candidate_sentences": ["a vine snake, which is a snake", "a vine snake, which is a serpent", "a vine snake, which is a ophidian"]}, "502": {"node_name": "Dungeness crab, Cancer magister", "parent_names": ["crab"], "child_names": [], "candidate_sentences": ["a Dungeness crab, Cancer magister, which is a crab"]}, "503": {"node_name": "squirrel monkey, <PERSON><PERSON><PERSON> sciureus", "parent_names": ["monkey"], "child_names": [], "candidate_sentences": ["a squirrel monkey, <PERSON><PERSON><PERSON> sciureus, which is a monkey"]}, "504": {"node_name": "<PERSON> setter", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Gordon setter, which is a dog", "a Gordon setter, which is a domestic dog", "a Gordon setter, which is a Canis familiaris"]}, "505": {"node_name": "beagle", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a beagle, which is a dog", "a beagle, which is a domestic dog", "a beagle, which is a Canis familiaris"]}, "506": {"node_name": "drum, membranophone, tympan", "parent_names": ["percussion instrument", "percussive instrument"], "child_names": [], "candidate_sentences": ["a drum, membranophone, tympan, which is a percussion instrument", "a drum, membranophone, tympan, which is a percussive instrument"]}, "507": {"node_name": "cowboy hat, ten-gallon hat", "parent_names": ["hat", "chapeau", "lid"], "child_names": [], "candidate_sentences": ["a cowboy hat, ten-gallon hat, which is a hat", "a cowboy hat, ten-gallon hat, which is a chapeau", "a cowboy hat, ten-gallon hat, which is a lid"]}, "508": {"node_name": "Bouvier des Flandres, Bouviers des Flandres", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Bouvier des Flandres, Bouviers des Flandres, which is a dog", "a Bouvier des Flandres, Bouviers des Flandres, which is a domestic dog", "a Bouvier des Flandres, Bouviers des Flandres, which is a Canis familiaris"]}, "509": {"node_name": "greenhouse, nursery, glasshouse", "parent_names": ["outbuilding"], "child_names": [], "candidate_sentences": ["a greenhouse, nursery, glasshouse, which is a outbuilding"]}, "510": {"node_name": "yurt", "parent_names": ["dwelling", "home", "domicile", "abode", "habitation", "dwelling house"], "child_names": [], "candidate_sentences": ["a yurt, which is a dwelling", "a yurt, which is a home", "a yurt, which is a domicile", "a yurt, which is a abode", "a yurt, which is a habitation", "a yurt, which is a dwelling house"]}, "511": {"node_name": "pop bottle, soda bottle", "parent_names": ["bottle"], "child_names": [], "candidate_sentences": ["a pop bottle, soda bottle, which is a bottle"]}, "512": {"node_name": "rubber eraser, rubber, pencil eraser", "parent_names": ["eraser"], "child_names": [], "candidate_sentences": ["a rubber eraser, rubber, pencil eraser, which is a eraser"]}, "513": {"node_name": "Border terrier", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Border terrier, which is a dog", "a Border terrier, which is a domestic dog", "a Border terrier, which is a Canis familiaris"]}, "514": {"node_name": "white wolf, Arctic wolf, Canis lupus tundrarum", "parent_names": ["wolf"], "child_names": [], "candidate_sentences": ["a white wolf, Arctic wolf, Canis lupus tundrarum, which is a wolf"]}, "515": {"node_name": "Arctic fox, white fox, Alopex lagopus", "parent_names": ["fox"], "child_names": [], "candidate_sentences": ["a Arctic fox, white fox, Alopex lagopus, which is a fox"]}, "516": {"node_name": "gazelle", "parent_names": ["antelope"], "child_names": [], "candidate_sentences": ["a gazelle, which is a antelope"]}, "517": {"node_name": "crib, cot", "parent_names": ["baby bed", "baby's bed"], "child_names": [], "candidate_sentences": ["a crib, cot, which is a baby bed", "a crib, cot, which is a baby's bed"]}, "518": {"node_name": "sundial", "parent_names": ["timepiece", "timekeeper", "horologe"], "child_names": [], "candidate_sentences": ["a sundial, which is a timepiece", "a sundial, which is a timekeeper", "a sundial, which is a horologe"]}, "519": {"node_name": "rocking chair, rocker", "parent_names": ["chair"], "child_names": [], "candidate_sentences": ["a rocking chair, rocker, which is a chair"]}, "520": {"node_name": "barber chair", "parent_names": ["chair"], "child_names": [], "candidate_sentences": ["a barber chair, which is a chair"]}, "521": {"node_name": "capuchin, ringtail, Cebus capucinus", "parent_names": ["monkey"], "child_names": [], "candidate_sentences": ["a capuchin, ringtail, Cebus capucinus, which is a monkey"]}, "522": {"node_name": "lipstick, lip rouge", "parent_names": ["makeup", "make-up", "war paint"], "child_names": [], "candidate_sentences": ["a lipstick, lip rouge, which is a makeup", "a lipstick, lip rouge, which is a make-up", "a lipstick, lip rouge, which is a war paint"]}, "523": {"node_name": "fiddler crab", "parent_names": ["crab"], "child_names": [], "candidate_sentences": ["a fiddler crab, which is a crab"]}, "524": {"node_name": "eating apple, dessert apple", "parent_names": ["apple"], "child_names": ["<PERSON>"], "candidate_sentences": ["a Granny Smith, which is a eating apple, dessert apple, which is a apple"]}, "525": {"node_name": "tiger beetle", "parent_names": ["beetle"], "child_names": [], "candidate_sentences": ["a tiger beetle, which is a beetle"]}, "526": {"node_name": "prayer rug, prayer mat", "parent_names": ["rug", "carpet", "carpeting"], "child_names": [], "candidate_sentences": ["a prayer rug, prayer mat, which is a rug", "a prayer rug, prayer mat, which is a carpet", "a prayer rug, prayer mat, which is a carpeting"]}, "527": {"node_name": "black widow, <PERSON><PERSON><PERSON><PERSON> mactans", "parent_names": ["spider"], "child_names": [], "candidate_sentences": ["a black widow, <PERSON><PERSON><PERSON><PERSON> mactans, which is a spider"]}, "528": {"node_name": "warship, war vessel, combat ship", "parent_names": ["ship"], "child_names": ["aircraft carrier", "carrier", "flattop", "attack aircraft carrier"], "candidate_sentences": ["a aircraft carrier, which is a warship, war vessel, combat ship, which is a ship", "a carrier, which is a warship, war vessel, combat ship, which is a ship", "a flattop, which is a warship, war vessel, combat ship, which is a ship", "a attack aircraft carrier, which is a warship, war vessel, combat ship, which is a ship"]}, "529": {"node_name": "three-toed sloth, ai, Bradypus tridactylus", "parent_names": ["sloth", "tree sloth"], "child_names": [], "candidate_sentences": ["a three-toed sloth, ai, Bradypus tridactylus, which is a sloth", "a three-toed sloth, ai, Bradypus tridactylus, which is a tree sloth"]}, "530": {"node_name": "sports car, sport car", "parent_names": ["car", "auto", "automobile", "machine", "motorcar"], "child_names": [], "candidate_sentences": ["a sports car, sport car, which is a car", "a sports car, sport car, which is a auto", "a sports car, sport car, which is a automobile", "a sports car, sport car, which is a machine", "a sports car, sport car, which is a motorcar"]}, "531": {"node_name": "sarong", "parent_names": ["skirt"], "child_names": [], "candidate_sentences": ["a sarong, which is a skirt"]}, "532": {"node_name": "police van, police wagon, paddy wagon, patrol wagon, wagon, black Maria", "parent_names": ["car", "auto", "automobile", "machine", "motorcar"], "child_names": [], "candidate_sentences": ["a police van, police wagon, paddy wagon, patrol wagon, wagon, black Maria, which is a car", "a police van, police wagon, paddy wagon, patrol wagon, wagon, black Maria, which is a auto", "a police van, police wagon, paddy wagon, patrol wagon, wagon, black Maria, which is a automobile", "a police van, police wagon, paddy wagon, patrol wagon, wagon, black Maria, which is a machine", "a police van, police wagon, paddy wagon, patrol wagon, wagon, black Maria, which is a motorcar"]}, "533": {"node_name": "terrapin", "parent_names": ["turtle"], "child_names": [], "candidate_sentences": ["a terrapin, which is a turtle"]}, "534": {"node_name": "electric locomotive", "parent_names": ["locomotive", "engine", "locomotive engine", "railway locomotive"], "child_names": [], "candidate_sentences": ["a electric locomotive, which is a locomotive", "a electric locomotive, which is a engine", "a electric locomotive, which is a locomotive engine", "a electric locomotive, which is a railway locomotive"]}, "535": {"node_name": "African hunting dog, hyena dog, Cape hunting dog, Lycaon pictus", "parent_names": ["wild dog"], "child_names": [], "candidate_sentences": ["a African hunting dog, hyena dog, Cape hunting dog, Lycaon pictus, which is a wild dog"]}, "536": {"node_name": "file, file cabinet, filing cabinet", "parent_names": ["cabinet"], "child_names": [], "candidate_sentences": ["a file, file cabinet, filing cabinet, which is a cabinet"]}, "537": {"node_name": "sulphur butterfly, sulfur butterfly", "parent_names": ["butterfly"], "child_names": [], "candidate_sentences": ["a sulphur butterfly, sulfur butterfly, which is a butterfly"]}, "538": {"node_name": "rule, ruler", "parent_names": ["measuring stick", "measure", "measuring rod"], "child_names": [], "candidate_sentences": ["a rule, ruler, which is a measuring stick", "a rule, ruler, which is a measure", "a rule, ruler, which is a measuring rod"]}, "539": {"node_name": "punching bag, punch bag, punching ball, punchball", "parent_names": ["ball"], "child_names": [], "candidate_sentences": ["a punching bag, punch bag, punching ball, punchball, which is a ball"]}, "540": {"node_name": "chain mail, ring mail, mail, chain armor, chain armour, ring armor, ring armour", "parent_names": ["body armor", "body armour", "suit of armor", "suit of armour", "coat of mail", "cataphract"], "child_names": [], "candidate_sentences": ["a chain mail, ring mail, mail, chain armor, chain armour, ring armor, ring armour, which is a body armor", "a chain mail, ring mail, mail, chain armor, chain armour, ring armor, ring armour, which is a body armour", "a chain mail, ring mail, mail, chain armor, chain armour, ring armor, ring armour, which is a suit of armor", "a chain mail, ring mail, mail, chain armor, chain armour, ring armor, ring armour, which is a suit of armour", "a chain mail, ring mail, mail, chain armor, chain armour, ring armor, ring armour, which is a coat of mail", "a chain mail, ring mail, mail, chain armor, chain armour, ring armor, ring armour, which is a cataphract"]}, "541": {"node_name": "orangutan, orang, orangutang, Pongo pygmaeus", "parent_names": ["ape"], "child_names": [], "candidate_sentences": ["a orangutan, orang, orangutang, Pongo pygmaeus, which is a ape"]}, "542": {"node_name": "cup", "parent_names": ["punch"], "child_names": [], "candidate_sentences": ["a cup, which is a punch"]}, "543": {"node_name": "tobacco shop, tobacconist shop, tobacconist", "parent_names": ["mercantile establishment", "retail store", "sales outlet", "outlet"], "child_names": [], "candidate_sentences": ["a tobacco shop, tobacconist shop, tobacconist, which is a mercantile establishment", "a tobacco shop, tobacconist shop, tobacconist, which is a retail store", "a tobacco shop, tobacconist shop, tobacconist, which is a sales outlet", "a tobacco shop, tobacconist shop, tobacconist, which is a outlet"]}, "544": {"node_name": "hamburger, beefburger, burger", "parent_names": ["sandwich"], "child_names": ["cheeseburger"], "candidate_sentences": ["a cheeseburger, which is a hamburger, beefburger, burger, which is a sandwich"]}, "545": {"node_name": "stone wall", "parent_names": ["fence", "fencing"], "child_names": [], "candidate_sentences": ["a stone wall, which is a fence", "a stone wall, which is a fencing"]}, "546": {"node_name": "dowitcher", "parent_names": ["snipe"], "child_names": [], "candidate_sentences": ["a dowitcher, which is a snipe"]}, "547": {"node_name": "restaurant, eating house, eating place, eatery", "parent_names": ["dummy49"], "child_names": [], "candidate_sentences": ["a restaurant, eating house, eating place, eatery, which is a dummy49"]}, "548": {"node_name": "wig", "parent_names": ["hairpiece", "false hair", "postiche"], "child_names": [], "candidate_sentences": ["a wig, which is a hairpiece", "a wig, which is a false hair", "a wig, which is a postiche"]}, "549": {"node_name": "macaque", "parent_names": ["monkey"], "child_names": [], "candidate_sentences": ["a macaque, which is a monkey"]}, "550": {"node_name": "newt, triton", "parent_names": ["salamander"], "child_names": ["common newt", "<PERSON><PERSON><PERSON> vulgaris", "eft"], "candidate_sentences": ["a common newt, which is a newt, triton, which is a salamander", "a Triturus vulgaris, which is a newt, triton, which is a salamander", "a eft, which is a newt, triton, which is a salamander"]}, "551": {"node_name": "mixing bowl", "parent_names": ["bowl"], "child_names": [], "candidate_sentences": ["a mixing bowl, which is a bowl"]}, "552": {"node_name": "Scotch terrier, Scottish terrier, <PERSON>ie", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Scotch terrier, Scottish terrier, <PERSON><PERSON>, which is a dog", "a Scotch terrier, Scottish terrier, <PERSON><PERSON>, which is a domestic dog", "a Scotch terrier, Scottish terrier, <PERSON><PERSON>, which is a Canis familiaris"]}, "553": {"node_name": "pug, pug-dog", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a pug, pug-dog, which is a dog", "a pug, pug-dog, which is a domestic dog", "a pug, pug-dog, which is a Canis familiaris"]}, "554": {"node_name": "timer", "parent_names": ["timepiece", "timekeeper", "horologe"], "child_names": ["parking meter", "stopwatch", "stop watch"], "candidate_sentences": ["a parking meter, which is a timer, which is a timepiece", "a stopwatch, which is a timer, which is a timepiece", "a stop watch, which is a timer, which is a timepiece", "a parking meter, which is a timer, which is a timekeeper", "a stopwatch, which is a timer, which is a timekeeper", "a stop watch, which is a timer, which is a timekeeper", "a parking meter, which is a timer, which is a horologe", "a stopwatch, which is a timer, which is a horologe", "a stop watch, which is a timer, which is a horologe"]}, "555": {"node_name": "Windsor tie", "parent_names": ["necktie", "tie"], "child_names": [], "candidate_sentences": ["a Windsor tie, which is a necktie", "a Windsor tie, which is a tie"]}, "556": {"node_name": "Persian cat", "parent_names": ["domestic cat", "house cat", "Fe<PERSON> domesticus", "<PERSON><PERSON> catus"], "child_names": [], "candidate_sentences": ["a Persian cat, which is a domestic cat", "a Persian cat, which is a house cat", "a Persian cat, which is a Felis domesticus", "a Persian cat, which is a Felis catus"]}, "557": {"node_name": "French horn, horn", "parent_names": ["wind instrument", "wind"], "child_names": [], "candidate_sentences": ["a French horn, horn, which is a wind instrument", "a French horn, horn, which is a wind"]}, "558": {"node_name": "wolf spider, hunting spider", "parent_names": ["spider"], "child_names": [], "candidate_sentences": ["a wolf spider, hunting spider, which is a spider"]}, "559": {"node_name": "EntleBucher", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a EntleBucher, which is a dog", "a EntleBucher, which is a domestic dog", "a EntleBucher, which is a Canis familiaris"]}, "560": {"node_name": "kit fox, Vulpes macrotis", "parent_names": ["fox"], "child_names": [], "candidate_sentences": ["a kit fox, <PERSON><PERSON><PERSON> macrotis, which is a fox"]}, "561": {"node_name": "sunscreen, sunblock, sun blocker", "parent_names": ["cream", "ointment", "emollient"], "child_names": [], "candidate_sentences": ["a sunscreen, sunblock, sun blocker, which is a cream", "a sunscreen, sunblock, sun blocker, which is a ointment", "a sunscreen, sunblock, sun blocker, which is a emollient"]}, "562": {"node_name": "breastplate, aegis, egis", "parent_names": ["body armor", "body armour", "suit of armor", "suit of armour", "coat of mail", "cataphract"], "child_names": [], "candidate_sentences": ["a breastplate, aegis, egis, which is a body armor", "a breastplate, aegis, egis, which is a body armour", "a breastplate, aegis, egis, which is a suit of armor", "a breastplate, aegis, egis, which is a suit of armour", "a breastplate, aegis, egis, which is a coat of mail", "a breastplate, aegis, egis, which is a cataphract"]}, "563": {"node_name": "sea lion", "parent_names": ["seal"], "child_names": [], "candidate_sentences": ["a sea lion, which is a seal"]}, "564": {"node_name": "African grey, African gray, Psittacus erithacus", "parent_names": ["parrot"], "child_names": [], "candidate_sentences": ["a African grey, African gray, Psittacus erithacus, which is a parrot"]}, "565": {"node_name": "langur", "parent_names": ["monkey"], "child_names": [], "candidate_sentences": ["a langur, which is a monkey"]}, "566": {"node_name": "odometer, hodometer, mileometer, milometer", "parent_names": ["meter"], "child_names": [], "candidate_sentences": ["a odometer, hodometer, mileometer, milometer, which is a meter"]}, "567": {"node_name": "Polaroid camera, Polaroid Land camera", "parent_names": ["camera", "photographic camera"], "child_names": [], "candidate_sentences": ["a Polaroid camera, Polaroid Land camera, which is a camera", "a Polaroid camera, Polaroid Land camera, which is a photographic camera"]}, "568": {"node_name": "lifeboat", "parent_names": ["boat"], "child_names": [], "candidate_sentences": ["a lifeboat, which is a boat"]}, "569": {"node_name": "screen, CRT screen", "parent_names": ["display", "video display"], "child_names": [], "candidate_sentences": ["a screen, CRT screen, which is a display", "a screen, CRT screen, which is a video display"]}, "570": {"node_name": "kite", "parent_names": ["hawk"], "child_names": [], "candidate_sentences": ["a kite, which is a hawk"]}, "571": {"node_name": "great grey owl, great gray owl, Strix nebulosa", "parent_names": ["owl", "bird of Minerva", "bird of night", "hooter"], "child_names": [], "candidate_sentences": ["a great grey owl, great gray owl, <PERSON><PERSON> nebulosa, which is a owl", "a great grey owl, great gray owl, <PERSON><PERSON> nebulosa, which is a bird of Minerva", "a great grey owl, great gray owl, <PERSON><PERSON> nebulosa, which is a bird of night", "a great grey owl, great gray owl, <PERSON><PERSON> nebulosa, which is a hooter"]}, "572": {"node_name": "monitor, monitor lizard, varan", "parent_names": ["lizard"], "child_names": ["Komodo dragon", "Komodo lizard", "dragon lizard", "giant lizard", "V<PERSON><PERSON> komo<PERSON>ensis"], "candidate_sentences": ["a Komodo dragon, which is a monitor, monitor lizard, varan, which is a lizard", "a Komodo lizard, which is a monitor, monitor lizard, varan, which is a lizard", "a dragon lizard, which is a monitor, monitor lizard, varan, which is a lizard", "a giant lizard, which is a monitor, monitor lizard, varan, which is a lizard", "a Varanus komodoensis, which is a monitor, monitor lizard, varan, which is a lizard"]}, "573": {"node_name": "running shoe", "parent_names": ["shoe"], "child_names": [], "candidate_sentences": ["a running shoe, which is a shoe"]}, "574": {"node_name": "vault", "parent_names": ["roof"], "child_names": [], "candidate_sentences": ["a vault, which is a roof"]}, "575": {"node_name": "flute, transverse flute", "parent_names": ["wind instrument", "wind"], "child_names": [], "candidate_sentences": ["a flute, transverse flute, which is a wind instrument", "a flute, transverse flute, which is a wind"]}, "576": {"node_name": "electric ray, crampfish, numbfish, torpedo", "parent_names": ["ray"], "child_names": [], "candidate_sentences": ["a electric ray, crampfish, numbfish, torpedo, which is a ray"]}, "577": {"node_name": "diaper, nappy, napkin", "parent_names": ["undergarment", "unmentionable"], "child_names": [], "candidate_sentences": ["a diaper, nappy, napkin, which is a undergarment", "a diaper, nappy, napkin, which is a unmentionable"]}, "578": {"node_name": "cleaver, meat cleaver, chopper", "parent_names": ["knife"], "child_names": [], "candidate_sentences": ["a cleaver, meat cleaver, chopper, which is a knife"]}, "579": {"node_name": "peacock", "parent_names": ["<PERSON><PERSON><PERSON><PERSON>"], "child_names": [], "candidate_sentences": ["a peacock, which is a phasianid"]}, "580": {"node_name": "fountain pen", "parent_names": ["pen"], "child_names": [], "candidate_sentences": ["a fountain pen, which is a pen"]}, "581": {"node_name": "padlock", "parent_names": ["lock"], "child_names": [], "candidate_sentences": ["a padlock, which is a lock"]}, "582": {"node_name": "pirate, pirate ship", "parent_names": ["ship"], "child_names": [], "candidate_sentences": ["a pirate, pirate ship, which is a ship"]}, "583": {"node_name": "night snake, Hypsiglena torquata", "parent_names": ["snake", "serpent", "ophidian"], "child_names": [], "candidate_sentences": ["a night snake, <PERSON><PERSON>ps<PERSON><PERSON> torquata, which is a snake", "a night snake, <PERSON><PERSON><PERSON><PERSON><PERSON> torquata, which is a serpent", "a night snake, <PERSON><PERSON><PERSON><PERSON><PERSON> torquata, which is a ophidian"]}, "584": {"node_name": "s<PERSON><PERSON><PERSON>", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a schipperke, which is a dog", "a schipperke, which is a domestic dog", "a schipperke, which is a Canis familiaris"]}, "585": {"node_name": "accordion, piano accordion, squeeze box", "parent_names": ["keyboard instrument"], "child_names": [], "candidate_sentences": ["a accordion, piano accordion, squeeze box, which is a keyboard instrument"]}, "586": {"node_name": "cornet, horn, trumpet, trump", "parent_names": ["wind instrument", "wind"], "child_names": [], "candidate_sentences": ["a cornet, horn, trumpet, trump, which is a wind instrument", "a cornet, horn, trumpet, trump, which is a wind"]}, "587": {"node_name": "Mexican hairless", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a Mexican hairless, which is a dog", "a Mexican hairless, which is a domestic dog", "a Mexican hairless, which is a Canis familiaris"]}, "588": {"node_name": "redbone", "parent_names": ["dog", "domestic dog", "<PERSON><PERSON> familiaris"], "child_names": [], "candidate_sentences": ["a redbone, which is a dog", "a redbone, which is a domestic dog", "a redbone, which is a Canis familiaris"]}, "589": {"node_name": "leaf beetle, chrysomelid", "parent_names": ["beetle"], "child_names": [], "candidate_sentences": ["a leaf beetle, chrysomelid, which is a beetle"]}, "590": {"node_name": "hand glass, simple microscope, magnifying glass", "parent_names": ["microscope"], "child_names": ["loupe", "jeweler's loupe"], "candidate_sentences": ["a loupe, which is a hand glass, simple microscope, magnifying glass, which is a microscope", "a jeweler's loupe, which is a hand glass, simple microscope, magnifying glass, which is a microscope"]}}