# Copyright (c) Facebook, Inc. and its affiliates.
# Part of the code is from https://github.com/facebookresearch/Detic/blob/main/detic/data/datasets/oid.py
# Modified by <PERSON><PERSON><PERSON>

from .registry_oid import register_oid_instances
import os


categories = [
    {'id': 1, 'name': 'Infant bed', 'freebase_id': '/m/061hd_'},
    {'id': 2, 'name': '<PERSON>', 'freebase_id': '/m/06m11'},
    {'id': 3, 'name': 'Flag', 'freebase_id': '/m/03120'},
    {'id': 4, 'name': 'Flashlight', 'freebase_id': '/m/01kb5b'},
    {'id': 5, 'name': 'Sea turtle', 'freebase_id': '/m/0120dh'},
    {'id': 6, 'name': 'Camera', 'freebase_id': '/m/0dv5r'},
    {'id': 7, 'name': 'Animal', 'freebase_id': '/m/0jbk'},
    {'id': 8, 'name': '<PERSON><PERSON>', 'freebase_id': '/m/0174n1'},
    {'id': 9, 'name': 'C<PERSON>', 'freebase_id': '/m/09f_2'},
    {'id': 10, 'name': 'Cattle', 'freebase_id': '/m/01xq0k1'},
    {'id': 11, 'name': 'House', 'freebase_id': '/m/03jm5'},
    {'id': 12, 'name': 'Guacamole', 'freebase_id': '/m/02g30s'},
    {'id': 13, 'name': 'Penguin', 'freebase_id': '/m/05z6w'},
    {'id': 14, 'name': 'Vehicle registration plate', 'freebase_id': '/m/01jfm_'},
    {'id': 15, 'name': 'Bench', 'freebase_id': '/m/076lb9'},
    {'id': 16, 'name': 'Ladybug', 'freebase_id': '/m/0gj37'},
    {'id': 17, 'name': 'Human nose', 'freebase_id': '/m/0k0pj'},
    {'id': 18, 'name': 'Watermelon', 'freebase_id': '/m/0kpqd'},
    {'id': 19, 'name': 'Flute', 'freebase_id': '/m/0l14j_'},
    {'id': 20, 'name': 'Butterfly', 'freebase_id': '/m/0cyf8'},
    {'id': 21, 'name': 'Washing machine', 'freebase_id': '/m/0174k2'},
    {'id': 22, 'name': 'Raccoon', 'freebase_id': '/m/0dq75'},
    {'id': 23, 'name': 'Segway', 'freebase_id': '/m/076bq'},
    {'id': 24, 'name': 'Taco', 'freebase_id': '/m/07crc'},
    {'id': 25, 'name': 'Jellyfish', 'freebase_id': '/m/0d8zb'},
    {'id': 26, 'name': 'Cake', 'freebase_id': '/m/0fszt'},
    {'id': 27, 'name': 'Pen', 'freebase_id': '/m/0k1tl'},
    {'id': 28, 'name': 'Cannon', 'freebase_id': '/m/020kz'},
    {'id': 29, 'name': 'Bread', 'freebase_id': '/m/09728'},
    {'id': 30, 'name': 'Tree', 'freebase_id': '/m/07j7r'},
    {'id': 31, 'name': 'Shellfish', 'freebase_id': '/m/0fbdv'},
    {'id': 32, 'name': 'Bed', 'freebase_id': '/m/03ssj5'},
    {'id': 33, 'name': 'Hamster', 'freebase_id': '/m/03qrc'},
    {'id': 34, 'name': 'Hat', 'freebase_id': '/m/02dl1y'},
    {'id': 35, 'name': 'Toaster', 'freebase_id': '/m/01k6s3'},
    {'id': 36, 'name': 'Sombrero', 'freebase_id': '/m/02jfl0'},
    {'id': 37, 'name': 'Tiara', 'freebase_id': '/m/01krhy'},
    {'id': 38, 'name': 'Bowl', 'freebase_id': '/m/04kkgm'},
    {'id': 39, 'name': 'Dragonfly', 'freebase_id': '/m/0ft9s'},
    {'id': 40, 'name': 'Moths and butterflies', 'freebase_id': '/m/0d_2m'},
    {'id': 41, 'name': 'Antelope', 'freebase_id': '/m/0czz2'},
    {'id': 42, 'name': 'Vegetable', 'freebase_id': '/m/0f4s2w'},
    {'id': 43, 'name': 'Torch', 'freebase_id': '/m/07dd4'},
    {'id': 44, 'name': 'Building', 'freebase_id': '/m/0cgh4'},
    {'id': 45, 'name': 'Power plugs and sockets', 'freebase_id': '/m/03bbps'},
    {'id': 46, 'name': 'Blender', 'freebase_id': '/m/02pjr4'},
    {'id': 47, 'name': 'Billiard table', 'freebase_id': '/m/04p0qw'},
    {'id': 48, 'name': 'Cutting board', 'freebase_id': '/m/02pdsw'},
    {'id': 49, 'name': 'Bronze sculpture', 'freebase_id': '/m/01yx86'},
    {'id': 50, 'name': 'Turtle', 'freebase_id': '/m/09dzg'},
    {'id': 51, 'name': 'Broccoli', 'freebase_id': '/m/0hkxq'},
    {'id': 52, 'name': 'Tiger', 'freebase_id': '/m/07dm6'},
    {'id': 53, 'name': 'Mirror', 'freebase_id': '/m/054_l'},
    {'id': 54, 'name': 'Bear', 'freebase_id': '/m/01dws'},
    {'id': 55, 'name': 'Zucchini', 'freebase_id': '/m/027pcv'},
    {'id': 56, 'name': 'Dress', 'freebase_id': '/m/01d40f'},
    {'id': 57, 'name': 'Volleyball', 'freebase_id': '/m/02rgn06'},
    {'id': 58, 'name': 'Guitar', 'freebase_id': '/m/0342h'},
    {'id': 59, 'name': 'Reptile', 'freebase_id': '/m/06bt6'},
    {'id': 60, 'name': 'Golf cart', 'freebase_id': '/m/0323sq'},
    {'id': 61, 'name': 'Tart', 'freebase_id': '/m/02zvsm'},
    {'id': 62, 'name': 'Fedora', 'freebase_id': '/m/02fq_6'},
    {'id': 63, 'name': 'Carnivore', 'freebase_id': '/m/01lrl'},
    {'id': 64, 'name': 'Car', 'freebase_id': '/m/0k4j'},
    {'id': 65, 'name': 'Lighthouse', 'freebase_id': '/m/04h7h'},
    {'id': 66, 'name': 'Coffeemaker', 'freebase_id': '/m/07xyvk'},
    {'id': 67, 'name': 'Food processor', 'freebase_id': '/m/03y6mg'},
    {'id': 68, 'name': 'Truck', 'freebase_id': '/m/07r04'},
    {'id': 69, 'name': 'Bookcase', 'freebase_id': '/m/03__z0'},
    {'id': 70, 'name': 'Surfboard', 'freebase_id': '/m/019w40'},
    {'id': 71, 'name': 'Footwear', 'freebase_id': '/m/09j5n'},
    {'id': 72, 'name': 'Bench', 'freebase_id': '/m/0cvnqh'},
    {'id': 73, 'name': 'Necklace', 'freebase_id': '/m/01llwg'},
    {'id': 74, 'name': 'Flower', 'freebase_id': '/m/0c9ph5'},
    {'id': 75, 'name': 'Radish', 'freebase_id': '/m/015x5n'},
    {'id': 76, 'name': 'Marine mammal', 'freebase_id': '/m/0gd2v'},
    {'id': 77, 'name': 'Frying pan', 'freebase_id': '/m/04v6l4'},
    {'id': 78, 'name': 'Tap', 'freebase_id': '/m/02jz0l'},
    {'id': 79, 'name': 'Peach', 'freebase_id': '/m/0dj6p'},
    {'id': 80, 'name': 'Knife', 'freebase_id': '/m/04ctx'},
    {'id': 81, 'name': 'Handbag', 'freebase_id': '/m/080hkjn'},
    {'id': 82, 'name': 'Laptop', 'freebase_id': '/m/01c648'},
    {'id': 83, 'name': 'Tent', 'freebase_id': '/m/01j61q'},
    {'id': 84, 'name': 'Ambulance', 'freebase_id': '/m/012n7d'},
    {'id': 85, 'name': 'Christmas tree', 'freebase_id': '/m/025nd'},
    {'id': 86, 'name': 'Eagle', 'freebase_id': '/m/09csl'},
    {'id': 87, 'name': 'Limousine', 'freebase_id': '/m/01lcw4'},
    {'id': 88, 'name': 'Kitchen & dining room table', 'freebase_id': '/m/0h8n5zk'},
    {'id': 89, 'name': 'Polar bear', 'freebase_id': '/m/0633h'},
    {'id': 90, 'name': 'Tower', 'freebase_id': '/m/01fdzj'},
    {'id': 91, 'name': 'Football', 'freebase_id': '/m/01226z'},
    {'id': 92, 'name': 'Willow', 'freebase_id': '/m/0mw_6'},
    {'id': 93, 'name': 'Human head', 'freebase_id': '/m/04hgtk'},
    {'id': 94, 'name': 'Stop sign', 'freebase_id': '/m/02pv19'},
    {'id': 95, 'name': 'Banana', 'freebase_id': '/m/09qck'},
    {'id': 96, 'name': 'Mixer', 'freebase_id': '/m/063rgb'},
    {'id': 97, 'name': 'Binoculars', 'freebase_id': '/m/0lt4_'},
    {'id': 98, 'name': 'Dessert', 'freebase_id': '/m/0270h'},
    {'id': 99, 'name': 'Bee', 'freebase_id': '/m/01h3n'},
    {'id': 100, 'name': 'Chair', 'freebase_id': '/m/01mzpv'},
    {'id': 101, 'name': 'Wood-burning stove', 'freebase_id': '/m/04169hn'},
    {'id': 102, 'name': 'Flowerpot', 'freebase_id': '/m/0fm3zh'},
    {'id': 103, 'name': 'Beaker', 'freebase_id': '/m/0d20w4'},
    {'id': 104, 'name': 'Oyster', 'freebase_id': '/m/0_cp5'},
    {'id': 105, 'name': 'Woodpecker', 'freebase_id': '/m/01dy8n'},
    {'id': 106, 'name': 'Harp', 'freebase_id': '/m/03m5k'},
    {'id': 107, 'name': 'Bathtub', 'freebase_id': '/m/03dnzn'},
    {'id': 108, 'name': 'Wall clock', 'freebase_id': '/m/0h8mzrc'},
    {'id': 109, 'name': 'Sports uniform', 'freebase_id': '/m/0h8mhzd'},
    {'id': 110, 'name': 'Rhinoceros', 'freebase_id': '/m/03d443'},
    {'id': 111, 'name': 'Beehive', 'freebase_id': '/m/01gllr'},
    {'id': 112, 'name': 'Cupboard', 'freebase_id': '/m/0642b4'},
    {'id': 113, 'name': 'Chicken', 'freebase_id': '/m/09b5t'},
    {'id': 114, 'name': 'Man', 'freebase_id': '/m/04yx4'},
    {'id': 115, 'name': 'Blue jay', 'freebase_id': '/m/01f8m5'},
    {'id': 116, 'name': 'Cucumber', 'freebase_id': '/m/015x4r'},
    {'id': 117, 'name': 'Balloon', 'freebase_id': '/m/01j51'},
    {'id': 118, 'name': 'Kite', 'freebase_id': '/m/02zt3'},
    {'id': 119, 'name': 'Fireplace', 'freebase_id': '/m/03tw93'},
    {'id': 120, 'name': 'Lantern', 'freebase_id': '/m/01jfsr'},
    {'id': 121, 'name': 'Missile', 'freebase_id': '/m/04ylt'},
    {'id': 122, 'name': 'Book', 'freebase_id': '/m/0bt_c3'},
    {'id': 123, 'name': 'Spoon', 'freebase_id': '/m/0cmx8'},
    {'id': 124, 'name': 'Grapefruit', 'freebase_id': '/m/0hqkz'},
    {'id': 125, 'name': 'Squirrel', 'freebase_id': '/m/071qp'},
    {'id': 126, 'name': 'Orange', 'freebase_id': '/m/0cyhj_'},
    {'id': 127, 'name': 'Coat', 'freebase_id': '/m/01xygc'},
    {'id': 128, 'name': 'Punching bag', 'freebase_id': '/m/0420v5'},
    {'id': 129, 'name': 'Zebra', 'freebase_id': '/m/0898b'},
    {'id': 130, 'name': 'Billboard', 'freebase_id': '/m/01knjb'},
    {'id': 131, 'name': 'Bicycle', 'freebase_id': '/m/0199g'},
    {'id': 132, 'name': 'Door handle', 'freebase_id': '/m/03c7gz'},
    {'id': 133, 'name': 'Mechanical fan', 'freebase_id': '/m/02x984l'},
    {'id': 134, 'name': 'Ring binder', 'freebase_id': '/m/04zwwv'},
    {'id': 135, 'name': 'Table', 'freebase_id': '/m/04bcr3'},
    {'id': 136, 'name': 'Parrot', 'freebase_id': '/m/0gv1x'},
    {'id': 137, 'name': 'Sock', 'freebase_id': '/m/01nq26'},
    {'id': 138, 'name': 'Vase', 'freebase_id': '/m/02s195'},
    {'id': 139, 'name': 'Weapon', 'freebase_id': '/m/083kb'},
    {'id': 140, 'name': 'Shotgun', 'freebase_id': '/m/06nrc'},
    {'id': 141, 'name': 'Glasses', 'freebase_id': '/m/0jyfg'},
    {'id': 142, 'name': 'Seahorse', 'freebase_id': '/m/0nybt'},
    {'id': 143, 'name': 'Belt', 'freebase_id': '/m/0176mf'},
    {'id': 144, 'name': 'Watercraft', 'freebase_id': '/m/01rzcn'},
    {'id': 145, 'name': 'Window', 'freebase_id': '/m/0d4v4'},
    {'id': 146, 'name': 'Giraffe', 'freebase_id': '/m/03bk1'},
    {'id': 147, 'name': 'Lion', 'freebase_id': '/m/096mb'},
    {'id': 148, 'name': 'Tire', 'freebase_id': '/m/0h9mv'},
    {'id': 149, 'name': 'Vehicle', 'freebase_id': '/m/07yv9'},
    {'id': 150, 'name': 'Canoe', 'freebase_id': '/m/0ph39'},
    {'id': 151, 'name': 'Tie', 'freebase_id': '/m/01rkbr'},
    {'id': 152, 'name': 'Shelf', 'freebase_id': '/m/0gjbg72'},
    {'id': 153, 'name': 'Picture frame', 'freebase_id': '/m/06z37_'},
    {'id': 154, 'name': 'Printer', 'freebase_id': '/m/01m4t'},
    {'id': 155, 'name': 'Human leg', 'freebase_id': '/m/035r7c'},
    {'id': 156, 'name': 'Boat', 'freebase_id': '/m/019jd'},
    {'id': 157, 'name': 'Slow cooker', 'freebase_id': '/m/02tsc9'},
    {'id': 158, 'name': 'Croissant', 'freebase_id': '/m/015wgc'},
    {'id': 159, 'name': 'Candle', 'freebase_id': '/m/0c06p'},
    {'id': 160, 'name': 'Pancake', 'freebase_id': '/m/01dwwc'},
    {'id': 161, 'name': 'Pillow', 'freebase_id': '/m/034c16'},
    {'id': 162, 'name': 'Coin', 'freebase_id': '/m/0242l'},
    {'id': 163, 'name': 'Stretcher', 'freebase_id': '/m/02lbcq'},
    {'id': 164, 'name': 'Sandal', 'freebase_id': '/m/03nfch'},
    {'id': 165, 'name': 'Woman', 'freebase_id': '/m/03bt1vf'},
    {'id': 166, 'name': 'Stairs', 'freebase_id': '/m/01lynh'},
    {'id': 167, 'name': 'Harpsichord', 'freebase_id': '/m/03q5t'},
    {'id': 168, 'name': 'Stool', 'freebase_id': '/m/0fqt361'},
    {'id': 169, 'name': 'Bus', 'freebase_id': '/m/01bjv'},
    {'id': 170, 'name': 'Suitcase', 'freebase_id': '/m/01s55n'},
    {'id': 171, 'name': 'Human mouth', 'freebase_id': '/m/0283dt1'},
    {'id': 172, 'name': 'Juice', 'freebase_id': '/m/01z1kdw'},
    {'id': 173, 'name': 'Skull', 'freebase_id': '/m/016m2d'},
    {'id': 174, 'name': 'Door', 'freebase_id': '/m/02dgv'},
    {'id': 175, 'name': 'Violin', 'freebase_id': '/m/07y_7'},
    {'id': 176, 'name': 'Chopsticks', 'freebase_id': '/m/01_5g'},
    {'id': 177, 'name': 'Digital clock', 'freebase_id': '/m/06_72j'},
    {'id': 178, 'name': 'Sunflower', 'freebase_id': '/m/0ftb8'},
    {'id': 179, 'name': 'Leopard', 'freebase_id': '/m/0c29q'},
    {'id': 180, 'name': 'Bell pepper', 'freebase_id': '/m/0jg57'},
    {'id': 181, 'name': 'Harbor seal', 'freebase_id': '/m/02l8p9'},
    {'id': 182, 'name': 'Snake', 'freebase_id': '/m/078jl'},
    {'id': 183, 'name': 'Sewing machine', 'freebase_id': '/m/0llzx'},
    {'id': 184, 'name': 'Goose', 'freebase_id': '/m/0dbvp'},
    {'id': 185, 'name': 'Helicopter', 'freebase_id': '/m/09ct_'},
    {'id': 186, 'name': 'Seat belt', 'freebase_id': '/m/0dkzw'},
    {'id': 187, 'name': 'Coffee cup', 'freebase_id': '/m/02p5f1q'},
    {'id': 188, 'name': 'Microwave oven', 'freebase_id': '/m/0fx9l'},
    {'id': 189, 'name': 'Hot dog', 'freebase_id': '/m/01b9xk'},
    {'id': 190, 'name': 'Countertop', 'freebase_id': '/m/0b3fp9'},
    {'id': 191, 'name': 'Serving tray', 'freebase_id': '/m/0h8n27j'},
    {'id': 192, 'name': 'Dog bed', 'freebase_id': '/m/0h8n6f9'},
    {'id': 193, 'name': 'Beer', 'freebase_id': '/m/01599'},
    {'id': 194, 'name': 'Sunglasses', 'freebase_id': '/m/017ftj'},
    {'id': 195, 'name': 'Golf ball', 'freebase_id': '/m/044r5d'},
    {'id': 196, 'name': 'Waffle', 'freebase_id': '/m/01dwsz'},
    {'id': 197, 'name': 'Palm tree', 'freebase_id': '/m/0cdl1'},
    {'id': 198, 'name': 'Trumpet', 'freebase_id': '/m/07gql'},
    {'id': 199, 'name': 'Ruler', 'freebase_id': '/m/0hdln'},
    {'id': 200, 'name': 'Helmet', 'freebase_id': '/m/0zvk5'},
    {'id': 201, 'name': 'Ladder', 'freebase_id': '/m/012w5l'},
    {'id': 202, 'name': 'Office building', 'freebase_id': '/m/021sj1'},
    {'id': 203, 'name': 'Tablet computer', 'freebase_id': '/m/0bh9flk'},
    {'id': 204, 'name': 'Toilet paper', 'freebase_id': '/m/09gtd'},
    {'id': 205, 'name': 'Pomegranate', 'freebase_id': '/m/0jwn_'},
    {'id': 206, 'name': 'Skirt', 'freebase_id': '/m/02wv6h6'},
    {'id': 207, 'name': 'Gas stove', 'freebase_id': '/m/02wv84t'},
    {'id': 208, 'name': 'Cookie', 'freebase_id': '/m/021mn'},
    {'id': 209, 'name': 'Cart', 'freebase_id': '/m/018p4k'},
    {'id': 210, 'name': 'Raven', 'freebase_id': '/m/06j2d'},
    {'id': 211, 'name': 'Egg', 'freebase_id': '/m/033cnk'},
    {'id': 212, 'name': 'Burrito', 'freebase_id': '/m/01j3zr'},
    {'id': 213, 'name': 'Goat', 'freebase_id': '/m/03fwl'},
    {'id': 214, 'name': 'Kitchen knife', 'freebase_id': '/m/058qzx'},
    {'id': 215, 'name': 'Skateboard', 'freebase_id': '/m/06_fw'},
    {'id': 216, 'name': 'Salt and pepper shakers', 'freebase_id': '/m/02x8cch'},
    {'id': 217, 'name': 'Lynx', 'freebase_id': '/m/04g2r'},
    {'id': 218, 'name': 'Boot', 'freebase_id': '/m/01b638'},
    {'id': 219, 'name': 'Platter', 'freebase_id': '/m/099ssp'},
    {'id': 220, 'name': 'Ski', 'freebase_id': '/m/071p9'},
    {'id': 221, 'name': 'Swimwear', 'freebase_id': '/m/01gkx_'},
    {'id': 222, 'name': 'Swimming pool', 'freebase_id': '/m/0b_rs'},
    {'id': 223, 'name': 'Drinking straw', 'freebase_id': '/m/03v5tg'},
    {'id': 224, 'name': 'Wrench', 'freebase_id': '/m/01j5ks'},
    {'id': 225, 'name': 'Drum', 'freebase_id': '/m/026t6'},
    {'id': 226, 'name': 'Ant', 'freebase_id': '/m/0_k2'},
    {'id': 227, 'name': 'Human ear', 'freebase_id': '/m/039xj_'},
    {'id': 228, 'name': 'Headphones', 'freebase_id': '/m/01b7fy'},
    {'id': 229, 'name': 'Fountain', 'freebase_id': '/m/0220r2'},
    {'id': 230, 'name': 'Bird', 'freebase_id': '/m/015p6'},
    {'id': 231, 'name': 'Jeans', 'freebase_id': '/m/0fly7'},
    {'id': 232, 'name': 'Television', 'freebase_id': '/m/07c52'},
    {'id': 233, 'name': 'Crab', 'freebase_id': '/m/0n28_'},
    {'id': 234, 'name': 'Microphone', 'freebase_id': '/m/0hg7b'},
    {'id': 235, 'name': 'Home appliance', 'freebase_id': '/m/019dx1'},
    {'id': 236, 'name': 'Snowplow', 'freebase_id': '/m/04vv5k'},
    {'id': 237, 'name': 'Beetle', 'freebase_id': '/m/020jm'},
    {'id': 238, 'name': 'Artichoke', 'freebase_id': '/m/047v4b'},
    {'id': 239, 'name': 'Jet ski', 'freebase_id': '/m/01xs3r'},
    {'id': 240, 'name': 'Stationary bicycle', 'freebase_id': '/m/03kt2w'},
    {'id': 241, 'name': 'Human hair', 'freebase_id': '/m/03q69'},
    {'id': 242, 'name': 'Brown bear', 'freebase_id': '/m/01dxs'},
    {'id': 243, 'name': 'Starfish', 'freebase_id': '/m/01h8tj'},
    {'id': 244, 'name': 'Fork', 'freebase_id': '/m/0dt3t'},
    {'id': 245, 'name': 'Lobster', 'freebase_id': '/m/0cjq5'},
    {'id': 246, 'name': 'Corded phone', 'freebase_id': '/m/0h8lkj8'},
    {'id': 247, 'name': 'Drink', 'freebase_id': '/m/0271t'},
    {'id': 248, 'name': 'Saucer', 'freebase_id': '/m/03q5c7'},
    {'id': 249, 'name': 'Carrot', 'freebase_id': '/m/0fj52s'},
    {'id': 250, 'name': 'Insect', 'freebase_id': '/m/03vt0'},
    {'id': 251, 'name': 'Clock', 'freebase_id': '/m/01x3z'},
    {'id': 252, 'name': 'Castle', 'freebase_id': '/m/0d5gx'},
    {'id': 253, 'name': 'Tennis racket', 'freebase_id': '/m/0h8my_4'},
    {'id': 254, 'name': 'Ceiling fan', 'freebase_id': '/m/03ldnb'},
    {'id': 255, 'name': 'Asparagus', 'freebase_id': '/m/0cjs7'},
    {'id': 256, 'name': 'Jaguar', 'freebase_id': '/m/0449p'},
    {'id': 257, 'name': 'Musical instrument', 'freebase_id': '/m/04szw'},
    {'id': 258, 'name': 'Train', 'freebase_id': '/m/07jdr'},
    {'id': 259, 'name': 'Cat', 'freebase_id': '/m/01yrx'},
    {'id': 260, 'name': 'Rifle', 'freebase_id': '/m/06c54'},
    {'id': 261, 'name': 'Dumbbell', 'freebase_id': '/m/04h8sr'},
    {'id': 262, 'name': 'Mobile phone', 'freebase_id': '/m/050k8'},
    {'id': 263, 'name': 'Taxi', 'freebase_id': '/m/0pg52'},
    {'id': 264, 'name': 'Shower', 'freebase_id': '/m/02f9f_'},
    {'id': 265, 'name': 'Pitcher', 'freebase_id': '/m/054fyh'},
    {'id': 266, 'name': 'Lemon', 'freebase_id': '/m/09k_b'},
    {'id': 267, 'name': 'Invertebrate', 'freebase_id': '/m/03xxp'},
    {'id': 268, 'name': 'Turkey', 'freebase_id': '/m/0jly1'},
    {'id': 269, 'name': 'High heels', 'freebase_id': '/m/06k2mb'},
    {'id': 270, 'name': 'Bust', 'freebase_id': '/m/04yqq2'},
    {'id': 271, 'name': 'Elephant', 'freebase_id': '/m/0bwd_0j'},
    {'id': 272, 'name': 'Scarf', 'freebase_id': '/m/02h19r'},
    {'id': 273, 'name': 'Barrel', 'freebase_id': '/m/02zn6n'},
    {'id': 274, 'name': 'Trombone', 'freebase_id': '/m/07c6l'},
    {'id': 275, 'name': 'Pumpkin', 'freebase_id': '/m/05zsy'},
    {'id': 276, 'name': 'Box', 'freebase_id': '/m/025dyy'},
    {'id': 277, 'name': 'Tomato', 'freebase_id': '/m/07j87'},
    {'id': 278, 'name': 'Frog', 'freebase_id': '/m/09ld4'},
    {'id': 279, 'name': 'Bidet', 'freebase_id': '/m/01vbnl'},
    {'id': 280, 'name': 'Human face', 'freebase_id': '/m/0dzct'},
    {'id': 281, 'name': 'Houseplant', 'freebase_id': '/m/03fp41'},
    {'id': 282, 'name': 'Van', 'freebase_id': '/m/0h2r6'},
    {'id': 283, 'name': 'Shark', 'freebase_id': '/m/0by6g'},
    {'id': 284, 'name': 'Ice cream', 'freebase_id': '/m/0cxn2'},
    {'id': 285, 'name': 'Swim cap', 'freebase_id': '/m/04tn4x'},
    {'id': 286, 'name': 'Falcon', 'freebase_id': '/m/0f6wt'},
    {'id': 287, 'name': 'Ostrich', 'freebase_id': '/m/05n4y'},
    {'id': 288, 'name': 'Handgun', 'freebase_id': '/m/0gxl3'},
    {'id': 289, 'name': 'Whiteboard', 'freebase_id': '/m/02d9qx'},
    {'id': 290, 'name': 'Lizard', 'freebase_id': '/m/04m9y'},
    {'id': 291, 'name': 'Pasta', 'freebase_id': '/m/05z55'},
    {'id': 292, 'name': 'Snowmobile', 'freebase_id': '/m/01x3jk'},
    {'id': 293, 'name': 'Light bulb', 'freebase_id': '/m/0h8l4fh'},
    {'id': 294, 'name': 'Window blind', 'freebase_id': '/m/031b6r'},
    {'id': 295, 'name': 'Muffin', 'freebase_id': '/m/01tcjp'},
    {'id': 296, 'name': 'Pretzel', 'freebase_id': '/m/01f91_'},
    {'id': 297, 'name': 'Computer monitor', 'freebase_id': '/m/02522'},
    {'id': 298, 'name': 'Horn', 'freebase_id': '/m/0319l'},
    {'id': 299, 'name': 'Furniture', 'freebase_id': '/m/0c_jw'},
    {'id': 300, 'name': 'Sandwich', 'freebase_id': '/m/0l515'},
    {'id': 301, 'name': 'Fox', 'freebase_id': '/m/0306r'},
    {'id': 302, 'name': 'Convenience store', 'freebase_id': '/m/0crjs'},
    {'id': 303, 'name': 'Fish', 'freebase_id': '/m/0ch_cf'},
    {'id': 304, 'name': 'Fruit', 'freebase_id': '/m/02xwb'},
    {'id': 305, 'name': 'Earrings', 'freebase_id': '/m/01r546'},
    {'id': 306, 'name': 'Curtain', 'freebase_id': '/m/03rszm'},
    {'id': 307, 'name': 'Grape', 'freebase_id': '/m/0388q'},
    {'id': 308, 'name': 'Sofa bed', 'freebase_id': '/m/03m3pdh'},
    {'id': 309, 'name': 'Horse', 'freebase_id': '/m/03k3r'},
    {'id': 310, 'name': 'Luggage and bags', 'freebase_id': '/m/0hf58v5'},
    {'id': 311, 'name': 'Desk', 'freebase_id': '/m/01y9k5'},
    {'id': 312, 'name': 'Crutch', 'freebase_id': '/m/05441v'},
    {'id': 313, 'name': 'Bicycle helmet', 'freebase_id': '/m/03p3bw'},
    {'id': 314, 'name': 'Tick', 'freebase_id': '/m/0175cv'},
    {'id': 315, 'name': 'Airplane', 'freebase_id': '/m/0cmf2'},
    {'id': 316, 'name': 'Canary', 'freebase_id': '/m/0ccs93'},
    {'id': 317, 'name': 'Spatula', 'freebase_id': '/m/02d1br'},
    {'id': 318, 'name': 'Watch', 'freebase_id': '/m/0gjkl'},
    {'id': 319, 'name': 'Lily', 'freebase_id': '/m/0jqgx'},
    {'id': 320, 'name': 'Kitchen appliance', 'freebase_id': '/m/0h99cwc'},
    {'id': 321, 'name': 'Filing cabinet', 'freebase_id': '/m/047j0r'},
    {'id': 322, 'name': 'Aircraft', 'freebase_id': '/m/0k5j'},
    {'id': 323, 'name': 'Cake stand', 'freebase_id': '/m/0h8n6ft'},
    {'id': 324, 'name': 'Candy', 'freebase_id': '/m/0gm28'},
    {'id': 325, 'name': 'Sink', 'freebase_id': '/m/0130jx'},
    {'id': 326, 'name': 'Mouse', 'freebase_id': '/m/04rmv'},
    {'id': 327, 'name': 'Wine', 'freebase_id': '/m/081qc'},
    {'id': 328, 'name': 'Wheelchair', 'freebase_id': '/m/0qmmr'},
    {'id': 329, 'name': 'Goldfish', 'freebase_id': '/m/03fj2'},
    {'id': 330, 'name': 'Refrigerator', 'freebase_id': '/m/040b_t'},
    {'id': 331, 'name': 'French fries', 'freebase_id': '/m/02y6n'},
    {'id': 332, 'name': 'Drawer', 'freebase_id': '/m/0fqfqc'},
    {'id': 333, 'name': 'Treadmill', 'freebase_id': '/m/030610'},
    {'id': 334, 'name': 'Picnic basket', 'freebase_id': '/m/07kng9'},
    {'id': 335, 'name': 'Dice', 'freebase_id': '/m/029b3'},
    {'id': 336, 'name': 'Cabbage', 'freebase_id': '/m/0fbw6'},
    {'id': 337, 'name': 'Football helmet', 'freebase_id': '/m/07qxg_'},
    {'id': 338, 'name': 'Pig', 'freebase_id': '/m/068zj'},
    {'id': 339, 'name': 'Person', 'freebase_id': '/m/01g317'},
    {'id': 340, 'name': 'Shorts', 'freebase_id': '/m/01bfm9'},
    {'id': 341, 'name': 'Gondola', 'freebase_id': '/m/02068x'},
    {'id': 342, 'name': 'Honeycomb', 'freebase_id': '/m/0fz0h'},
    {'id': 343, 'name': 'Doughnut', 'freebase_id': '/m/0jy4k'},
    {'id': 344, 'name': 'Chest of drawers', 'freebase_id': '/m/05kyg_'},
    {'id': 345, 'name': 'Land vehicle', 'freebase_id': '/m/01prls'},
    {'id': 346, 'name': 'Bat', 'freebase_id': '/m/01h44'},
    {'id': 347, 'name': 'Monkey', 'freebase_id': '/m/08pbxl'},
    {'id': 348, 'name': 'Dagger', 'freebase_id': '/m/02gzp'},
    {'id': 349, 'name': 'Tableware', 'freebase_id': '/m/04brg2'},
    {'id': 350, 'name': 'Human foot', 'freebase_id': '/m/031n1'},
    {'id': 351, 'name': 'Mug', 'freebase_id': '/m/02jvh9'},
    {'id': 352, 'name': 'Alarm clock', 'freebase_id': '/m/046dlr'},
    {'id': 353, 'name': 'Pressure cooker', 'freebase_id': '/m/0h8ntjv'},
    {'id': 354, 'name': 'Human hand', 'freebase_id': '/m/0k65p'},
    {'id': 355, 'name': 'Tortoise', 'freebase_id': '/m/011k07'},
    {'id': 356, 'name': 'Baseball glove', 'freebase_id': '/m/03grzl'},
    {'id': 357, 'name': 'Sword', 'freebase_id': '/m/06y5r'},
    {'id': 358, 'name': 'Pear', 'freebase_id': '/m/061_f'},
    {'id': 359, 'name': 'Miniskirt', 'freebase_id': '/m/01cmb2'},
    {'id': 360, 'name': 'Traffic sign', 'freebase_id': '/m/01mqdt'},
    {'id': 361, 'name': 'Girl', 'freebase_id': '/m/05r655'},
    {'id': 362, 'name': 'Roller skates', 'freebase_id': '/m/02p3w7d'},
    {'id': 363, 'name': 'Dinosaur', 'freebase_id': '/m/029tx'},
    {'id': 364, 'name': 'Porch', 'freebase_id': '/m/04m6gz'},
    {'id': 365, 'name': 'Human beard', 'freebase_id': '/m/015h_t'},
    {'id': 366, 'name': 'Submarine sandwich', 'freebase_id': '/m/06pcq'},
    {'id': 367, 'name': 'Screwdriver', 'freebase_id': '/m/01bms0'},
    {'id': 368, 'name': 'Strawberry', 'freebase_id': '/m/07fbm7'},
    {'id': 369, 'name': 'Wine glass', 'freebase_id': '/m/09tvcd'},
    {'id': 370, 'name': 'Seafood', 'freebase_id': '/m/06nwz'},
    {'id': 371, 'name': 'Racket', 'freebase_id': '/m/0dv9c'},
    {'id': 372, 'name': 'Wheel', 'freebase_id': '/m/083wq'},
    {'id': 373, 'name': 'Sea lion', 'freebase_id': '/m/0gd36'},
    {'id': 374, 'name': 'Toy', 'freebase_id': '/m/0138tl'},
    {'id': 375, 'name': 'Tea', 'freebase_id': '/m/07clx'},
    {'id': 376, 'name': 'Tennis ball', 'freebase_id': '/m/05ctyq'},
    {'id': 377, 'name': 'Waste container', 'freebase_id': '/m/0bjyj5'},
    {'id': 378, 'name': 'Mule', 'freebase_id': '/m/0dbzx'},
    {'id': 379, 'name': 'Cricket ball', 'freebase_id': '/m/02ctlc'},
    {'id': 380, 'name': 'Pineapple', 'freebase_id': '/m/0fp6w'},
    {'id': 381, 'name': 'Coconut', 'freebase_id': '/m/0djtd'},
    {'id': 382, 'name': 'Doll', 'freebase_id': '/m/0167gd'},
    {'id': 383, 'name': 'Coffee table', 'freebase_id': '/m/078n6m'},
    {'id': 384, 'name': 'Snowman', 'freebase_id': '/m/0152hh'},
    {'id': 385, 'name': 'Lavender', 'freebase_id': '/m/04gth'},
    {'id': 386, 'name': 'Shrimp', 'freebase_id': '/m/0ll1f78'},
    {'id': 387, 'name': 'Maple', 'freebase_id': '/m/0cffdh'},
    {'id': 388, 'name': 'Cowboy hat', 'freebase_id': '/m/025rp__'},
    {'id': 389, 'name': 'Goggles', 'freebase_id': '/m/02_n6y'},
    {'id': 390, 'name': 'Rugby ball', 'freebase_id': '/m/0wdt60w'},
    {'id': 391, 'name': 'Caterpillar', 'freebase_id': '/m/0cydv'},
    {'id': 392, 'name': 'Poster', 'freebase_id': '/m/01n5jq'},
    {'id': 393, 'name': 'Rocket', 'freebase_id': '/m/09rvcxw'},
    {'id': 394, 'name': 'Organ', 'freebase_id': '/m/013y1f'},
    {'id': 395, 'name': 'Saxophone', 'freebase_id': '/m/06ncr'},
    {'id': 396, 'name': 'Traffic light', 'freebase_id': '/m/015qff'},
    {'id': 397, 'name': 'Cocktail', 'freebase_id': '/m/024g6'},
    {'id': 398, 'name': 'Plastic bag', 'freebase_id': '/m/05gqfk'},
    {'id': 399, 'name': 'Squash', 'freebase_id': '/m/0dv77'},
    {'id': 400, 'name': 'Mushroom', 'freebase_id': '/m/052sf'},
    {'id': 401, 'name': 'Hamburger', 'freebase_id': '/m/0cdn1'},
    {'id': 402, 'name': 'Light switch', 'freebase_id': '/m/03jbxj'},
    {'id': 403, 'name': 'Parachute', 'freebase_id': '/m/0cyfs'},
    {'id': 404, 'name': 'Teddy bear', 'freebase_id': '/m/0kmg4'},
    {'id': 405, 'name': 'Winter melon', 'freebase_id': '/m/02cvgx'},
    {'id': 406, 'name': 'Deer', 'freebase_id': '/m/09kx5'},
    {'id': 407, 'name': 'Musical keyboard', 'freebase_id': '/m/057cc'},
    {'id': 408, 'name': 'Plumbing fixture', 'freebase_id': '/m/02pkr5'},
    {'id': 409, 'name': 'Scoreboard', 'freebase_id': '/m/057p5t'},
    {'id': 410, 'name': 'Baseball bat', 'freebase_id': '/m/03g8mr'},
    {'id': 411, 'name': 'Envelope', 'freebase_id': '/m/0frqm'},
    {'id': 412, 'name': 'Adhesive tape', 'freebase_id': '/m/03m3vtv'},
    {'id': 413, 'name': 'Briefcase', 'freebase_id': '/m/0584n8'},
    {'id': 414, 'name': 'Paddle', 'freebase_id': '/m/014y4n'},
    {'id': 415, 'name': 'Bow and arrow', 'freebase_id': '/m/01g3x7'},
    {'id': 416, 'name': 'Telephone', 'freebase_id': '/m/07cx4'},
    {'id': 417, 'name': 'Sheep', 'freebase_id': '/m/07bgp'},
    {'id': 418, 'name': 'Jacket', 'freebase_id': '/m/032b3c'},
    {'id': 419, 'name': 'Boy', 'freebase_id': '/m/01bl7v'},
    {'id': 420, 'name': 'Pizza', 'freebase_id': '/m/0663v'},
    {'id': 421, 'name': 'Otter', 'freebase_id': '/m/0cn6p'},
    {'id': 422, 'name': 'Office supplies', 'freebase_id': '/m/02rdsp'},
    {'id': 423, 'name': 'Couch', 'freebase_id': '/m/02crq1'},
    {'id': 424, 'name': 'Cello', 'freebase_id': '/m/01xqw'},
    {'id': 425, 'name': 'Bull', 'freebase_id': '/m/0cnyhnx'},
    {'id': 426, 'name': 'Camel', 'freebase_id': '/m/01x_v'},
    {'id': 427, 'name': 'Ball', 'freebase_id': '/m/018xm'},
    {'id': 428, 'name': 'Duck', 'freebase_id': '/m/09ddx'},
    {'id': 429, 'name': 'Whale', 'freebase_id': '/m/084zz'},
    {'id': 430, 'name': 'Shirt', 'freebase_id': '/m/01n4qj'},
    {'id': 431, 'name': 'Tank', 'freebase_id': '/m/07cmd'},
    {'id': 432, 'name': 'Motorcycle', 'freebase_id': '/m/04_sv'},
    {'id': 433, 'name': 'Accordion', 'freebase_id': '/m/0mkg'},
    {'id': 434, 'name': 'Owl', 'freebase_id': '/m/09d5_'},
    {'id': 435, 'name': 'Porcupine', 'freebase_id': '/m/0c568'},
    {'id': 436, 'name': 'Sun hat', 'freebase_id': '/m/02wbtzl'},
    {'id': 437, 'name': 'Nail', 'freebase_id': '/m/05bm6'},
    {'id': 438, 'name': 'Scissors', 'freebase_id': '/m/01lsmm'},
    {'id': 439, 'name': 'Swan', 'freebase_id': '/m/0dftk'},
    {'id': 440, 'name': 'Lamp', 'freebase_id': '/m/0dtln'},
    {'id': 441, 'name': 'Crown', 'freebase_id': '/m/0nl46'},
    {'id': 442, 'name': 'Piano', 'freebase_id': '/m/05r5c'},
    {'id': 443, 'name': 'Sculpture', 'freebase_id': '/m/06msq'},
    {'id': 444, 'name': 'Cheetah', 'freebase_id': '/m/0cd4d'},
    {'id': 445, 'name': 'Oboe', 'freebase_id': '/m/05kms'},
    {'id': 446, 'name': 'Tin can', 'freebase_id': '/m/02jnhm'},
    {'id': 447, 'name': 'Mango', 'freebase_id': '/m/0fldg'},
    {'id': 448, 'name': 'Tripod', 'freebase_id': '/m/073bxn'},
    {'id': 449, 'name': 'Oven', 'freebase_id': '/m/029bxz'},
    {'id': 450, 'name': 'Mouse', 'freebase_id': '/m/020lf'},
    {'id': 451, 'name': 'Barge', 'freebase_id': '/m/01btn'},
    {'id': 452, 'name': 'Coffee', 'freebase_id': '/m/02vqfm'},
    {'id': 453, 'name': 'Snowboard', 'freebase_id': '/m/06__v'},
    {'id': 454, 'name': 'Common fig', 'freebase_id': '/m/043nyj'},
    {'id': 455, 'name': 'Salad', 'freebase_id': '/m/0grw1'},
    {'id': 456, 'name': 'Marine invertebrates', 'freebase_id': '/m/03hl4l9'},
    {'id': 457, 'name': 'Umbrella', 'freebase_id': '/m/0hnnb'},
    {'id': 458, 'name': 'Kangaroo', 'freebase_id': '/m/04c0y'},
    {'id': 459, 'name': 'Human arm', 'freebase_id': '/m/0dzf4'},
    {'id': 460, 'name': 'Measuring cup', 'freebase_id': '/m/07v9_z'},
    {'id': 461, 'name': 'Snail', 'freebase_id': '/m/0f9_l'},
    {'id': 462, 'name': 'Loveseat', 'freebase_id': '/m/0703r8'},
    {'id': 463, 'name': 'Suit', 'freebase_id': '/m/01xyhv'},
    {'id': 464, 'name': 'Teapot', 'freebase_id': '/m/01fh4r'},
    {'id': 465, 'name': 'Bottle', 'freebase_id': '/m/04dr76w'},
    {'id': 466, 'name': 'Alpaca', 'freebase_id': '/m/0pcr'},
    {'id': 467, 'name': 'Kettle', 'freebase_id': '/m/03s_tn'},
    {'id': 468, 'name': 'Trousers', 'freebase_id': '/m/07mhn'},
    {'id': 469, 'name': 'Popcorn', 'freebase_id': '/m/01hrv5'},
    {'id': 470, 'name': 'Centipede', 'freebase_id': '/m/019h78'},
    {'id': 471, 'name': 'Spider', 'freebase_id': '/m/09kmb'},
    {'id': 472, 'name': 'Sparrow', 'freebase_id': '/m/0h23m'},
    {'id': 473, 'name': 'Plate', 'freebase_id': '/m/050gv4'},
    {'id': 474, 'name': 'Bagel', 'freebase_id': '/m/01fb_0'},
    {'id': 475, 'name': 'Personal care', 'freebase_id': '/m/02w3_ws'},
    {'id': 476, 'name': 'Apple', 'freebase_id': '/m/014j1m'},
    {'id': 477, 'name': 'Brassiere', 'freebase_id': '/m/01gmv2'},
    {'id': 478, 'name': 'Bathroom cabinet', 'freebase_id': '/m/04y4h8h'},
    {'id': 479, 'name': 'studio couch', 'freebase_id': '/m/026qbn5'},
    {'id': 480, 'name': 'Computer keyboard', 'freebase_id': '/m/01m2v'},
    {'id': 481, 'name': 'Table tennis racket', 'freebase_id': '/m/05_5p_0'},
    {'id': 482, 'name': 'Sushi', 'freebase_id': '/m/07030'},
    {'id': 483, 'name': 'Cabinetry', 'freebase_id': '/m/01s105'},
    {'id': 484, 'name': 'Street light', 'freebase_id': '/m/033rq4'},
    {'id': 485, 'name': 'Towel', 'freebase_id': '/m/0162_1'},
    {'id': 486, 'name': 'Nightstand', 'freebase_id': '/m/02z51p'},
    {'id': 487, 'name': 'Rabbit', 'freebase_id': '/m/06mf6'},
    {'id': 488, 'name': 'Dolphin', 'freebase_id': '/m/02hj4'},
    {'id': 489, 'name': 'Dog', 'freebase_id': '/m/0bt9lr'},
    {'id': 490, 'name': 'Jug', 'freebase_id': '/m/08hvt4'},
    {'id': 491, 'name': 'Wok', 'freebase_id': '/m/084rd'},
    {'id': 492, 'name': 'Fire hydrant', 'freebase_id': '/m/01pns0'},
    {'id': 493, 'name': 'Human eye', 'freebase_id': '/m/014sv8'},
    {'id': 494, 'name': 'Skyscraper', 'freebase_id': '/m/079cl'},
    {'id': 495, 'name': 'Backpack', 'freebase_id': '/m/01940j'},
    {'id': 496, 'name': 'Potato', 'freebase_id': '/m/05vtc'},
    {'id': 497, 'name': 'Paper towel', 'freebase_id': '/m/02w3r3'},
    {'id': 498, 'name': 'Lifejacket', 'freebase_id': '/m/054xkw'},
    {'id': 499, 'name': 'Bicycle wheel', 'freebase_id': '/m/01bqk0'},
    {'id': 500, 'name': 'Toilet', 'freebase_id': '/m/09g1w'},
]

categories_l3 = [
    {'id': 1, 'name': 'Vegetable', 'freebase_id': '/m/0f4s2w'},
    {'id': 2, 'name': 'Reptile', 'freebase_id': '/m/06bt6'},
    {'id': 3, 'name': 'Carnivore', 'freebase_id': '/m/01lrl'},
    {'id': 4, 'name': 'Watercraft', 'freebase_id': '/m/01rzcn'},
    {'id': 5, 'name': 'Insect', 'freebase_id': '/m/03vt0'},
    {'id': 6, 'name': 'Furniture', 'freebase_id': '/m/0c_jw'},
    {'id': 7, 'name': 'Land vehicle', 'freebase_id': '/m/01prls'},
    {'id': 8, 'name': 'Seafood', 'freebase_id': '/m/06nwz'},
]

categories_l4 = [
    {'id': 1, 'name': 'Glove', 'freebase_id': '/m/0174n1'},
    {'id': 2, 'name': 'Tree', 'freebase_id': '/m/07j7r'},
    {'id': 3, 'name': 'Shellfish', 'freebase_id': '/m/0fbdv'},
    {'id': 4, 'name': 'Bed', 'freebase_id': '/m/03ssj5'},
    {'id': 5, 'name': 'Hat', 'freebase_id': '/m/02dl1y'},
    {'id': 6, 'name': 'Moths and butterflies', 'freebase_id': '/m/0d_2m'},
    {'id': 7, 'name': 'Building', 'freebase_id': '/m/0cgh4'},
    {'id': 8, 'name': 'Turtle', 'freebase_id': '/m/09dzg'},
    {'id': 9, 'name': 'Bear', 'freebase_id': '/m/01dws'},
    {'id': 10, 'name': 'Car', 'freebase_id': '/m/0k4j'},
    {'id': 11, 'name': 'Footwear', 'freebase_id': '/m/09j5n'},
    {'id': 12, 'name': 'Flower', 'freebase_id': '/m/0c9ph5'},
    {'id': 13, 'name': 'Marine mammal', 'freebase_id': '/m/0gd2v'},
    {'id': 14, 'name': 'Dessert', 'freebase_id': '/m/0270h'},
    {'id': 15, 'name': 'Table', 'freebase_id': '/m/04bcr3'},
    {'id': 16, 'name': 'Weapon', 'freebase_id': '/m/083kb'},
    {'id': 17, 'name': 'Boat', 'freebase_id': '/m/019jd'},
    {'id': 18, 'name': 'Helmet', 'freebase_id': '/m/0zvk5'},
    {'id': 19, 'name': 'Skirt', 'freebase_id': '/m/02wv6h6'},
    {'id': 20, 'name': 'Bird', 'freebase_id': '/m/015p6'},
    {'id': 21, 'name': 'Home appliance', 'freebase_id': '/m/019dx1'},
    {'id': 22, 'name': 'Beetle', 'freebase_id': '/m/020jm'},
    {'id': 23, 'name': 'Drink', 'freebase_id': '/m/0271t'},
    {'id': 24, 'name': 'Clock', 'freebase_id': '/m/01x3z'},
    {'id': 25, 'name': 'Musical instrument', 'freebase_id': '/m/04szw'},
    {'id': 26, 'name': 'Sandwich', 'freebase_id': '/m/0l515'},
    {'id': 27, 'name': 'Fish', 'freebase_id': '/m/0ch_cf'},
    {'id': 28, 'name': 'Fruit', 'freebase_id': '/m/02xwb'},
    {'id': 29, 'name': 'Luggage and bags', 'freebase_id': '/m/0hf58v5'},
    {'id': 30, 'name': 'Kitchen appliance', 'freebase_id': '/m/0h99cwc'},
    {'id': 31, 'name': 'Aircraft', 'freebase_id': '/m/0k5j'},
    {'id': 32, 'name': 'Person', 'freebase_id': '/m/01g317'},
    {'id': 33, 'name': 'Tableware', 'freebase_id': '/m/04brg2'},
    {'id': 34, 'name': 'Traffic sign', 'freebase_id': '/m/01mqdt'},
    {'id': 35, 'name': 'Racket', 'freebase_id': '/m/0dv9c'},
    {'id': 36, 'name': 'Toy', 'freebase_id': '/m/0138tl'},
    {'id': 37, 'name': 'Squash', 'freebase_id': '/m/0dv77'},
    {'id': 38, 'name': 'Plumbing fixture', 'freebase_id': '/m/02pkr5'},
    {'id': 39, 'name': 'Telephone', 'freebase_id': '/m/07cx4'},
    {'id': 40, 'name': 'Office supplies', 'freebase_id': '/m/02rdsp'},
    {'id': 41, 'name': 'Couch', 'freebase_id': '/m/02crq1'},
    {'id': 42, 'name': 'Ball', 'freebase_id': '/m/018xm'},
    {'id': 43, 'name': 'Sculpture', 'freebase_id': '/m/06msq'},
    {'id': 44, 'name': 'Marine invertebrates', 'freebase_id': '/m/03hl4l9'},
    {'id': 45, 'name': 'Trousers', 'freebase_id': '/m/07mhn'},
    {'id': 46, 'name': 'Personal care', 'freebase_id': '/m/02w3_ws'},
]

categories_l5 = [
    {'id': 1, 'name': 'Infant bed', 'freebase_id': '/m/061hd_'},
    {'id': 2, 'name': 'Rose', 'freebase_id': '/m/06m11'},
    {'id': 3, 'name': 'Flag', 'freebase_id': '/m/03120'},
    {'id': 4, 'name': 'Flashlight', 'freebase_id': '/m/01kb5b'},
    {'id': 5, 'name': 'Sea turtle', 'freebase_id': '/m/0120dh'},
    {'id': 6, 'name': 'Camera', 'freebase_id': '/m/0dv5r'},
    {'id': 7, 'name': 'Crocodile', 'freebase_id': '/m/09f_2'},
    {'id': 8, 'name': 'Cattle', 'freebase_id': '/m/01xq0k1'},
    {'id': 9, 'name': 'House', 'freebase_id': '/m/03jm5'},
    {'id': 10, 'name': 'Guacamole', 'freebase_id': '/m/02g30s'},
    {'id': 11, 'name': 'Penguin', 'freebase_id': '/m/05z6w'},
    {'id': 12, 'name': 'Vehicle registration plate', 'freebase_id': '/m/01jfm_'},
    {'id': 13, 'name': 'Bench', 'freebase_id': '/m/076lb9'},
    {'id': 14, 'name': 'Ladybug', 'freebase_id': '/m/0gj37'},
    {'id': 15, 'name': 'Human nose', 'freebase_id': '/m/0k0pj'},
    {'id': 16, 'name': 'Watermelon', 'freebase_id': '/m/0kpqd'},
    {'id': 17, 'name': 'Flute', 'freebase_id': '/m/0l14j_'},
    {'id': 18, 'name': 'Butterfly', 'freebase_id': '/m/0cyf8'},
    {'id': 19, 'name': 'Washing machine', 'freebase_id': '/m/0174k2'},
    {'id': 20, 'name': 'Raccoon', 'freebase_id': '/m/0dq75'},
    {'id': 21, 'name': 'Segway', 'freebase_id': '/m/076bq'},
    {'id': 22, 'name': 'Taco', 'freebase_id': '/m/07crc'},
    {'id': 23, 'name': 'Jellyfish', 'freebase_id': '/m/0d8zb'},
    {'id': 24, 'name': 'Cake', 'freebase_id': '/m/0fszt'},
    {'id': 25, 'name': 'Pen', 'freebase_id': '/m/0k1tl'},
    {'id': 26, 'name': 'Cannon', 'freebase_id': '/m/020kz'},
    {'id': 27, 'name': 'Bread', 'freebase_id': '/m/09728'},
    {'id': 28, 'name': 'Hamster', 'freebase_id': '/m/03qrc'},
    {'id': 29, 'name': 'Toaster', 'freebase_id': '/m/01k6s3'},
    {'id': 30, 'name': 'Sombrero', 'freebase_id': '/m/02jfl0'},
    {'id': 31, 'name': 'Tiara', 'freebase_id': '/m/01krhy'},
    {'id': 32, 'name': 'Bowl', 'freebase_id': '/m/04kkgm'},
    {'id': 33, 'name': 'Dragonfly', 'freebase_id': '/m/0ft9s'},
    {'id': 34, 'name': 'Antelope', 'freebase_id': '/m/0czz2'},
    {'id': 35, 'name': 'Torch', 'freebase_id': '/m/07dd4'},
    {'id': 36, 'name': 'Power plugs and sockets', 'freebase_id': '/m/03bbps'},
    {'id': 37, 'name': 'Blender', 'freebase_id': '/m/02pjr4'},
    {'id': 38, 'name': 'Billiard table', 'freebase_id': '/m/04p0qw'},
    {'id': 39, 'name': 'Cutting board', 'freebase_id': '/m/02pdsw'},
    {'id': 40, 'name': 'Bronze sculpture', 'freebase_id': '/m/01yx86'},
    {'id': 41, 'name': 'Broccoli', 'freebase_id': '/m/0hkxq'},
    {'id': 42, 'name': 'Tiger', 'freebase_id': '/m/07dm6'},
    {'id': 43, 'name': 'Mirror', 'freebase_id': '/m/054_l'},
    {'id': 44, 'name': 'Zucchini', 'freebase_id': '/m/027pcv'},
    {'id': 45, 'name': 'Dress', 'freebase_id': '/m/01d40f'},
    {'id': 46, 'name': 'Volleyball', 'freebase_id': '/m/02rgn06'},
    {'id': 47, 'name': 'Guitar', 'freebase_id': '/m/0342h'},
    {'id': 48, 'name': 'Golf cart', 'freebase_id': '/m/0323sq'},
    {'id': 49, 'name': 'Tart', 'freebase_id': '/m/02zvsm'},
    {'id': 50, 'name': 'Fedora', 'freebase_id': '/m/02fq_6'},
    {'id': 51, 'name': 'Lighthouse', 'freebase_id': '/m/04h7h'},
    {'id': 52, 'name': 'Coffeemaker', 'freebase_id': '/m/07xyvk'},
    {'id': 53, 'name': 'Food processor', 'freebase_id': '/m/03y6mg'},
    {'id': 54, 'name': 'Truck', 'freebase_id': '/m/07r04'},
    {'id': 55, 'name': 'Bookcase', 'freebase_id': '/m/03__z0'},
    {'id': 56, 'name': 'Surfboard', 'freebase_id': '/m/019w40'},
    {'id': 57, 'name': 'Bench', 'freebase_id': '/m/0cvnqh'},
    {'id': 58, 'name': 'Necklace', 'freebase_id': '/m/01llwg'},
    {'id': 59, 'name': 'Radish', 'freebase_id': '/m/015x5n'},
    {'id': 60, 'name': 'Frying pan', 'freebase_id': '/m/04v6l4'},
    {'id': 61, 'name': 'Tap', 'freebase_id': '/m/02jz0l'},
    {'id': 62, 'name': 'Peach', 'freebase_id': '/m/0dj6p'},
    {'id': 63, 'name': 'Knife', 'freebase_id': '/m/04ctx'},
    {'id': 64, 'name': 'Handbag', 'freebase_id': '/m/080hkjn'},
    {'id': 65, 'name': 'Laptop', 'freebase_id': '/m/01c648'},
    {'id': 66, 'name': 'Tent', 'freebase_id': '/m/01j61q'},
    {'id': 67, 'name': 'Ambulance', 'freebase_id': '/m/012n7d'},
    {'id': 68, 'name': 'Christmas tree', 'freebase_id': '/m/025nd'},
    {'id': 69, 'name': 'Eagle', 'freebase_id': '/m/09csl'},
    {'id': 70, 'name': 'Limousine', 'freebase_id': '/m/01lcw4'},
    {'id': 71, 'name': 'Kitchen & dining room table', 'freebase_id': '/m/0h8n5zk'},
    {'id': 72, 'name': 'Polar bear', 'freebase_id': '/m/0633h'},
    {'id': 73, 'name': 'Tower', 'freebase_id': '/m/01fdzj'},
    {'id': 74, 'name': 'Football', 'freebase_id': '/m/01226z'},
    {'id': 75, 'name': 'Willow', 'freebase_id': '/m/0mw_6'},
    {'id': 76, 'name': 'Human head', 'freebase_id': '/m/04hgtk'},
    {'id': 77, 'name': 'Stop sign', 'freebase_id': '/m/02pv19'},
    {'id': 78, 'name': 'Banana', 'freebase_id': '/m/09qck'},
    {'id': 79, 'name': 'Mixer', 'freebase_id': '/m/063rgb'},
    {'id': 80, 'name': 'Binoculars', 'freebase_id': '/m/0lt4_'},
    {'id': 81, 'name': 'Bee', 'freebase_id': '/m/01h3n'},
    {'id': 82, 'name': 'Chair', 'freebase_id': '/m/01mzpv'},
    {'id': 83, 'name': 'Wood-burning stove', 'freebase_id': '/m/04169hn'},
    {'id': 84, 'name': 'Flowerpot', 'freebase_id': '/m/0fm3zh'},
    {'id': 85, 'name': 'Beaker', 'freebase_id': '/m/0d20w4'},
    {'id': 86, 'name': 'Oyster', 'freebase_id': '/m/0_cp5'},
    {'id': 87, 'name': 'Woodpecker', 'freebase_id': '/m/01dy8n'},
    {'id': 88, 'name': 'Harp', 'freebase_id': '/m/03m5k'},
    {'id': 89, 'name': 'Bathtub', 'freebase_id': '/m/03dnzn'},
    {'id': 90, 'name': 'Wall clock', 'freebase_id': '/m/0h8mzrc'},
    {'id': 91, 'name': 'Sports uniform', 'freebase_id': '/m/0h8mhzd'},
    {'id': 92, 'name': 'Rhinoceros', 'freebase_id': '/m/03d443'},
    {'id': 93, 'name': 'Beehive', 'freebase_id': '/m/01gllr'},
    {'id': 94, 'name': 'Cupboard', 'freebase_id': '/m/0642b4'},
    {'id': 95, 'name': 'Chicken', 'freebase_id': '/m/09b5t'},
    {'id': 96, 'name': 'Man', 'freebase_id': '/m/04yx4'},
    {'id': 97, 'name': 'Blue jay', 'freebase_id': '/m/01f8m5'},
    {'id': 98, 'name': 'Cucumber', 'freebase_id': '/m/015x4r'},
    {'id': 99, 'name': 'Balloon', 'freebase_id': '/m/01j51'},
    {'id': 100, 'name': 'Kite', 'freebase_id': '/m/02zt3'},
    {'id': 101, 'name': 'Fireplace', 'freebase_id': '/m/03tw93'},
    {'id': 102, 'name': 'Lantern', 'freebase_id': '/m/01jfsr'},
    {'id': 103, 'name': 'Missile', 'freebase_id': '/m/04ylt'},
    {'id': 104, 'name': 'Book', 'freebase_id': '/m/0bt_c3'},
    {'id': 105, 'name': 'Spoon', 'freebase_id': '/m/0cmx8'},
    {'id': 106, 'name': 'Grapefruit', 'freebase_id': '/m/0hqkz'},
    {'id': 107, 'name': 'Squirrel', 'freebase_id': '/m/071qp'},
    {'id': 108, 'name': 'Orange', 'freebase_id': '/m/0cyhj_'},
    {'id': 109, 'name': 'Coat', 'freebase_id': '/m/01xygc'},
    {'id': 110, 'name': 'Punching bag', 'freebase_id': '/m/0420v5'},
    {'id': 111, 'name': 'Zebra', 'freebase_id': '/m/0898b'},
    {'id': 112, 'name': 'Billboard', 'freebase_id': '/m/01knjb'},
    {'id': 113, 'name': 'Bicycle', 'freebase_id': '/m/0199g'},
    {'id': 114, 'name': 'Door handle', 'freebase_id': '/m/03c7gz'},
    {'id': 115, 'name': 'Mechanical fan', 'freebase_id': '/m/02x984l'},
    {'id': 116, 'name': 'Ring binder', 'freebase_id': '/m/04zwwv'},
    {'id': 117, 'name': 'Parrot', 'freebase_id': '/m/0gv1x'},
    {'id': 118, 'name': 'Sock', 'freebase_id': '/m/01nq26'},
    {'id': 119, 'name': 'Vase', 'freebase_id': '/m/02s195'},
    {'id': 120, 'name': 'Shotgun', 'freebase_id': '/m/06nrc'},
    {'id': 121, 'name': 'Glasses', 'freebase_id': '/m/0jyfg'},
    {'id': 122, 'name': 'Seahorse', 'freebase_id': '/m/0nybt'},
    {'id': 123, 'name': 'Belt', 'freebase_id': '/m/0176mf'},
    {'id': 124, 'name': 'Window', 'freebase_id': '/m/0d4v4'},
    {'id': 125, 'name': 'Giraffe', 'freebase_id': '/m/03bk1'},
    {'id': 126, 'name': 'Lion', 'freebase_id': '/m/096mb'},
    {'id': 127, 'name': 'Tire', 'freebase_id': '/m/0h9mv'},
    {'id': 128, 'name': 'Canoe', 'freebase_id': '/m/0ph39'},
    {'id': 129, 'name': 'Tie', 'freebase_id': '/m/01rkbr'},
    {'id': 130, 'name': 'Shelf', 'freebase_id': '/m/0gjbg72'},
    {'id': 131, 'name': 'Picture frame', 'freebase_id': '/m/06z37_'},
    {'id': 132, 'name': 'Printer', 'freebase_id': '/m/01m4t'},
    {'id': 133, 'name': 'Human leg', 'freebase_id': '/m/035r7c'},
    {'id': 134, 'name': 'Slow cooker', 'freebase_id': '/m/02tsc9'},
    {'id': 135, 'name': 'Croissant', 'freebase_id': '/m/015wgc'},
    {'id': 136, 'name': 'Candle', 'freebase_id': '/m/0c06p'},
    {'id': 137, 'name': 'Pancake', 'freebase_id': '/m/01dwwc'},
    {'id': 138, 'name': 'Pillow', 'freebase_id': '/m/034c16'},
    {'id': 139, 'name': 'Coin', 'freebase_id': '/m/0242l'},
    {'id': 140, 'name': 'Stretcher', 'freebase_id': '/m/02lbcq'},
    {'id': 141, 'name': 'Sandal', 'freebase_id': '/m/03nfch'},
    {'id': 142, 'name': 'Woman', 'freebase_id': '/m/03bt1vf'},
    {'id': 143, 'name': 'Stairs', 'freebase_id': '/m/01lynh'},
    {'id': 144, 'name': 'Harpsichord', 'freebase_id': '/m/03q5t'},
    {'id': 145, 'name': 'Stool', 'freebase_id': '/m/0fqt361'},
    {'id': 146, 'name': 'Bus', 'freebase_id': '/m/01bjv'},
    {'id': 147, 'name': 'Suitcase', 'freebase_id': '/m/01s55n'},
    {'id': 148, 'name': 'Human mouth', 'freebase_id': '/m/0283dt1'},
    {'id': 149, 'name': 'Juice', 'freebase_id': '/m/01z1kdw'},
    {'id': 150, 'name': 'Skull', 'freebase_id': '/m/016m2d'},
    {'id': 151, 'name': 'Door', 'freebase_id': '/m/02dgv'},
    {'id': 152, 'name': 'Violin', 'freebase_id': '/m/07y_7'},
    {'id': 153, 'name': 'Chopsticks', 'freebase_id': '/m/01_5g'},
    {'id': 154, 'name': 'Digital clock', 'freebase_id': '/m/06_72j'},
    {'id': 155, 'name': 'Sunflower', 'freebase_id': '/m/0ftb8'},
    {'id': 156, 'name': 'Leopard', 'freebase_id': '/m/0c29q'},
    {'id': 157, 'name': 'Bell pepper', 'freebase_id': '/m/0jg57'},
    {'id': 158, 'name': 'Harbor seal', 'freebase_id': '/m/02l8p9'},
    {'id': 159, 'name': 'Snake', 'freebase_id': '/m/078jl'},
    {'id': 160, 'name': 'Sewing machine', 'freebase_id': '/m/0llzx'},
    {'id': 161, 'name': 'Goose', 'freebase_id': '/m/0dbvp'},
    {'id': 162, 'name': 'Helicopter', 'freebase_id': '/m/09ct_'},
    {'id': 163, 'name': 'Seat belt', 'freebase_id': '/m/0dkzw'},
    {'id': 164, 'name': 'Coffee cup', 'freebase_id': '/m/02p5f1q'},
    {'id': 165, 'name': 'Microwave oven', 'freebase_id': '/m/0fx9l'},
    {'id': 166, 'name': 'Hot dog', 'freebase_id': '/m/01b9xk'},
    {'id': 167, 'name': 'Countertop', 'freebase_id': '/m/0b3fp9'},
    {'id': 168, 'name': 'Serving tray', 'freebase_id': '/m/0h8n27j'},
    {'id': 169, 'name': 'Dog bed', 'freebase_id': '/m/0h8n6f9'},
    {'id': 170, 'name': 'Beer', 'freebase_id': '/m/01599'},
    {'id': 171, 'name': 'Sunglasses', 'freebase_id': '/m/017ftj'},
    {'id': 172, 'name': 'Golf ball', 'freebase_id': '/m/044r5d'},
    {'id': 173, 'name': 'Waffle', 'freebase_id': '/m/01dwsz'},
    {'id': 174, 'name': 'Palm tree', 'freebase_id': '/m/0cdl1'},
    {'id': 175, 'name': 'Trumpet', 'freebase_id': '/m/07gql'},
    {'id': 176, 'name': 'Ruler', 'freebase_id': '/m/0hdln'},
    {'id': 177, 'name': 'Ladder', 'freebase_id': '/m/012w5l'},
    {'id': 178, 'name': 'Office building', 'freebase_id': '/m/021sj1'},
    {'id': 179, 'name': 'Tablet computer', 'freebase_id': '/m/0bh9flk'},
    {'id': 180, 'name': 'Toilet paper', 'freebase_id': '/m/09gtd'},
    {'id': 181, 'name': 'Pomegranate', 'freebase_id': '/m/0jwn_'},
    {'id': 182, 'name': 'Gas stove', 'freebase_id': '/m/02wv84t'},
    {'id': 183, 'name': 'Cookie', 'freebase_id': '/m/021mn'},
    {'id': 184, 'name': 'Cart', 'freebase_id': '/m/018p4k'},
    {'id': 185, 'name': 'Raven', 'freebase_id': '/m/06j2d'},
    {'id': 186, 'name': 'Egg', 'freebase_id': '/m/033cnk'},
    {'id': 187, 'name': 'Burrito', 'freebase_id': '/m/01j3zr'},
    {'id': 188, 'name': 'Goat', 'freebase_id': '/m/03fwl'},
    {'id': 189, 'name': 'Kitchen knife', 'freebase_id': '/m/058qzx'},
    {'id': 190, 'name': 'Skateboard', 'freebase_id': '/m/06_fw'},
    {'id': 191, 'name': 'Salt and pepper shakers', 'freebase_id': '/m/02x8cch'},
    {'id': 192, 'name': 'Lynx', 'freebase_id': '/m/04g2r'},
    {'id': 193, 'name': 'Boot', 'freebase_id': '/m/01b638'},
    {'id': 194, 'name': 'Platter', 'freebase_id': '/m/099ssp'},
    {'id': 195, 'name': 'Ski', 'freebase_id': '/m/071p9'},
    {'id': 196, 'name': 'Swimwear', 'freebase_id': '/m/01gkx_'},
    {'id': 197, 'name': 'Swimming pool', 'freebase_id': '/m/0b_rs'},
    {'id': 198, 'name': 'Drinking straw', 'freebase_id': '/m/03v5tg'},
    {'id': 199, 'name': 'Wrench', 'freebase_id': '/m/01j5ks'},
    {'id': 200, 'name': 'Drum', 'freebase_id': '/m/026t6'},
    {'id': 201, 'name': 'Ant', 'freebase_id': '/m/0_k2'},
    {'id': 202, 'name': 'Human ear', 'freebase_id': '/m/039xj_'},
    {'id': 203, 'name': 'Headphones', 'freebase_id': '/m/01b7fy'},
    {'id': 204, 'name': 'Fountain', 'freebase_id': '/m/0220r2'},
    {'id': 205, 'name': 'Jeans', 'freebase_id': '/m/0fly7'},
    {'id': 206, 'name': 'Television', 'freebase_id': '/m/07c52'},
    {'id': 207, 'name': 'Crab', 'freebase_id': '/m/0n28_'},
    {'id': 208, 'name': 'Microphone', 'freebase_id': '/m/0hg7b'},
    {'id': 209, 'name': 'Snowplow', 'freebase_id': '/m/04vv5k'},
    {'id': 210, 'name': 'Artichoke', 'freebase_id': '/m/047v4b'},
    {'id': 211, 'name': 'Jet ski', 'freebase_id': '/m/01xs3r'},
    {'id': 212, 'name': 'Stationary bicycle', 'freebase_id': '/m/03kt2w'},
    {'id': 213, 'name': 'Human hair', 'freebase_id': '/m/03q69'},
    {'id': 214, 'name': 'Brown bear', 'freebase_id': '/m/01dxs'},
    {'id': 215, 'name': 'Starfish', 'freebase_id': '/m/01h8tj'},
    {'id': 216, 'name': 'Fork', 'freebase_id': '/m/0dt3t'},
    {'id': 217, 'name': 'Lobster', 'freebase_id': '/m/0cjq5'},
    {'id': 218, 'name': 'Corded phone', 'freebase_id': '/m/0h8lkj8'},
    {'id': 219, 'name': 'Saucer', 'freebase_id': '/m/03q5c7'},
    {'id': 220, 'name': 'Carrot', 'freebase_id': '/m/0fj52s'},
    {'id': 221, 'name': 'Castle', 'freebase_id': '/m/0d5gx'},
    {'id': 222, 'name': 'Tennis racket', 'freebase_id': '/m/0h8my_4'},
    {'id': 223, 'name': 'Ceiling fan', 'freebase_id': '/m/03ldnb'},
    {'id': 224, 'name': 'Asparagus', 'freebase_id': '/m/0cjs7'},
    {'id': 225, 'name': 'Jaguar', 'freebase_id': '/m/0449p'},
    {'id': 226, 'name': 'Train', 'freebase_id': '/m/07jdr'},
    {'id': 227, 'name': 'Cat', 'freebase_id': '/m/01yrx'},
    {'id': 228, 'name': 'Rifle', 'freebase_id': '/m/06c54'},
    {'id': 229, 'name': 'Dumbbell', 'freebase_id': '/m/04h8sr'},
    {'id': 230, 'name': 'Mobile phone', 'freebase_id': '/m/050k8'},
    {'id': 231, 'name': 'Taxi', 'freebase_id': '/m/0pg52'},
    {'id': 232, 'name': 'Shower', 'freebase_id': '/m/02f9f_'},
    {'id': 233, 'name': 'Pitcher', 'freebase_id': '/m/054fyh'},
    {'id': 234, 'name': 'Lemon', 'freebase_id': '/m/09k_b'},
    {'id': 235, 'name': 'Turkey', 'freebase_id': '/m/0jly1'},
    {'id': 236, 'name': 'High heels', 'freebase_id': '/m/06k2mb'},
    {'id': 237, 'name': 'Bust', 'freebase_id': '/m/04yqq2'},
    {'id': 238, 'name': 'Elephant', 'freebase_id': '/m/0bwd_0j'},
    {'id': 239, 'name': 'Scarf', 'freebase_id': '/m/02h19r'},
    {'id': 240, 'name': 'Barrel', 'freebase_id': '/m/02zn6n'},
    {'id': 241, 'name': 'Trombone', 'freebase_id': '/m/07c6l'},
    {'id': 242, 'name': 'Pumpkin', 'freebase_id': '/m/05zsy'},
    {'id': 243, 'name': 'Box', 'freebase_id': '/m/025dyy'},
    {'id': 244, 'name': 'Tomato', 'freebase_id': '/m/07j87'},
    {'id': 245, 'name': 'Frog', 'freebase_id': '/m/09ld4'},
    {'id': 246, 'name': 'Bidet', 'freebase_id': '/m/01vbnl'},
    {'id': 247, 'name': 'Human face', 'freebase_id': '/m/0dzct'},
    {'id': 248, 'name': 'Houseplant', 'freebase_id': '/m/03fp41'},
    {'id': 249, 'name': 'Van', 'freebase_id': '/m/0h2r6'},
    {'id': 250, 'name': 'Shark', 'freebase_id': '/m/0by6g'},
    {'id': 251, 'name': 'Ice cream', 'freebase_id': '/m/0cxn2'},
    {'id': 252, 'name': 'Swim cap', 'freebase_id': '/m/04tn4x'},
    {'id': 253, 'name': 'Falcon', 'freebase_id': '/m/0f6wt'},
    {'id': 254, 'name': 'Ostrich', 'freebase_id': '/m/05n4y'},
    {'id': 255, 'name': 'Handgun', 'freebase_id': '/m/0gxl3'},
    {'id': 256, 'name': 'Whiteboard', 'freebase_id': '/m/02d9qx'},
    {'id': 257, 'name': 'Lizard', 'freebase_id': '/m/04m9y'},
    {'id': 258, 'name': 'Pasta', 'freebase_id': '/m/05z55'},
    {'id': 259, 'name': 'Snowmobile', 'freebase_id': '/m/01x3jk'},
    {'id': 260, 'name': 'Light bulb', 'freebase_id': '/m/0h8l4fh'},
    {'id': 261, 'name': 'Window blind', 'freebase_id': '/m/031b6r'},
    {'id': 262, 'name': 'Muffin', 'freebase_id': '/m/01tcjp'},
    {'id': 263, 'name': 'Pretzel', 'freebase_id': '/m/01f91_'},
    {'id': 264, 'name': 'Computer monitor', 'freebase_id': '/m/02522'},
    {'id': 265, 'name': 'Horn', 'freebase_id': '/m/0319l'},
    {'id': 266, 'name': 'Fox', 'freebase_id': '/m/0306r'},
    {'id': 267, 'name': 'Convenience store', 'freebase_id': '/m/0crjs'},
    {'id': 268, 'name': 'Earrings', 'freebase_id': '/m/01r546'},
    {'id': 269, 'name': 'Curtain', 'freebase_id': '/m/03rszm'},
    {'id': 270, 'name': 'Grape', 'freebase_id': '/m/0388q'},
    {'id': 271, 'name': 'Sofa bed', 'freebase_id': '/m/03m3pdh'},
    {'id': 272, 'name': 'Horse', 'freebase_id': '/m/03k3r'},
    {'id': 273, 'name': 'Desk', 'freebase_id': '/m/01y9k5'},
    {'id': 274, 'name': 'Crutch', 'freebase_id': '/m/05441v'},
    {'id': 275, 'name': 'Bicycle helmet', 'freebase_id': '/m/03p3bw'},
    {'id': 276, 'name': 'Tick', 'freebase_id': '/m/0175cv'},
    {'id': 277, 'name': 'Airplane', 'freebase_id': '/m/0cmf2'},
    {'id': 278, 'name': 'Canary', 'freebase_id': '/m/0ccs93'},
    {'id': 279, 'name': 'Spatula', 'freebase_id': '/m/02d1br'},
    {'id': 280, 'name': 'Watch', 'freebase_id': '/m/0gjkl'},
    {'id': 281, 'name': 'Lily', 'freebase_id': '/m/0jqgx'},
    {'id': 282, 'name': 'Filing cabinet', 'freebase_id': '/m/047j0r'},
    {'id': 283, 'name': 'Cake stand', 'freebase_id': '/m/0h8n6ft'},
    {'id': 284, 'name': 'Candy', 'freebase_id': '/m/0gm28'},
    {'id': 285, 'name': 'Sink', 'freebase_id': '/m/0130jx'},
    {'id': 286, 'name': 'Mouse', 'freebase_id': '/m/04rmv'},
    {'id': 287, 'name': 'Wine', 'freebase_id': '/m/081qc'},
    {'id': 288, 'name': 'Wheelchair', 'freebase_id': '/m/0qmmr'},
    {'id': 289, 'name': 'Goldfish', 'freebase_id': '/m/03fj2'},
    {'id': 290, 'name': 'Refrigerator', 'freebase_id': '/m/040b_t'},
    {'id': 291, 'name': 'French fries', 'freebase_id': '/m/02y6n'},
    {'id': 292, 'name': 'Drawer', 'freebase_id': '/m/0fqfqc'},
    {'id': 293, 'name': 'Treadmill', 'freebase_id': '/m/030610'},
    {'id': 294, 'name': 'Picnic basket', 'freebase_id': '/m/07kng9'},
    {'id': 295, 'name': 'Dice', 'freebase_id': '/m/029b3'},
    {'id': 296, 'name': 'Cabbage', 'freebase_id': '/m/0fbw6'},
    {'id': 297, 'name': 'Football helmet', 'freebase_id': '/m/07qxg_'},
    {'id': 298, 'name': 'Pig', 'freebase_id': '/m/068zj'},
    {'id': 299, 'name': 'Shorts', 'freebase_id': '/m/01bfm9'},
    {'id': 300, 'name': 'Gondola', 'freebase_id': '/m/02068x'},
    {'id': 301, 'name': 'Honeycomb', 'freebase_id': '/m/0fz0h'},
    {'id': 302, 'name': 'Doughnut', 'freebase_id': '/m/0jy4k'},
    {'id': 303, 'name': 'Chest of drawers', 'freebase_id': '/m/05kyg_'},
    {'id': 304, 'name': 'Bat', 'freebase_id': '/m/01h44'},
    {'id': 305, 'name': 'Monkey', 'freebase_id': '/m/08pbxl'},
    {'id': 306, 'name': 'Dagger', 'freebase_id': '/m/02gzp'},
    {'id': 307, 'name': 'Human foot', 'freebase_id': '/m/031n1'},
    {'id': 308, 'name': 'Mug', 'freebase_id': '/m/02jvh9'},
    {'id': 309, 'name': 'Alarm clock', 'freebase_id': '/m/046dlr'},
    {'id': 310, 'name': 'Pressure cooker', 'freebase_id': '/m/0h8ntjv'},
    {'id': 311, 'name': 'Human hand', 'freebase_id': '/m/0k65p'},
    {'id': 312, 'name': 'Tortoise', 'freebase_id': '/m/011k07'},
    {'id': 313, 'name': 'Baseball glove', 'freebase_id': '/m/03grzl'},
    {'id': 314, 'name': 'Sword', 'freebase_id': '/m/06y5r'},
    {'id': 315, 'name': 'Pear', 'freebase_id': '/m/061_f'},
    {'id': 316, 'name': 'Miniskirt', 'freebase_id': '/m/01cmb2'},
    {'id': 317, 'name': 'Girl', 'freebase_id': '/m/05r655'},
    {'id': 318, 'name': 'Roller skates', 'freebase_id': '/m/02p3w7d'},
    {'id': 319, 'name': 'Dinosaur', 'freebase_id': '/m/029tx'},
    {'id': 320, 'name': 'Porch', 'freebase_id': '/m/04m6gz'},
    {'id': 321, 'name': 'Human beard', 'freebase_id': '/m/015h_t'},
    {'id': 322, 'name': 'Submarine sandwich', 'freebase_id': '/m/06pcq'},
    {'id': 323, 'name': 'Screwdriver', 'freebase_id': '/m/01bms0'},
    {'id': 324, 'name': 'Strawberry', 'freebase_id': '/m/07fbm7'},
    {'id': 325, 'name': 'Wine glass', 'freebase_id': '/m/09tvcd'},
    {'id': 326, 'name': 'Wheel', 'freebase_id': '/m/083wq'},
    {'id': 327, 'name': 'Sea lion', 'freebase_id': '/m/0gd36'},
    {'id': 328, 'name': 'Tea', 'freebase_id': '/m/07clx'},
    {'id': 329, 'name': 'Tennis ball', 'freebase_id': '/m/05ctyq'},
    {'id': 330, 'name': 'Waste container', 'freebase_id': '/m/0bjyj5'},
    {'id': 331, 'name': 'Mule', 'freebase_id': '/m/0dbzx'},
    {'id': 332, 'name': 'Cricket ball', 'freebase_id': '/m/02ctlc'},
    {'id': 333, 'name': 'Pineapple', 'freebase_id': '/m/0fp6w'},
    {'id': 334, 'name': 'Coconut', 'freebase_id': '/m/0djtd'},
    {'id': 335, 'name': 'Doll', 'freebase_id': '/m/0167gd'},
    {'id': 336, 'name': 'Coffee table', 'freebase_id': '/m/078n6m'},
    {'id': 337, 'name': 'Snowman', 'freebase_id': '/m/0152hh'},
    {'id': 338, 'name': 'Lavender', 'freebase_id': '/m/04gth'},
    {'id': 339, 'name': 'Shrimp', 'freebase_id': '/m/0ll1f78'},
    {'id': 340, 'name': 'Maple', 'freebase_id': '/m/0cffdh'},
    {'id': 341, 'name': 'Cowboy hat', 'freebase_id': '/m/025rp__'},
    {'id': 342, 'name': 'Goggles', 'freebase_id': '/m/02_n6y'},
    {'id': 343, 'name': 'Rugby ball', 'freebase_id': '/m/0wdt60w'},
    {'id': 344, 'name': 'Caterpillar', 'freebase_id': '/m/0cydv'},
    {'id': 345, 'name': 'Poster', 'freebase_id': '/m/01n5jq'},
    {'id': 346, 'name': 'Rocket', 'freebase_id': '/m/09rvcxw'},
    {'id': 347, 'name': 'Organ', 'freebase_id': '/m/013y1f'},
    {'id': 348, 'name': 'Saxophone', 'freebase_id': '/m/06ncr'},
    {'id': 349, 'name': 'Traffic light', 'freebase_id': '/m/015qff'},
    {'id': 350, 'name': 'Cocktail', 'freebase_id': '/m/024g6'},
    {'id': 351, 'name': 'Plastic bag', 'freebase_id': '/m/05gqfk'},
    {'id': 352, 'name': 'Mushroom', 'freebase_id': '/m/052sf'},
    {'id': 353, 'name': 'Hamburger', 'freebase_id': '/m/0cdn1'},
    {'id': 354, 'name': 'Light switch', 'freebase_id': '/m/03jbxj'},
    {'id': 355, 'name': 'Parachute', 'freebase_id': '/m/0cyfs'},
    {'id': 356, 'name': 'Teddy bear', 'freebase_id': '/m/0kmg4'},
    {'id': 357, 'name': 'Winter melon', 'freebase_id': '/m/02cvgx'},
    {'id': 358, 'name': 'Deer', 'freebase_id': '/m/09kx5'},
    {'id': 359, 'name': 'Musical keyboard', 'freebase_id': '/m/057cc'},
    {'id': 360, 'name': 'Scoreboard', 'freebase_id': '/m/057p5t'},
    {'id': 361, 'name': 'Baseball bat', 'freebase_id': '/m/03g8mr'},
    {'id': 362, 'name': 'Envelope', 'freebase_id': '/m/0frqm'},
    {'id': 363, 'name': 'Adhesive tape', 'freebase_id': '/m/03m3vtv'},
    {'id': 364, 'name': 'Briefcase', 'freebase_id': '/m/0584n8'},
    {'id': 365, 'name': 'Paddle', 'freebase_id': '/m/014y4n'},
    {'id': 366, 'name': 'Bow and arrow', 'freebase_id': '/m/01g3x7'},
    {'id': 367, 'name': 'Sheep', 'freebase_id': '/m/07bgp'},
    {'id': 368, 'name': 'Jacket', 'freebase_id': '/m/032b3c'},
    {'id': 369, 'name': 'Boy', 'freebase_id': '/m/01bl7v'},
    {'id': 370, 'name': 'Pizza', 'freebase_id': '/m/0663v'},
    {'id': 371, 'name': 'Otter', 'freebase_id': '/m/0cn6p'},
    {'id': 372, 'name': 'Cello', 'freebase_id': '/m/01xqw'},
    {'id': 373, 'name': 'Bull', 'freebase_id': '/m/0cnyhnx'},
    {'id': 374, 'name': 'Camel', 'freebase_id': '/m/01x_v'},
    {'id': 375, 'name': 'Duck', 'freebase_id': '/m/09ddx'},
    {'id': 376, 'name': 'Whale', 'freebase_id': '/m/084zz'},
    {'id': 377, 'name': 'Shirt', 'freebase_id': '/m/01n4qj'},
    {'id': 378, 'name': 'Tank', 'freebase_id': '/m/07cmd'},
    {'id': 379, 'name': 'Motorcycle', 'freebase_id': '/m/04_sv'},
    {'id': 380, 'name': 'Accordion', 'freebase_id': '/m/0mkg'},
    {'id': 381, 'name': 'Owl', 'freebase_id': '/m/09d5_'},
    {'id': 382, 'name': 'Porcupine', 'freebase_id': '/m/0c568'},
    {'id': 383, 'name': 'Sun hat', 'freebase_id': '/m/02wbtzl'},
    {'id': 384, 'name': 'Nail', 'freebase_id': '/m/05bm6'},
    {'id': 385, 'name': 'Scissors', 'freebase_id': '/m/01lsmm'},
    {'id': 386, 'name': 'Swan', 'freebase_id': '/m/0dftk'},
    {'id': 387, 'name': 'Lamp', 'freebase_id': '/m/0dtln'},
    {'id': 388, 'name': 'Crown', 'freebase_id': '/m/0nl46'},
    {'id': 389, 'name': 'Piano', 'freebase_id': '/m/05r5c'},
    {'id': 390, 'name': 'Cheetah', 'freebase_id': '/m/0cd4d'},
    {'id': 391, 'name': 'Oboe', 'freebase_id': '/m/05kms'},
    {'id': 392, 'name': 'Tin can', 'freebase_id': '/m/02jnhm'},
    {'id': 393, 'name': 'Mango', 'freebase_id': '/m/0fldg'},
    {'id': 394, 'name': 'Tripod', 'freebase_id': '/m/073bxn'},
    {'id': 395, 'name': 'Oven', 'freebase_id': '/m/029bxz'},
    {'id': 396, 'name': 'Mouse', 'freebase_id': '/m/020lf'},
    {'id': 397, 'name': 'Barge', 'freebase_id': '/m/01btn'},
    {'id': 398, 'name': 'Coffee', 'freebase_id': '/m/02vqfm'},
    {'id': 399, 'name': 'Snowboard', 'freebase_id': '/m/06__v'},
    {'id': 400, 'name': 'Common fig', 'freebase_id': '/m/043nyj'},
    {'id': 401, 'name': 'Salad', 'freebase_id': '/m/0grw1'},
    {'id': 402, 'name': 'Umbrella', 'freebase_id': '/m/0hnnb'},
    {'id': 403, 'name': 'Kangaroo', 'freebase_id': '/m/04c0y'},
    {'id': 404, 'name': 'Human arm', 'freebase_id': '/m/0dzf4'},
    {'id': 405, 'name': 'Measuring cup', 'freebase_id': '/m/07v9_z'},
    {'id': 406, 'name': 'Snail', 'freebase_id': '/m/0f9_l'},
    {'id': 407, 'name': 'Loveseat', 'freebase_id': '/m/0703r8'},
    {'id': 408, 'name': 'Suit', 'freebase_id': '/m/01xyhv'},
    {'id': 409, 'name': 'Teapot', 'freebase_id': '/m/01fh4r'},
    {'id': 410, 'name': 'Bottle', 'freebase_id': '/m/04dr76w'},
    {'id': 411, 'name': 'Alpaca', 'freebase_id': '/m/0pcr'},
    {'id': 412, 'name': 'Kettle', 'freebase_id': '/m/03s_tn'},
    {'id': 413, 'name': 'Popcorn', 'freebase_id': '/m/01hrv5'},
    {'id': 414, 'name': 'Centipede', 'freebase_id': '/m/019h78'},
    {'id': 415, 'name': 'Spider', 'freebase_id': '/m/09kmb'},
    {'id': 416, 'name': 'Sparrow', 'freebase_id': '/m/0h23m'},
    {'id': 417, 'name': 'Plate', 'freebase_id': '/m/050gv4'},
    {'id': 418, 'name': 'Bagel', 'freebase_id': '/m/01fb_0'},
    {'id': 419, 'name': 'Apple', 'freebase_id': '/m/014j1m'},
    {'id': 420, 'name': 'Brassiere', 'freebase_id': '/m/01gmv2'},
    {'id': 421, 'name': 'Bathroom cabinet', 'freebase_id': '/m/04y4h8h'},
    {'id': 422, 'name': 'studio couch', 'freebase_id': '/m/026qbn5'},
    {'id': 423, 'name': 'Computer keyboard', 'freebase_id': '/m/01m2v'},
    {'id': 424, 'name': 'Table tennis racket', 'freebase_id': '/m/05_5p_0'},
    {'id': 425, 'name': 'Sushi', 'freebase_id': '/m/07030'},
    {'id': 426, 'name': 'Cabinetry', 'freebase_id': '/m/01s105'},
    {'id': 427, 'name': 'Street light', 'freebase_id': '/m/033rq4'},
    {'id': 428, 'name': 'Towel', 'freebase_id': '/m/0162_1'},
    {'id': 429, 'name': 'Nightstand', 'freebase_id': '/m/02z51p'},
    {'id': 430, 'name': 'Rabbit', 'freebase_id': '/m/06mf6'},
    {'id': 431, 'name': 'Dolphin', 'freebase_id': '/m/02hj4'},
    {'id': 432, 'name': 'Dog', 'freebase_id': '/m/0bt9lr'},
    {'id': 433, 'name': 'Jug', 'freebase_id': '/m/08hvt4'},
    {'id': 434, 'name': 'Wok', 'freebase_id': '/m/084rd'},
    {'id': 435, 'name': 'Fire hydrant', 'freebase_id': '/m/01pns0'},
    {'id': 436, 'name': 'Human eye', 'freebase_id': '/m/014sv8'},
    {'id': 437, 'name': 'Skyscraper', 'freebase_id': '/m/079cl'},
    {'id': 438, 'name': 'Backpack', 'freebase_id': '/m/01940j'},
    {'id': 439, 'name': 'Potato', 'freebase_id': '/m/05vtc'},
    {'id': 440, 'name': 'Paper towel', 'freebase_id': '/m/02w3r3'},
    {'id': 441, 'name': 'Lifejacket', 'freebase_id': '/m/054xkw'},
    {'id': 442, 'name': 'Bicycle wheel', 'freebase_id': '/m/01bqk0'},
    {'id': 443, 'name': 'Toilet', 'freebase_id': '/m/09g1w'},
]


def _get_builtin_metadata(cats):
    id_to_name = {x['id']: x['name'] for x in cats}
    thing_dataset_id_to_contiguous_id = {i + 1: i for i in range(len(cats))}    # convert to 1-indexed
    thing_classes = [x['name'] for x in sorted(cats, key=lambda x: x['id'])]    # sorted name list that match the 1-indexed list
    return {
        "thing_dataset_id_to_contiguous_id": thing_dataset_id_to_contiguous_id,
        "thing_classes": thing_classes}


_PREDEFINED_SPLITS_OID = {
    # cat threshold: 500, 1500: r 170, c 151, f 179
    "oid_train": ("oid/images/", "oid/annotations/oid_challenge_2019_train_bbox.json"),
    # "expanded" duplicates annotations to their father classes based on the official
    #   hierarchy. This is used in the official evaulation protocol.
    #   https://storage.googleapis.com/openimages/web/evaluation.html
    "oid_val_expanded": ("oid/images/validation/", "oid/annotations/oid_challenge_2019_val_expanded.json"),
    "oid_val_expanded_rare": ("oid/images/validation/", "oid/annotations/oid_challenge_2019_val_expanded_rare.json"),
}


def register_all_oid(root="datasets"):
    for key, (image_root, json_file) in _PREDEFINED_SPLITS_OID.items():
        register_oid_instances(
            key,  # dataset split name that matches the COMMAND LINE args
            _get_builtin_metadata(categories),  # label info. {'id': [...], 'ClassNames': [...]}
            os.path.join(root, json_file) if "://" not in json_file else json_file,  # COCO-styled annotation file
            os.path.join(root, image_root),  # dataset root
        )


# register the hierarchical datasets
_PREDEFINED_HRCHY_CATEGORIES = {
    "oid_val_expanded_l3": categories_l3,
    "oid_val_expanded_l4": categories_l4,
    "oid_val_expanded_l5": categories_l5,
}

_PREDEFINED_SPLITS_OID_HIERARCHY = {
    "oid_val_expanded_l3": ("oid/images/validation/", "oid/annotations/oid_challenge_2019_val_l3.json"),   # L3: 8
    "oid_val_expanded_l4": ("oid/images/validation/", "oid/annotations/oid_challenge_2019_val_l4.json"),   # L4: 46
    "oid_val_expanded_l5": ("oid/images/validation/", "oid/annotations/oid_challenge_2019_val_l5.json"),   # L5: 443
}


def register_all_oid_hierarchy(root="datasets"):
    for key, (image_root, json_file) in _PREDEFINED_SPLITS_OID_HIERARCHY.items():
        register_oid_instances(
            key,  # dataset split name that matches the COMMAND LINE args
            _get_builtin_metadata(_PREDEFINED_HRCHY_CATEGORIES[key]),
            # label info. {'id': [...], 'ClassNames': [...]}
            os.path.join(root, json_file) if "://" not in json_file else json_file,  # COCO-styled annotation file
            os.path.join(root, image_root),  # dataset root
        )


# if __name__.endswith(".builtin_oid"):
#     # Assume pre-defined datasets live in `./datasets`.
#     _root = "datasets"
#     register_all_oid(_root)
#     register_all_oid_hierarchy(_root)

