import os
from .registry_fsod import register_fsod_instances

# FSOD layer 1 (top)
# num_classes = 15
categories_l1 = [
    {'supersupercategory': '', 'supercategory': '', 'id': 1, 'name': 'liquid', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': '', 'id': 2, 'name': 'instrument', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': '', 'id': 3, 'name': 'food', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': '', 'id': 4, 'name': 'plant', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': '', 'id': 5, 'name': 'component', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': '', 'id': 6, 'name': 'equipment', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': '', 'id': 7, 'name': 'animal', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': '', 'id': 8, 'name': 'wearable item', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': '', 'id': 9, 'name': 'infrastructure', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': '', 'id': 10, 'name': 'art', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': '', 'id': 11, 'name': 'vehicle', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': '', 'id': 12, 'name': 'furnishing', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': '', 'id': 13, 'name': 'Fungi', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': '', 'id': 14, 'name': 'body', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': '', 'id': 15, 'name': 'beauty product', 'freebase_id': 'tbd'},
]


# FSOD layer 2
# num_classes = 46
categories_l2 = [
    {'supersupercategory': '', 'supercategory': 'liquid', 'id': 1, 'name': 'drink', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'instrument', 'id': 2, 'name': 'musical instrument', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'food', 'id': 3, 'name': 'vegetable', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'plant', 'id': 4, 'name': 'tree', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'component', 'id': 5, 'name': 'auto part', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'equipment', 'id': 6, 'name': 'sports equipment', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'plant', 'id': 7, 'name': 'flower', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'food', 'id': 8, 'name': 'baked goods', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'animal', 'id': 9, 'name': 'mammal', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'wearable item', 'id': 10, 'name': 'footwear', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'wearable item', 'id': 11, 'name': 'clothing', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'animal', 'id': 12, 'name': 'reptile', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'equipment', 'id': 13, 'name': 'office supplies', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'infrastructure', 'id': 14, 'name': 'lighting', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'infrastructure', 'id': 15, 'name': 'building', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'food', 'id': 16, 'name': 'fruit', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'food', 'id': 17, 'name': 'seafood', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'food', 'id': 18, 'name': 'dessert', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'equipment', 'id': 19, 'name': 'kitchen appliance', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'art', 'id': 20, 'name': 'sculpture', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'equipment', 'id': 21, 'name': 'kitchen utensil', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'vehicle', 'id': 22, 'name': 'land vehicle', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'furnishing', 'id': 23, 'name': 'furniture', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'animal', 'id': 24, 'name': 'shellfish', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'equipment', 'id': 25, 'name': 'home appliance', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'food', 'id': 26, 'name': 'animal product', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'equipment', 'id': 27, 'name': 'tool', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'wearable item', 'id': 28, 'name': 'fashion accessory', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'equipment', 'id': 29, 'name': 'electronic product', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'infrastructure', 'id': 30, 'name': 'container', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'equipment', 'id': 31, 'name': 'luggage and bags', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'equipment', 'id': 32, 'name': 'batheroom accessory', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'vehicle', 'id': 33, 'name': 'aircraft', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'instrument', 'id': 34, 'name': 'weapon', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'infrastructure', 'id': 35, 'name': 'facility', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'furnishing', 'id': 36, 'name': 'plumbing fixture', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'component', 'id': 37, 'name': 'door hardware', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'Fungi', 'id': 38, 'name': 'fungus', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'animal', 'id': 39, 'name': 'fish', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'equipment', 'id': 40, 'name': 'tableware', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'food', 'id': 41, 'name': 'nut', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'body', 'id': 42, 'name': 'human body part', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'beauty product', 'id': 43, 'name': 'cosmetics', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'vehicle', 'id': 44, 'name': 'watercraft', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'equipment', 'id': 45, 'name': 'kitchenware', 'freebase_id': 'tbd'},
    {'supersupercategory': '', 'supercategory': 'food', 'id': 46, 'name': 'fast food', 'freebase_id': 'tbd'},
]


# FSOD layer 3 (leaf)
# num_classes = 200
categories_l3 = [
    {'supercategory': 'drink', 'id': 1, 'name': 'beer', 'supersupercategory': 'liquid', 'freebase_id': 'tbd'},
    {'supercategory': 'musical instrument', 'id': 2, 'name': 'musical keyboard', 'supersupercategory': 'instrument', 'freebase_id': 'tbd'},
    {'supercategory': 'vegetable', 'id': 3, 'name': 'jalapeno', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'tree', 'id': 4, 'name': 'maple', 'supersupercategory': 'plant', 'freebase_id': 'tbd'},
    {'supercategory': 'auto part', 'id': 5, 'name': 'cartwheel', 'supersupercategory': 'component', 'freebase_id': 'tbd'},
    {'supercategory': 'tree', 'id': 6, 'name': 'christmas tree', 'supersupercategory': 'plant', 'freebase_id': 'tbd'},
    {'supercategory': 'sports equipment', 'id': 7, 'name': 'hiking equipment', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'sports equipment', 'id': 8, 'name': 'bicycle helmet', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'flower', 'id': 9, 'name': 'laelia', 'supersupercategory': 'plant', 'freebase_id': 'tbd'},
    {'supercategory': 'flower', 'id': 10, 'name': 'cattleya', 'supersupercategory': 'plant', 'freebase_id': 'tbd'},
    {'supercategory': 'baked goods', 'id': 11, 'name': 'bran muffin', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'sports equipment', 'id': 12, 'name': 'goggles', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'mammal', 'id': 13, 'name': 'caribou', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'footwear', 'id': 14, 'name': 'buskin', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'clothing', 'id': 15, 'name': 'turban', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'reptile', 'id': 16, 'name': 'tortoise', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'office supplies', 'id': 17, 'name': 'whiteboard', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'office supplies', 'id': 18, 'name': 'chalk', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'drink', 'id': 19, 'name': 'cider vinegar', 'supersupercategory': 'liquid', 'freebase_id': 'tbd'},
    {'supercategory': 'lighting', 'id': 20, 'name': 'lantern', 'supersupercategory': 'infrastructure', 'freebase_id': 'tbd'},
    {'supercategory': 'baked goods', 'id': 21, 'name': 'bannock', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'building', 'id': 22, 'name': 'convenience store', 'supersupercategory': 'infrastructure', 'freebase_id': 'tbd'},
    {'supercategory': 'fruit', 'id': 23, 'name': 'persimmon', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'sports equipment', 'id': 24, 'name': 'lifejacket', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'seafood', 'id': 25, 'name': 'squid', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'fruit', 'id': 26, 'name': 'watermelon', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'footwear', 'id': 27, 'name': 'wing tip', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'flower', 'id': 28, 'name': 'sunflower', 'supersupercategory': 'plant', 'freebase_id': 'tbd'},
    {'supercategory': 'sports equipment', 'id': 29, 'name': 'shin guard', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'footwear', 'id': 30, 'name': 'baby shoe', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'dessert', 'id': 31, 'name': 'muffin', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'kitchen appliance', 'id': 32, 'name': 'mixer', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'sculpture', 'id': 33, 'name': 'bronze sculpture', 'supersupercategory': 'art', 'freebase_id': 'tbd'},
    {'supercategory': 'musical instrument', 'id': 34, 'name': 'euphonium', 'supersupercategory': 'instrument', 'freebase_id': 'tbd'},
    {'supercategory': 'building', 'id': 35, 'name': 'skyscraper', 'supersupercategory': 'infrastructure', 'freebase_id': 'tbd'},
    {'supercategory': 'kitchen utensil', 'id': 36, 'name': 'drinking straw', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'baked goods', 'id': 37, 'name': 'popover', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'land vehicle', 'id': 38, 'name': 'segway', 'supersupercategory': 'vehicle', 'freebase_id': 'tbd'},
    {'supercategory': 'clothing', 'id': 39, 'name': 'sun hat', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'mammal', 'id': 40, 'name': 'harbor seal', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'furniture', 'id': 41, 'name': 'cat furniture', 'supersupercategory': 'furnishing', 'freebase_id': 'tbd'},
    {'supercategory': 'clothing', 'id': 42, 'name': 'fedora', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'kitchen utensil', 'id': 43, 'name': 'kitchen knife', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'auto part', 'id': 44, 'name': 'pulley', 'supersupercategory': 'component', 'freebase_id': 'tbd'},
    {'supercategory': 'footwear', 'id': 45, 'name': 'walking shoe', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'clothing', 'id': 46, 'name': 'fancy dress', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'shellfish', 'id': 47, 'name': 'clam', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'home appliance', 'id': 48, 'name': 'hand dryer', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'animal product', 'id': 49, 'name': 'mozzarella', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'mammal', 'id': 50, 'name': 'peccary', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'tool', 'id': 51, 'name': 'spinning rod', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'building', 'id': 52, 'name': 'tree house', 'supersupercategory': 'infrastructure', 'freebase_id': 'tbd'},
    {'supercategory': 'clothing', 'id': 53, 'name': 'khimar', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'fashion accessory', 'id': 54, 'name': 'earrings', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'electronic product', 'id': 55, 'name': 'power plugs and sockets', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'container', 'id': 56, 'name': 'waste container', 'supersupercategory': 'infrastructure', 'freebase_id': 'tbd'},
    {'supercategory': 'kitchen appliance', 'id': 57, 'name': 'blender', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'luggage and bags', 'id': 58, 'name': 'briefcase', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'batheroom accessory', 'id': 59, 'name': 'soap dish', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'aircraft', 'id': 60, 'name': 'hot air balloon', 'supersupercategory': 'vehicle', 'freebase_id': 'tbd'},
    {'supercategory': 'auto part', 'id': 61, 'name': 'windmill', 'supersupercategory': 'component', 'freebase_id': 'tbd'},
    {'supercategory': 'lighting', 'id': 62, 'name': 'street light', 'supersupercategory': 'infrastructure', 'freebase_id': 'tbd'},
    {'supercategory': 'weapon', 'id': 63, 'name': 'shotgun', 'supersupercategory': 'instrument', 'freebase_id': 'tbd'},
    {'supercategory': 'clothing', 'id': 64, 'name': 'sports uniform', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'tool', 'id': 65, 'name': 'manometer', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'home appliance', 'id': 66, 'name': 'wood burning stove', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'mammal', 'id': 67, 'name': 'gnu', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'electronic product', 'id': 68, 'name': 'earphone', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'building', 'id': 69, 'name': 'double hung window', 'supersupercategory': 'infrastructure', 'freebase_id': 'tbd'},
    {'supercategory': 'facility', 'id': 70, 'name': 'billboard', 'supersupercategory': 'infrastructure', 'freebase_id': 'tbd'},
    {'supercategory': 'dessert', 'id': 71, 'name': 'conserve', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'weapon', 'id': 72, 'name': 'claymore', 'supersupercategory': 'instrument', 'freebase_id': 'tbd'},
    {'supercategory': 'auto part', 'id': 73, 'name': 'vehicle registration plate', 'supersupercategory': 'component', 'freebase_id': 'tbd'},
    {'supercategory': 'home appliance', 'id': 74, 'name': 'ceiling fan', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'electronic product', 'id': 75, 'name': 'cassette deck', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'sports equipment', 'id': 76, 'name': 'table tennis racket', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'baked goods', 'id': 77, 'name': 'scone', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'fashion accessory', 'id': 78, 'name': 'bouquet', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'plumbing fixture', 'id': 79, 'name': 'bidet', 'supersupercategory': 'furnishing', 'freebase_id': 'tbd'},
    {'supercategory': 'footwear', 'id': 80, 'name': 'ski boot', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'sports equipment', 'id': 81, 'name': 'pumpkin', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'flower', 'id': 82, 'name': 'welsh poppy', 'supersupercategory': 'plant', 'freebase_id': 'tbd'},
    {'supercategory': 'electronic product', 'id': 83, 'name': 'tablet computer', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'mammal', 'id': 84, 'name': 'rhinoceros', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'animal product', 'id': 85, 'name': 'cheese', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'facility', 'id': 86, 'name': 'jacuzzi', 'supersupercategory': 'infrastructure', 'freebase_id': 'tbd'},
    {'supercategory': 'door hardware', 'id': 87, 'name': 'door handle', 'supersupercategory': 'component', 'freebase_id': 'tbd'},
    {'supercategory': 'fungus', 'id': 88, 'name': 'puffball', 'supersupercategory': 'Fungi', 'freebase_id': 'tbd'},
    {'supercategory': 'facility', 'id': 89, 'name': 'swimming pool', 'supersupercategory': 'infrastructure', 'freebase_id': 'tbd'},
    {'supercategory': 'fish', 'id': 90, 'name': 'rays and skates', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'kitchen utensil', 'id': 91, 'name': 'chopsticks', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'shellfish', 'id': 92, 'name': 'oyster', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'building', 'id': 93, 'name': 'office building', 'supersupercategory': 'infrastructure', 'freebase_id': 'tbd'},
    {'supercategory': 'tool', 'id': 94, 'name': 'ratchet', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'drink', 'id': 95, 'name': 'sambuca', 'supersupercategory': 'liquid', 'freebase_id': 'tbd'},
    {'supercategory': 'fungus', 'id': 96, 'name': 'truffle', 'supersupercategory': 'Fungi', 'freebase_id': 'tbd'},
    {'supercategory': 'tableware', 'id': 97, 'name': 'salt and pepper shakers', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'flower', 'id': 98, 'name': 'calla lily', 'supersupercategory': 'plant', 'freebase_id': 'tbd'},
    {'supercategory': 'clothing', 'id': 99, 'name': 'hard hat', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'mammal', 'id': 100, 'name': 'elephant seal', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'nut', 'id': 101, 'name': 'peanut', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'mammal', 'id': 102, 'name': 'hind', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'fungus', 'id': 103, 'name': 'jelly fungus', 'supersupercategory': 'Fungi', 'freebase_id': 'tbd'},
    {'supercategory': 'drink', 'id': 104, 'name': 'juice', 'supersupercategory': 'liquid', 'freebase_id': 'tbd'},
    {'supercategory': 'baked goods', 'id': 105, 'name': 'pirogi', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'sports equipment', 'id': 106, 'name': 'bowling equipment', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'container', 'id': 107, 'name': 'recycling bin', 'supersupercategory': 'infrastructure', 'freebase_id': 'tbd'},
    {'supercategory': 'human body part', 'id': 108, 'name': 'skull', 'supersupercategory': 'body', 'freebase_id': 'tbd'},
    {'supercategory': 'furniture', 'id': 109, 'name': 'nightstand', 'supersupercategory': 'furnishing', 'freebase_id': 'tbd'},
    {'supercategory': 'lighting', 'id': 110, 'name': 'light bulb', 'supersupercategory': 'infrastructure', 'freebase_id': 'tbd'},
    {'supercategory': 'footwear', 'id': 111, 'name': 'high heels', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'container', 'id': 112, 'name': 'picnic basket', 'supersupercategory': 'infrastructure', 'freebase_id': 'tbd'},
    {'supercategory': 'footwear', 'id': 113, 'name': 'in line skate', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'tableware', 'id': 114, 'name': 'platter', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'baked goods', 'id': 115, 'name': 'bialy', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'tool', 'id': 116, 'name': 'shelf bracket', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'fruit', 'id': 117, 'name': 'cantaloupe', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'baked goods', 'id': 118, 'name': 'croissant', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'footwear', 'id': 119, 'name': 'bowling shoe', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'facility', 'id': 120, 'name': 'ferris wheel', 'supersupercategory': 'infrastructure', 'freebase_id': 'tbd'},
    {'supercategory': 'reptile', 'id': 121, 'name': 'dinosaur', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'office supplies', 'id': 122, 'name': 'adhesive tape', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'flower', 'id': 123, 'name': 'stanhopea', 'supersupercategory': 'plant', 'freebase_id': 'tbd'},
    {'supercategory': 'home appliance', 'id': 124, 'name': 'mechanical fan', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'vegetable', 'id': 125, 'name': 'winter melon', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'shellfish', 'id': 126, 'name': 'cowrie', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'tool', 'id': 127, 'name': 'adjustable wrench', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'baked goods', 'id': 128, 'name': 'date bread', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'tool', 'id': 129, 'name': 'o ring', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'sculpture', 'id': 130, 'name': 'caryatid', 'supersupercategory': 'art', 'freebase_id': 'tbd'},
    {'supercategory': 'animal product', 'id': 131, 'name': 'egg', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'container', 'id': 132, 'name': 'beehive', 'supersupercategory': 'infrastructure', 'freebase_id': 'tbd'},
    {'supercategory': 'flower', 'id': 133, 'name': 'lily', 'supersupercategory': 'plant', 'freebase_id': 'tbd'},
    {'supercategory': 'weapon', 'id': 134, 'name': 'leaf spring', 'supersupercategory': 'instrument', 'freebase_id': 'tbd'},
    {'supercategory': 'baked goods', 'id': 135, 'name': 'french bread', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'tableware', 'id': 136, 'name': 'cake stand', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'fish', 'id': 137, 'name': 'sergeant major', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'sports equipment', 'id': 138, 'name': 'treadmill', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'drink', 'id': 139, 'name': 'daiquiri', 'supersupercategory': 'liquid', 'freebase_id': 'tbd'},
    {'supercategory': 'baked goods', 'id': 140, 'name': 'sweet roll', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'fungus', 'id': 141, 'name': 'polypore', 'supersupercategory': 'Fungi', 'freebase_id': 'tbd'},
    {'supercategory': 'fashion accessory', 'id': 142, 'name': 'face veil', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'furniture', 'id': 143, 'name': 'kitchen & dining room table', 'supersupercategory': 'furnishing', 'freebase_id': 'tbd'},
    {'supercategory': 'clothing', 'id': 144, 'name': 'support hose', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'electronic product', 'id': 145, 'name': 'headphones', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'lighting', 'id': 146, 'name': 'chinese lantern', 'supersupercategory': 'infrastructure', 'freebase_id': 'tbd'},
    {'supercategory': 'furniture', 'id': 147, 'name': 'wine rack', 'supersupercategory': 'furnishing', 'freebase_id': 'tbd'},
    {'supercategory': 'musical instrument', 'id': 148, 'name': 'triangle', 'supersupercategory': 'instrument', 'freebase_id': 'tbd'},
    {'supercategory': 'fruit', 'id': 149, 'name': 'mulberry', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'baked goods', 'id': 150, 'name': 'quick bread', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'musical instrument', 'id': 151, 'name': 'harpsichord', 'supersupercategory': 'instrument', 'freebase_id': 'tbd'},
    {'supercategory': 'electronic product', 'id': 152, 'name': 'optical disk', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'animal product', 'id': 153, 'name': 'egg yolk', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'vegetable', 'id': 154, 'name': 'shallot', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'flower', 'id': 155, 'name': 'strawflower', 'supersupercategory': 'plant', 'freebase_id': 'tbd'},
    {'supercategory': 'sports equipment', 'id': 156, 'name': 'cue', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'electronic product', 'id': 157, 'name': 'corded phone', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'flower', 'id': 158, 'name': 'blue columbine', 'supersupercategory': 'plant', 'freebase_id': 'tbd'},
    {'supercategory': 'building', 'id': 159, 'name': 'silo', 'supersupercategory': 'infrastructure', 'freebase_id': 'tbd'},
    {'supercategory': 'cosmetics', 'id': 160, 'name': 'mascara', 'supersupercategory': 'beauty product', 'freebase_id': 'tbd'},
    {'supercategory': 'sculpture', 'id': 161, 'name': 'snowman', 'supersupercategory': 'art', 'freebase_id': 'tbd'},
    {'supercategory': 'vegetable', 'id': 162, 'name': 'cherry tomato', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'tool', 'id': 163, 'name': 'box wrench', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'footwear', 'id': 164, 'name': 'flipper', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'watercraft', 'id': 165, 'name': 'jet ski', 'supersupercategory': 'vehicle', 'freebase_id': 'tbd'},
    {'supercategory': 'clothing', 'id': 166, 'name': 'bathrobe', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'facility', 'id': 167, 'name': 'fireplace', 'supersupercategory': 'infrastructure', 'freebase_id': 'tbd'},
    {'supercategory': 'fungus', 'id': 168, 'name': 'gill fungus', 'supersupercategory': 'Fungi', 'freebase_id': 'tbd'},
    {'supercategory': 'office supplies', 'id': 169, 'name': 'blackboard', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'tool', 'id': 170, 'name': 'thumbtack', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'kitchenware', 'id': 171, 'name': 'spice rack', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'mammal', 'id': 172, 'name': 'longhorn', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'mammal', 'id': 173, 'name': 'pacific walrus', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'flower', 'id': 174, 'name': 'streptocarpus', 'supersupercategory': 'plant', 'freebase_id': 'tbd'},
    {'supercategory': 'fruit', 'id': 175, 'name': 'coconut', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'mammal', 'id': 176, 'name': 'addax', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'kitchen appliance', 'id': 177, 'name': 'coffeemaker', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'flower', 'id': 178, 'name': 'fly orchid', 'supersupercategory': 'plant', 'freebase_id': 'tbd'},
    {'supercategory': 'fruit', 'id': 179, 'name': 'blackberry', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'mammal', 'id': 180, 'name': 'kob', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'auto part', 'id': 181, 'name': 'car tire', 'supersupercategory': 'component', 'freebase_id': 'tbd'},
    {'supercategory': 'fish', 'id': 182, 'name': 'seahorse', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'fashion accessory', 'id': 183, 'name': 'tiara', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'mammal', 'id': 184, 'name': 'sassaby', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'tool', 'id': 185, 'name': 'fishing rod', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'baked goods', 'id': 186, 'name': 'baguet', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'tool', 'id': 187, 'name': 'trowel', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'electronic product', 'id': 188, 'name': 'light switch', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'baked goods', 'id': 189, 'name': 'cornbread', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'flower', 'id': 190, 'name': 'disa', 'supersupercategory': 'plant', 'freebase_id': 'tbd'},
    {'supercategory': 'tableware', 'id': 191, 'name': 'serving tray', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
    {'supercategory': 'musical instrument', 'id': 192, 'name': 'tuning fork', 'supersupercategory': 'instrument', 'freebase_id': 'tbd'},
    {'supercategory': 'flower', 'id': 193, 'name': 'virginia spring beauty', 'supersupercategory': 'plant', 'freebase_id': 'tbd'},
    {'supercategory': 'fast food', 'id': 194, 'name': 'samosa', 'supersupercategory': 'food', 'freebase_id': 'tbd'},
    {'supercategory': 'furniture', 'id': 195, 'name': 'bathroom cabinet', 'supersupercategory': 'furnishing', 'freebase_id': 'tbd'},
    {'supercategory': 'mammal', 'id': 196, 'name': 'chigetai', 'supersupercategory': 'animal', 'freebase_id': 'tbd'},
    {'supercategory': 'flower', 'id': 197, 'name': 'blue poppy', 'supersupercategory': 'plant', 'freebase_id': 'tbd'},
    {'supercategory': 'weapon', 'id': 198, 'name': 'scimitar', 'supersupercategory': 'instrument', 'freebase_id': 'tbd'},
    {'supercategory': 'clothing', 'id': 199, 'name': 'shirt button', 'supersupercategory': 'wearable item', 'freebase_id': 'tbd'},
    {'supercategory': 'kitchen appliance', 'id': 200, 'name': 'slow cooker', 'supersupercategory': 'equipment', 'freebase_id': 'tbd'},
]

# register the hierarchical datasets
_PREDEFINED_FSOD_HRCHY_CATEGORIES = {
    "fsod_test_l1": categories_l1,  # num_classes = 15
    "fsod_test_l2": categories_l2,  # num_classes = 46
    "fsod_test_l3": categories_l3,  # num_classes = 200
}

_PREDEFINED_SPLITS_FSOD_HIERARCHY = {
    # |- test set
    "fsod_test_l1": ("fsod/images/", "fsod/annotations/fsod_test_fixed_l1.json"),
    "fsod_test_l2": ("fsod/images/", "fsod/annotations/fsod_test_fixed_l2.json"),
    "fsod_test_l3": ("fsod/images/", "fsod/annotations/fsod_test_fixed_l3.json"),

}


def _get_builtin_metadata(cats):
    id_to_name = {x['id']: x['name'] for x in cats}
    thing_dataset_id_to_contiguous_id = {i + 1: i for i in range(len(cats))}    # convert to 1-indexed
    thing_classes = [x['name'] for x in sorted(cats, key=lambda x: x['id'])]    # sorted name list that match the 1-indexed list
    return {
        "thing_dataset_id_to_contiguous_id": thing_dataset_id_to_contiguous_id,
        "thing_classes": thing_classes}


def register_all_fsod_hierarchy(root="datasets"):
    for key, (image_root, json_file) in _PREDEFINED_SPLITS_FSOD_HIERARCHY.items():
        register_fsod_instances(
            name=key,
            metadata=_get_builtin_metadata(_PREDEFINED_FSOD_HRCHY_CATEGORIES[key]),
            json_file=os.path.join(root, json_file) if "://" not in json_file else json_file,
            image_root=os.path.join(root, image_root),  # dataset root
        )